# QSS Variables: Tavily Search Findings

Date: 2025-08-05  
Purpose: Determine whether double-curly template syntax {{...}} is native to Qt Style Sheets (QSS), and summarize viable approaches to variables in QSS.

Conclusion (TL;DR)
- Qt Style Sheets (QSS) do NOT natively support {{...}} placeholders. Double-curly tokens are a preprocessing/workaround pattern, not part of QSS.
- QSS also does NOT natively support CSS var(--token) semantics.
- Common solutions use preprocessing or runtime substitution:
  - Custom loaders that add “CSS variables” and @import behavior
  - External template engines or Sass-like preprocessors
  - App-level string replacement prior to app.setStyleSheet()

Authoritative/Relevant Sources

1) Qt Official Docs: Style Sheet Syntax
- Source: https://doc.qt.io/qt-6/stylesheet-syntax.html
- Key points:
  - Documents selectors, pseudo-states, subcontrols, qproperty syntax, specificity, inheritance rules.
  - No mention of native variable placeholders like {{...}} or CSS var().
  - Confirms QSS is a CSS-like subset/superset tailored for Qt, without CSS variable semantics.

2) Using variables in Qt StyleSheets (Stack Overflow)
- Source: https://stackoverflow.com/questions/10898399/using-variables-in-qt-stylesheets
- Summary:
  - Common guidance is to preprocess stylesheets (e.g., Sass) or do string replacement before applying.
  - Variables and advanced composition are typically achieved outside of QSS itself.

3) GitHub: xamgu-QSS-loader (adds CSS variables and imports)
- Source: https://github.com/mrkkrj/xamgu-QSS-loader
- Summary:
  - A custom loader that augments QSS with CSS-like variables and @import behavior.
  - Confirms that native QSS lacks these features; tooling adds them in a preprocessing step.

4) GitHub: QssStylesheetEditor (user-defined variables)
- Source: https://github.com/hustlei/QssStylesheetEditor
- Summary:
  - Editor provides a mechanism for user-defined variables and real-time preview.
  - Again, this is an external tool approach; variables are an editor/loader feature, not QSS native.

Additional References
- Templates and collections (non-authoritative but illustrative):
  - https://github.com/xakod/qt-stylesheets
  - https://github.com/GTRONICK/QSS

Implications for Our Project

1) Our {{...}} placeholders are a deliberate, supported workaround
- Matches industry practice: template tokens (e.g., {{FONT_SIZE}}) replaced at runtime (or during load) before setStyleSheet().
- Our current pipeline (see [__init__.py](flatmate/src/fm/gui/styles/__init__.py:1)) performs explicit replacement for {{FONT_SIZE}} and should continue to do so.

2) Keeping {{FONT_SIZE}} and adopting {{FONT}} is consistent with best practice
- These are application-level variables, not QSS variables. The app owns the replacement step.
- This keeps behavior deterministic and independent of Qt version quirks.

3) CSS var(--token) should be treated as experimental only
- Not supported by QSS per docs; any success would be incidental or tool-augmented.
- If investigated, do so in an experiment harness; do not base core theming on it.

Recommended Practices

- Replacement step: Ensure replacements occur prior to app.setStyleSheet() and log counts (e.g., how many {{FONT_SIZE}} tokens replaced).
- Token contract: Maintain a documented list of supported tokens (e.g., {{FONT_SIZE}}, {{FONT}}) in a styles README.
- Safety checks: Fail or warn if expected tokens remain unreplaced, to avoid shipping dangling placeholders.
- Experiments: Validate any CSS var() approach in a separate harness; keep core path on explicit string replacement.

Cross-Links
- Runtime variable usage analysis: [VARIABLES_Usage_Investigation.md](flatmate/DOCS/_ARCHITECTURE/STYLING_SYSTEM/VARIABLES_Usage_Investigation.md:1)
- Brownfield plan: [BROWNFIELD_Styling_System_Architecture.md](flatmate/DOCS/_ARCHITECTURE/STYLING_SYSTEM/BROWNFIELD_Styling_System_Architecture.md:1)
- Consolidated stylesheet: [flatmate_consolidated.qss](flatmate/src/fm/gui/styles/flatmate_consolidated.qss:1)
- Replacement logic (current): [__init__.py](flatmate/src/fm/gui/styles/__init__.py:1)

Summary
Double-curly placeholders are not native to QSS. They are a reliable app-controlled templating mechanism. Our current approach of replacing {{FONT_SIZE}} (and the proposal to add {{FONT}}) is aligned with common practice and should remain the supported path. Any CSS var() exploration should be isolated and not block or complicate the primary, deterministic styling pipeline.