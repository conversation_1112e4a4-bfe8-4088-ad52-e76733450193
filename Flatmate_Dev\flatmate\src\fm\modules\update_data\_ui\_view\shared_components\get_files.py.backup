import os
import sys
from pathlib import Path
from typing import List, Optional, Set

from PySide6.QtWidgets import QFileDialog
from ....config.ud_keys import UpdateDataKeys as ud_keys
from ....config.ud_config import ud_config as config
from fm.core.services.logger import log

# Default directory if no last used directory is available
default_dir = os.path.expanduser('~')


def get_file_paths(method: str, title: str = "Select Source", parent=None, start_dir: Optional[str] = None) -> List[str]:
    """
    Convenience function to get file paths using a dialog.

    Args:
        method: 'select_files' or 'select_folder'.
        title: The title for the dialog window.
        parent: The parent widget for the dialog.
        start_dir: An optional directory to start in.

    Returns:
        A list of selected file paths.
    """
    log.debug(f"File selection requested: method={method}, title={title}")
    
    if method == 'select_files':
        dialog = SelectFileDialogue(title=title, parent=parent, start_dir=start_dir)
        return dialog.selected_files
    
    elif method == 'select_folder':
        dialog = SelectFolderDialogue(title=title, parent=parent, start_dir=start_dir)
        folder_path = dialog.selected_folder
        if folder_path:
            return FileUtils.discover_files_in_folder(folder_path)
        return []
        
    else:
        raise ValueError(f"Invalid method '{method}'. Use 'select_files' or 'select_folder'.")


class SelectFolderDialogue:
    """Dialog for selecting a folder with platform-specific handling."""
    
    def __init__(self, title: str = "Select Folder", parent=None, start_dir: Optional[str] = None):
        """Initialize the folder selection dialog.
        
        Args:
            title: Dialog window title
            parent: Parent widget
            start_dir: Starting directory (uses last used dir if None)
        """
        self.selected_folder = ""
        initial_dir = start_dir or FileUtils.get_last_used_dir()

        if sys.platform == 'win32':
            # On Windows, the native folder dialog is poor. Use the file dialog workaround.
            instructive_title = f"{title} (select any file in the desired folder)"
            dialog = SelectFileDialogue(title=instructive_title, parent=parent, start_dir=initial_dir)
            if dialog.selected_files:
                self.selected_folder = os.path.dirname(dialog.selected_files[0])
        else:
            # On macOS and Linux, use the native folder dialog.
            folder = QFileDialog.getExistingDirectory(
                parent=parent,
                caption=title,
                dir=initial_dir
            )
            self.selected_folder = folder

        if self.selected_folder:
            FileUtils.set_last_used_dir(self.selected_folder)
            log.debug(f"Selected folder: {self.selected_folder}")


class SelectFileDialogue:
    """Dialog for selecting one or more files."""
    
    def __init__(self, title: str = "Select File(s)", parent=None, start_dir: Optional[str] = None):
        """Initialize the file selection dialog.
        
        Args:
            title: Dialog window title
            parent: Parent widget
            start_dir: Starting directory (uses last used dir if None)
        """
        initial_dir = start_dir or FileUtils.get_last_used_dir()
        supported_types = FileUtils._get_supported_file_types()
        filter_string = f"Supported Files ({' '.join(['*.' + ext for ext in supported_types])});;All Files (*)"

        files, _ = QFileDialog.getOpenFileNames(
            parent=parent,
            caption=title,
            dir=initial_dir,
            filter=filter_string
        )

        if files:
            # Save the directory of the first selected file
            FileUtils.set_last_used_dir(os.path.dirname(files[0]))
        
        self.selected_files = files
        if files:
            log.debug(f"Selected {len(files)} file(s): {files[0]}{' and more' if len(files) > 1 else ''}")

class FileUtils:
    """Utility class for file operations and configuration."""
    
    @staticmethod
    def _normalise_extension(filename: str) -> str:
        """Return the file extension in lower case, without the leading dot."""
        return filename.rsplit('.', 1)[-1].lower() if '.' in filename else ''
    
    @staticmethod
    def _get_supported_file_types(file_types: List[str] = ('csv', 'ofx', 'pdf')) -> List[str]:
        """Return supported file types from config, or fallback to default."""
        return config.get_value(ud_keys.Files.SUPPORTED, default=file_types)

    @staticmethod
    def get_last_used_dir() -> str:
        """Return the last used directory from config, or the user's home dir."""
        return config.get_value(ud_keys.Paths.LAST_SOURCE_DIR, default=default_dir)

    @staticmethod
    def set_last_used_dir(directory: str) -> None:
        """Save the last used directory to config."""
        config.set_value(ud_keys.Paths.LAST_SOURCE_DIR, directory)

    @staticmethod
    def discover_files_in_folder(folder_path: str) -> List[str]:
        """Discover supported files in the selected folder.
        
        Args:
            folder_path: Path to the folder to scan for files
            
        Returns:
            List of paths to supported files found in the folder
        """
        if not folder_path or not os.path.isdir(folder_path):
            log.debug(f"Invalid folder path: {folder_path}")
            return []

        folder = Path(folder_path)
        # Use a set for efficient O(1) average time complexity lookups
        supported_exts_set: Set[str] = set(ext.lstrip('.') for ext in FileUtils._get_supported_file_types())

        discovered_files = [
            str(file_path) 
            for file_path in folder.iterdir() 
            if file_path.is_file() and FileUtils._normalise_extension(file_path.name) in supported_exts_set
        ]
        
        log.debug(f"Discovered {len(discovered_files)} supported files in {folder_path}")
        return discovered_files
