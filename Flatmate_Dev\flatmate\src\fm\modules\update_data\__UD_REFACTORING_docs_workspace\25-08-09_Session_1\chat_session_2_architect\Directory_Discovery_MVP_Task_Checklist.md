# Directory Discovery MVP – Task Checklist

Date: 2025-08-09
Owner: Update Data (Architect: <PERSON>)

## Completed
- [x] Audit existing discovery/scanning logic in UI (`file_selector.py`) and avoid duplication
- [x] Create core utils package `fm/core/directory/utils/`
- [x] Implement `extensions.py` with `normalise_ext()` and `parse_supported_extensions()`
- [x] Implement `file_scan.py` with `scan_root_files()` (root-only, non-recursive)
- [x] Refactor UI `file_selector.py` to delegate to core utils (scan + extensions)
- [x] Wire presenter path to persist discovery metadata
  - [x] Integrate `DirectoryInfoService` in `file_config_manager.py`
  - [x] On folder select: resolve archive destination, `enable_discovery()`, `mark_scanned_now()`

## In Progress / To Do
- [ ] Presenter: optional explicit “Enable file discovery for this folder” toggle
  - [ ] View event + handler to call `enable_discovery`/`disable_discovery`
  - [ ] Persist per-folder preference (MVP registry only)
- [ ] Audit for other duplicate scanning/extension utilities and route to core utils
  - [ ] Search `update_data` module utilities (e.g., any ad-hoc scans)
  - [ ] Replace with `scan_root_files()` and `parse_supported_extensions()`
- [ ] End-to-end test of golden path
  - [ ] Select folder -> files appear via FileInfoManager enrichment
  - [ ] Archive summary reflects Save option (Same as Source vs Select Location)
  - [ ] Directory registry shows discovery enabled + last scan timestamp
- [ ] Documentation
  - [ ] Update Architecture Plan with final wiring notes (presenter integration summary)
  - [ ] Note single source of truth for supported extensions (`ud_keys.Files.SUPPORTED`)
- [ ] Tech debt (post-MVP)
  - [ ] Persistence hooks for `DirectoryInfoService` (config/db)
  - [ ] Potential rename `FileInfoService` -> `FileMetadataService` (clarify role)
  - [ ] Multi-folder support + live monitoring integration

## Notes
- Presenter remains Qt-decoupled; uses view interface and services only.
- Discovery = where files come from; Enrichment = what files are (handler/bank/format).
- Archive destination rule (MVP): Same as Source -> source folder; otherwise use selected save location if set.
