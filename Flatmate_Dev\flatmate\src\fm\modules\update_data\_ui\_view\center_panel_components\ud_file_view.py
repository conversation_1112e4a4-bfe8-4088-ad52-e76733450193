"""
Main file view component for the Update Data module.

This is the smart widget that encapsulates all file-related functionality
following the clean MVP pattern with zero Qt coupling in the presenter.
"""

from PySide6.QtWidgets import QVBoxLayout, QFileDialog
from PySide6.QtCore import Signal, QPoint
from PySide6.QtGui import QContextMenuEvent
from typing import List, Optional, Dict, Any
import os
from pathlib import Path

from fm.gui._shared_components.base.base_pane import BasePane
from .file_pane_v2.models import FileViewModel, FileInfo
from .file_pane_v2.config import FileConfig
from .file_pane_v2.utils import create_file_info, validate_file_paths
from .file_pane_v2.widgets.file_tree import FileTree
from .file_pane_v2.widgets.add_remove_btns import AddRemoveButtons
from .file_pane_v2.widgets.context_menu import FileContextMenu
from fm.core.services.logger import log
# Removed direct FileSelector usage to align with event-first architecture


class UDFileView(BasePane):
    """Self-contained file display component with smart widget pattern."""
    
    # High-level domain signals (not Qt widget signals)
    file_list_changed = Signal(list)  # List[str] of file paths
    file_selected = Signal(str)       # Selected file path
    processing_requested = Signal()   # User wants to process files
    # New: raise intent to add files, to be translated by UpdateDataView into Local Event Bus
    add_files_requested = Signal()
    
    def __init__(self, config: Optional[FileConfig] = None, parent=None):
        """Initialize the file view component."""
        super().__init__(parent)
        
        # Initialize models and configuration
        self._model = FileViewModel()
        self._config = config or FileConfig.default()
        
        # Initialize components
        self._file_tree: Optional[FileTree] = None
        self._add_remove_btns: Optional[AddRemoveButtons] = None
        self._context_menu: Optional[FileContextMenu] = None
        
        # Set up the component
        self._init_layout()
        self._setup_ui()
        self._connect_signals()
        
        log.debug("UDFileView component initialized")
    
    def _init_layout(self) -> None:
        """Initialize the layout."""
        self._layout = QVBoxLayout(self)
        # Match legacy widget: tight, zero margins and compact spacing
        # file_browser.py used: layout.setContentsMargins(0,0,0,0) and layout.setSpacing(2)
        self._layout.setContentsMargins(0, 0, 0, 0)
        self._layout.setSpacing(2)
    
    def _setup_ui(self) -> None:
        """Set up the user interface components."""
        # Create file tree (refactored from FileTree)
        
        self._file_tree = FileTree(self._model, self._config, self)
        # Ensure QSS hooks match stylesheet selectors
        try:
            if not self._file_tree.objectName():
                self._file_tree.setObjectName("file_tree")
        except Exception:
            pass

        # Apply legacy container sizing hints on the tree
        try:
            from PySide6.QtWidgets import QSizePolicy, QTreeWidget
            # Fix: Use proper QSizePolicy enum values
            self._file_tree.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
            self._file_tree.setMinimumHeight(100)
            # Match legacy: allow content-driven width adjustments
            self._file_tree.setSizeAdjustPolicy(QTreeWidget.SizeAdjustPolicy.AdjustToContents)
        except Exception as e:
            log.debug(f"[UDFileView] Non-critical: sizing hints failed: {e}")

        # Ensure alternating rows for dark theme styling (picked up by theme.qss #file_tree)
        self._file_tree.setAlternatingRowColors(True)

        # Add tree to main layout with stretch factor similar to legacy
        self._layout.addWidget(self._file_tree, 1)

        # Footer: simple button container for file actions
        from PySide6.QtWidgets import QWidget, QHBoxLayout, QPushButton, QWidget as _QW, QSizePolicy as _QSizePolicy
        btn_container = QWidget(self)
        btn_container.setObjectName("file_view_buttons")  # Simple container for file buttons
        h = QHBoxLayout(btn_container)
        h.setContentsMargins(0, 2, 0, 0)  # tight top margin like legacy
        h.setSpacing(6)

        # left stretch to push buttons to the right
        spacer = _QW(btn_container)
        spacer.setSizePolicy(_QSizePolicy.Expanding, _QSizePolicy.Preferred)
        h.addWidget(spacer)

        # Add Files button (use existing select_btn style)
        self._add_button = QPushButton("Add Files", btn_container)
        self._add_button.setObjectName("add_files_btn")
        self._add_button.setProperty("type", "select_btn")
        self._add_button.setMaximumHeight(26)

        # Remove Selected button (use existing select_btn style)
        self._remove_button = QPushButton("Remove Selected", btn_container)
        self._remove_button.setObjectName("remove_files_btn")
        self._remove_button.setProperty("type", "select_btn")
        self._remove_button.setMaximumHeight(26)

        # Ensure QSS picks up property changes
        try:
            self._add_button.style().unpolish(self._add_button); self._add_button.style().polish(self._add_button)
            self._remove_button.style().unpolish(self._remove_button); self._remove_button.style().polish(self._remove_button)
        except Exception:
            pass

        # Wire buttons
        self._add_button.clicked.connect(self._on_add_files_requested)
        self._remove_button.clicked.connect(self._on_remove_file_requested)

        # Add to layout
        h.addWidget(self._add_button)
        h.addWidget(self._remove_button)
        self._layout.addWidget(btn_container)

        # Create context menu handler
        self._context_menu = FileContextMenu(self._config, self)
        
        # Enable context menu on tree
        if self._config.allow_context_menu:
            from PySide6.QtCore import Qt
            self._file_tree.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
            self._file_tree.customContextMenuRequested.connect(self._show_context_menu)

        # Ensure pane object name for potential scoped styling
        try:
            self.setObjectName("FileTreePane")
        except Exception as e:
            log.debug(f"[UDFileView] Non-critical: cannot set pane object name: {e}")
    
    def _connect_signals(self) -> None:
        """Connect internal component signals."""
        if self._file_tree:
            self._file_tree.file_selected.connect(self._on_file_selected)
            self._file_tree.file_double_clicked.connect(self._on_file_double_clicked)
        
        # Footer buttons wiring already connected in _setup_ui via _on_add_files_requested/_on_remove_file_requested
        
        if self._context_menu:
            self._context_menu.remove_file_requested.connect(self._remove_file_by_path)
            self._context_menu.open_file_location_requested.connect(self._open_file_location)
            self._context_menu.file_properties_requested.connect(self._show_file_properties)
    
    # Public API methods (interface implementation)
    def set_files(self, file_paths: List[str]) -> 'UDFileView':
        """Set the list of files. Returns self for method chaining.

        Phase 3 Task A: deterministic refresh on dataset changes
        - Ensures the table row count matches the dataset size post-update
        - Clears selection on data rebind (Task B baseline), optional future restore-by-id
        """
        try:
            log.debug(f"[UDFileView] Setting {len(file_paths)} files (legacy path)")

            # Validate and normalize input first to avoid flicker from partial updates
            valid_paths = validate_file_paths(file_paths, self._config)

            # Compute old/new identity sets for selection handling
            old_paths = set(self._model.get_file_paths())
            new_paths = set(valid_paths)

            # Rebind data deterministically
            self._model.clear_files()
            for file_path in valid_paths:
                file_info = create_file_info(file_path)
                if file_info:
                    self._model.add_file(file_info)

            # Clear selection if identity set changed (removed items or general rebind)
            if old_paths != new_paths:
                self._clear_selection()

            # Single refresh pass
            self._refresh_ui()

            # Emit signal after UI reflects new state
            self.file_list_changed.emit(self._model.get_file_paths())

            # Row count invariant (debug log)
            # Optional invariant logging using FileTree helper if available
            try:
                row_counter = getattr(self._file_tree, "row_count", None)
                if callable(row_counter):
                    table_rows = row_counter()
                    log.debug(f"[UDFileView] Row count after rebind -> table: {table_rows}, model: {len(valid_paths)}")
            except Exception:
                pass

            log.debug(f"Set {len(valid_paths)} files in file view")

        except Exception as e:
            log.error(f"Error setting files: {e}")

        return self

    def set_enriched_files(self, file_info_list: List) -> 'UDFileView':
        """Set files using enriched FileInfoData objects directly - NO CONVERSION NEEDED."""
        try:
            log.debug(f"[UDFileView] Setting {len(file_info_list)} enriched files directly")

            # Clear existing files
            self._model.clear_files()

            # Add enriched FileInfoData objects directly to model
            for file_info in file_info_list:
                if hasattr(file_info, 'path') and file_info.path:
                    self._model.add_file(file_info)  # Pass FileInfoData object directly

            # Single refresh pass
            self._refresh_ui()

            # Emit signal with paths for compatibility
            file_paths = [info.path for info in file_info_list if hasattr(info, 'path')]
            self.file_list_changed.emit(file_paths)

            log.debug(f"[UDFileView] Successfully set {len(file_info_list)} enriched files with full metadata")

        except Exception as e:
            log.error(f"[UDFileView] Error setting enriched files: {e}")

        return self

    def add_file(self, file_path: str) -> 'UDFileView':
        """Add a single file. Returns self for method chaining."""
        try:
            if validate_file_paths([file_path], self._config):
                file_info = create_file_info(file_path)
                if file_info:
                    self._model.add_file(file_info)
                    self._refresh_ui()
                    self.file_list_changed.emit(self._model.get_file_paths())
                    log.debug(f"Added file: {file_path}")
        
        except Exception as e:
            log.error(f"Error adding file {file_path}: {e}")
        
        return self
    
    def remove_file(self, file_path: str) -> 'UDFileView':
        """Remove a file by path. Returns self for method chaining."""
        try:
            if self._model.remove_file(file_path):
                self._refresh_ui()
                self.file_list_changed.emit(self._model.get_file_paths())
                log.debug(f"Removed file: {file_path}")
        
        except Exception as e:
            log.error(f"Error removing file {file_path}: {e}")
        
        return self
    
    def get_files(self) -> List[str]:
        """Get list of all file paths."""
        return self._model.get_file_paths()
    
    def get_selected_file(self) -> Optional[str]:
        """Get the currently selected file path."""
        return self._model.selected_file
    
    def clear_files(self) -> 'UDFileView':
        """Clear all files. Returns self for method chaining."""
        self._model.clear_files()
        self._refresh_ui()
        self.file_list_changed.emit([])
        log.debug("Cleared all files from file view")
        return self
    
    # Internal event handlers
    def _on_file_selected(self, file_path: str) -> None:
        """Handle file selection."""
        self.file_selected.emit(file_path)
        self._update_button_states()
    
    def _on_file_double_clicked(self, file_path: str) -> None:
        """Handle file double click - could trigger processing."""
        log.debug(f"File double-clicked: {file_path}")
        # For now, just emit processing requested
        # self.processing_requested.emit()
        pass # TODO: Decide if we want double-click to trigger anything - poss open in finder 
    def _on_add_files_requested(self) -> None:
        """Handle add files request by emitting intent signal (no direct dialog here)."""
        try:
            # Emit an intent that the View will translate to Local Event Bus
            self.add_files_requested.emit()
            log.debug("[UDFileView] add_files_requested emitted (delegating file dialog to FileManager via View)")
        except Exception as e:
            log.error(f"Error raising add files intent: {e}")
    
    def _on_remove_file_requested(self) -> None:
        """Handle remove file request."""
        selected_file = self.get_selected_file()
        if selected_file:
            self.remove_file(selected_file)
    
    def _remove_file_by_path(self, file_path: str) -> None:
        """Remove file by path (used by context menu)."""
        self.remove_file(file_path)
    
    def _show_context_menu(self, position: QPoint) -> None:
        """Show context menu at the given position."""
        if not self._config.allow_context_menu or not self._context_menu:
            return
        
        # Get file at position
        item = self._file_tree.itemAt(position)
        file_path = None
        if item:
            try:
                from PySide6.QtCore import Qt
                # Prefer explicit UserRole for underlying path set by FileTree
                file_path = item.data(0, Qt.ItemDataRole.UserRole)
                # Fallbacks: try EditRole and then display text
                if not file_path:
                    file_path = item.data(0, Qt.ItemDataRole.EditRole)
                if not file_path:
                    file_path = item.text(0)
            except Exception:
                # Fallback to column 0 text if role-based data not set
                try:
                    file_path = item.text(0)
                except Exception:
                    file_path = None
        
        # Create and show menu
        menu = self._context_menu.create_menu(file_path)
        if menu.actions():
            menu.exec(self._file_tree.mapToGlobal(position))
    
    def _open_file_location(self, file_path: str) -> None:
        """Open the folder containing the file (cross-platform)."""
        try:
            folder_path = str(Path(file_path).parent)
            import sys, subprocess
            if sys.platform.startswith("win"):
                os.startfile(folder_path)  # Windows
            elif sys.platform == "darwin":
                subprocess.Popen(["open", folder_path])  # macOS
            else:
                # Linux/Unix - prefer xdg-open if available
                subprocess.Popen(["xdg-open", folder_path])
            log.debug(f"Opened file location: {folder_path}")
        except Exception as e:
            log.error(f"Error opening file location: {e}")
    
    def _show_file_properties(self, file_path: str) -> None:
        """Show file properties (placeholder for future implementation)."""
        log.debug(f"File properties requested for: {file_path}")
        # TODO: Implement file properties dialog
    
    def _refresh_ui(self) -> None:
        """Refresh the user interface."""
        if self._file_tree:
            # Single refresh to avoid flicker; model is already updated
            self._file_tree.refresh_data()
        self._update_button_states()
    
    def _update_button_states(self) -> None:
        """Update button states based on current state."""
        # Update footer button states
        try:
            has_files = len(self._model.files) > 0
            has_selection = self._model.selected_file is not None
            # Add is always enabled; Remove depends on selection
            if hasattr(self, "_remove_button") and self._remove_button:
                self._remove_button.setEnabled(has_selection)
        except Exception:
            pass
    
    # BasePane overrides
    def clear(self) -> None:
        """Clear any data or selections in this pane."""
        self.clear_files()

    def _clear_selection(self) -> None:
        """Phase 3 Task B: Clear selection on data rebind or when items removed."""
        try:
            # Update model-selected_file
            self._model.selected_file = None
            # If table supports selection model, clear it
            table = self._file_tree
            sel_model = getattr(table, "selectionModel", lambda: None)()
            if sel_model:
                sel_model.clearSelection()
        except Exception as e:
            log.debug(f"[UDFileView] Non-critical: failed to clear selection: {e}")
        
    def display_enriched_file_info(self, file_info_list: List[Dict[str, Any]]) -> None:
        """Display enriched file information received from the presenter.
        
        Implements IUpdateDataView interface method.
        
        Args:
            file_info_list: List of dictionaries with enriched file information
        """
        if not file_info_list:
            return
            
        log.debug(f"[UDFileView] Received enriched file info for {len(file_info_list)} files")
        
        # Update file model with enriched information
        for file_info in file_info_list:
            file_path = file_info.get('path', '')
            if file_path and self._model.has_file(file_path):
                # Update existing file info with enriched data
                self._model.update_file_info(file_path, file_info)
                
        # Refresh the UI to show updated information
        self._refresh_ui()
        log.debug("[UDFileView] Updated display with enriched file information")

    # TEMPORARY: Testing method for focused mode
    def enable_focused_mode(self, enabled: bool = True) -> None:
        """Enable focused mode for testing purposes.

        Args:
            enabled: Whether to enable focused mode (default True)
        """
        if self._file_tree:
            self._file_tree.set_focused_mode(enabled)
            log.info(f"[UDFileView] Focused mode {'enabled' if enabled else 'disabled'}")
