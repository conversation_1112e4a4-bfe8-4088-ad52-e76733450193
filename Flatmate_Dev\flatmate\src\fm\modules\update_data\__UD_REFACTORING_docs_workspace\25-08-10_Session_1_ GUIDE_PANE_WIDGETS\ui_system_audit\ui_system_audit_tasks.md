---
description: UI system audit tasks – events vs interface methods, and file roles catalogue
---

# UI System Audit – Tasks

## Objectives
- Ensure clean MVP boundaries: presenter uses interface methods for single-listener sync flows; events only for multi-listener/async.
- Catalogue all UI-related files and define their proper roles to avoid duplication and architectural drift.
- Identify and fix duplications (e.g., presenter vs manager responsibilities) with minimal, scoped patches.

## Decision Rule (canonical)
- Interface method when: single listener (Presenter↔View), synchronous, direct UI update or user intent.
- Event when: multiple listeners must react, or asynchronous broadcast is required.

## Scope (files to audit)
- Events and buses:
  - `fm/modules/update_data/services/events.py`
  - `fm/modules/update_data/_ui/ui_events.py`
  - `fm/modules/update_data/services/local_event_bus.py`
- Orchestration and managers:
  - `fm/modules/update_data/ud_presenter.py`
  - `fm/modules/update_data/_ui/_presenter/**/*.py`
- View layer:
  - `fm/modules/update_data/_ui/ud_view.py`
  - `fm/modules/update_data/_ui/_view/**/*.py`

## Task List
- [ ] Inventory all communications (events, subscriptions, interface calls) across scope files.
- [ ] Build matrix: Name | Emitter | Consumers | Nature (sync/async, single/multi) | Use (Interface/Event) | Rationale | Action.
- [ ] Identify event/interface duplications and MVP violations (presenter doing manager work, view re-emitting widget signals, etc.).
- [ ] Propose minimal refactors (3–5 items) to realign responsibilities without changing behaviour.
- [ ] Catalogue UI files and assign proper roles (orchestrator, manager, view, layout, component, service).
- [ ] Verify MRU update flows and UI refresh points (source/archive), note exact call sites.
- [ ] Confirm selection semantics (Windows folder workaround vs plain single-file selection) and document.
- [ ] Prepare regression tests plan to lock behaviour (selection, MRU, discovery gating).
- [ ] Optional repo guardrails plan (CODEOWNERS, hooks) for protected files.

## Current Status (from conversation)
- Dealt with:
  - MRU option value types normalised to strings; post-view refresh added to avoid timing issues.
  - `FileInfoManager._on_files_discovered()` gated by monitored folders; left intact otherwise.
- Pending:
  - Full MRU end-to-end verification (source + archive lists consistently populated/persisted).
  - Event vs Interface matrix and minimal refactor list.
  - Presenter duplication removal: delegate discovery toggle to `FileDiscoveryManager`.
  - Selection semantics confirmation and documentation.
  - Regression tests and optional guardrails.

## Deliverables
- `docs/events_interface_matrix.md` (within update_data module):
  - Table with final decisions and rationale + specific actions.
- Minimal patch list (3–5 items) to:
  - Replace presenter duplication with manager delegation.
  - Remove/convert events that proxy a single listener to interface calls.
- Short catalogue of UI files and roles, placed alongside this document.

## Acceptance Criteria
- Matrix reviewed and agreed; no ambiguities in event vs interface usage.
- Presenter contains no domain/monitoring logic; only orchestrates and delegates.
- MRU behaviour verified: folder selection and Windows workaround update recent folders; options populate reliably post-view init.
- Selection semantics are documented and covered by tests plan.
- Optional: guardrails plan documented for protected files.

## Notes
- Follow UK spelling and fail-fast logging via `fm.core.services.logger.log`.
- Keep changes minimal; no speculative abstractions.
- Centre panel manager composes only; no signal relaying or business logic.
