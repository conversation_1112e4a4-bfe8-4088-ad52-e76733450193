# BMAD Story: Update Data - File Info Display and Recent Folders

**Epic:** [UPDATE DATA REFACTORING]
**Feature:** [File List and Recent Folders]
**User Role:** Data Processor
**Date:** 2025-08-07

---

## 1. User Story

As a **Data Processor**, I need to **efficiently select files and folders for processing and see detailed, relevant information about them in the file list**, so that I can **quickly and confidently prepare a valid batch of statements for import**.

This includes:
- A clear, informative file list that shows not just the filename, but also its size, creation date, and—most importantly—its detected type (e.g., "Kiwibank | Basic CSV").
- A "quick access" list of recently used folders, so I don't have to navigate through the file system repeatedly for common data sources.
- The ability to have the application monitor specific folders for new files, adding them to my list automatically.

## 2. Acceptance Criteria

### AC1: File List View and Layout
- **GIVEN** I have selected a folder of statement files
- **WHEN** the files are displayed
- **THEN** they must appear in a hierarchical tree structure with:
  - **Option A (Preferred)**: One parent folder node with a folder icon, containing all files as child nodes with file icons
  - **Option B (Alternative)**: Path from home directory (~) displayed on one line with a folder icon, and filenames listed underneath with indentation
- **AND** the list view must have the following columns in this specific order:
  1. `Name`: The filename (for files) or folder path (for folders). This column MUST stretch to fill available space.
  2. `File Info`: The enriched file details (e.g., "Kiwibank Basic CSV").
  3. `Size`: Human-readable file size.
  4. `Created`: The file's creation date.
- **AND** other columns like `Type` must be hidden by default, but available via a right-click on the header.

### AC2: Data Formatting and Display
- **GIVEN** files are shown in the list
- **WHEN** I view the `File Info` column
- **THEN** the text must be formatted without pipes (e.g., `Kiwibank Basic CSV`).
- **AND** 'Co-operative Bank' must be abbreviated to `Co-op Bank`.
- **WHEN** I view the `Created` column
- **THEN** it must correctly display the file's creation date and time from the file system.

### AC3: User Interaction
- **GIVEN** the file list is displayed
- **WHEN** I click on a file
- **THEN** the entire row is selected.
- **AND** I must be able to select multiple files using standard `Shift+Click` and `Ctrl+Click`.
- **WHEN** I right-click on a selected file
- **THEN** a context menu appears with an option to "Reveal in Explorer".

### AC4: Recent Folders Quick Access
- **GIVEN** I have previously selected files from a folder (`C:/data/statements/2024/q3`)
- **WHEN** I click the "Select Source" dropdown
- **THEN** that folder path appears in the list of recent folders for quick selection.
- **AND** this list must persist between application sessions.
- **AND** if a folder in the list is deleted or renamed on the disk, it is automatically removed from the list upon application startup.

### AC5: Per-Folder Monitoring
- **GIVEN** I have selected a source folder
- **WHEN** new files are added to that folder on the disk
- **THEN** I am asked if I want to monitor this folder for new files.
- **AND** if I enable monitoring, any new files subsequently added are automatically added to the file list in the UI.
- **AND** this monitoring setting is specific to that folder only.

### AC6: Canonical Data Source and Event System
- **GIVEN** files are added, removed, or discovered via monitoring
- **WHEN** the file list is updated
- **THEN** the system must use the established events system with the following flow:
  - FileInfoManager acts as the single source of truth ("The Librarian")
  - All file list changes must publish `FileListUpdatedEvent` with enriched `List[FileInfoData]` objects
  - The View must receive and process these events through the local event bus
  - No direct file list manipulation should bypass the FileInfoManager
- **AND** the event payload must contain fully enriched file information, not just file paths
- **AND** the UI must reflect the enriched data immediately after the event is processed
## 3. Technical Notes

- **FileInfoManager (`The Librarian`)**: This is the single source of truth. It holds the canonical `List[FileInfoData]`, performs enrichment via `FileInfoService`, and publishes the `FileListUpdatedEvent`.
- **FileConfigManager**: Manages user-facing interactions like file dialogs and the persistence of the `recent_source_folders` list via `ud_config`.
- **FileInfoData Model**: The unified dataclass that provides all necessary data and formatted properties (`.name`, `.file_info_display`, `.created_formatted`) for direct binding in the UI, ensuring the Presenter remains decoupled from Qt formatting logic.
- **Event-Driven**: The system is event-driven, with `FileConfigManager` initiating actions and `FileInfoManager` managing the resulting data state and notifying the rest of the module via the local event bus.

## 4. Definition of Done (DoD)

- [ ] All Acceptance Criteria are met and verified through manual testing.
- [ ] The implementation is confirmed to align with the technical notes.
- [ ] Code is clean, follows repository conventions (UK spelling, logging), and has no speculative clutter.
- [ ] The solution is confirmed to be robust and addresses the desynchronization issues from the previous implementation.
- [ ] All related documentation is updated to reflect the current, stable state.
