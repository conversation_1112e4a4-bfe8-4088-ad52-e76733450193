# Phase 3: Table View Improvement Plan v0 + Test-Gated Quality Pass

Status: Proposal v0  
Scope: Focused improvements to Update Data Table View behavior and UX, integrated with automated test execution using the Flatmate venv.

Links:
- PRD: [DOCS/brownfield-prd.md](DOCS/brownfield-prd.md)
- Deep Dive: [flatmate/DOCS/_GUIDES/Update-Data-UI-Deep-Dive.md](flatmate/DOCS/_GUIDES/Update-Data-UI-Deep-Dive.md)
- Event Contracts: [flatmate/src/fm/modules/update_data/_ui/ui_event_contracts.md](flatmate/src/fm/modules/update_data/_ui/ui_event_contracts.md)
- Testing Protocol: [flatmate/DOCS/_PROTOCOLS/GUIDES/testing_protocol.md](flatmate/DOCS/_PROTOCOLS/GUIDES/testing_protocol.md)

Objectives
1) Improve Table View UX and correctness with two targeted tasks that are high impact and low risk.
2) Ensure improvements are test-gated: run fast tests and smoke tests in CI and locally via Flatmate venv.
3) Keep scope tight to deliver a quick win, then follow with Phase 3.1 for broader UI improvements.

Non-Goals
- Full visual redesign
- Heavy refactor of model/data plumbing beyond what’s needed for correctness
- Introduction of new dependencies

Current State Summary
- Table View is functionally wired but exhibits inconsistent refresh and selection affordances after file add/remove events.
- Event-first contracts are established; views should render state from events and avoid ad-hoc signal coupling.
- Smoke and contract tests exist to guard the bus and dialog policies but not yet Table View operations.

Improvements (Actionable Tasks)

Task A: Consistent Refresh on FileListUpdatedEvent
- Problem: After add/remove operations, the table sometimes shows stale contents or misaligned row counts before user interaction.
- Target State: On FileListUpdatedEvent, Table View refreshes deterministically from the datamodel and shows accurate row count.
- Approach:
  - Ensure the table model listens or is driven from a single source of truth updated in FileListUpdatedEvent handlers.
  - Debounce redundant refresh calls within a short window to avoid flicker.
- Acceptance Criteria:
  - After emitting FileListUpdatedEvent with N files, the rendered row count equals N without manual user interaction.
  - No duplicate refresh or visible flicker is observed during rapid consecutive updates (manual smoke acceptable).
- Evidence:
  - Add/extend a smoke-level test (if feasible without full widget lifecycle) or manual confirmation notes.
  - Update SESSION_LOG with before/after evidence if manual.

Task B: Clear Selection State on Data Rebind
- Problem: Selection remains on rows that are removed, causing confusing highlight states or actions applying to non-existent rows.
- Target State: When the file list changes, selection is cleared if those rows no longer exist; selection never points to removed data.
- Approach:
  - On data rebind (FileListUpdatedEvent), clear the selection model when row identity set changes.
  - If desired, optionally reselect by stable identifier when rows survive, otherwise leave no selection.
- Acceptance Criteria:
  - After removing selected rows and rebinding, no selection remains for removed items; no exceptions or ghost highlights.
  - Optional: If a previously selected row persists (same file id/path), it remains selected; otherwise selection is empty.
- Evidence:
  - Manual verification steps documented; optional test hook if widget lifecycle is readily accessible in tests.

Quality Gate: Test Execution
- Local commands (Windows, Flatmate venv):
  - Focused tests:
    - cd flatmate && ./.venv_fm313/Scripts/python -m pytest -q tests/gui/update_data -k "smoke or harden or subscription" --maxfail=1 --disable-warnings
  - Full GUI tests for Update Data (if stable on your environment):
    - cd flatmate && ./.venv_fm313/Scripts/python -m pytest -q tests/gui/update_data --maxfail=1 --disable-warnings
  - With offscreen, if needed:
    - cd flatmate && ./.venv_fm313/Scripts/python -m pytest -q tests/gui/update_data --offscreen --maxfail=1 --disable-warnings

Definition of Done
- Task A and Task B acceptance criteria met.
- Tests run and pass (focused suite at minimum).
- Any minimal code changes documented in SESSION_LOG with rationale.
- PRD and Deep-Dive remain accurate; if any policy changes arise, update ui_event_contracts.md accordingly.

Risks & Mitigations
- Risk: Widget lifecycle not easily testable in headless mode.
  - Mitigation: Keep automated tests focused on bus/contract and smoke-level validations; document manual steps for UI.
- Risk: Over-scoping.
  - Mitigation: Limit to A and B only in v0; defer enhancements to Phase 3.1.

Phase 3.1 (Next)
- Broader Update Data UI Enhancements (selection UX refinement, loading/error affordances, table column autosizing rules, keyboard interactions), gated by tests and manual confirmations.