#!/usr/bin/env python3
"""
Test script for file_pane_v2 integration.

This script tests the new file_pane_v2 component to ensure it works correctly
within the update_data module.
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    # Test imports
    print("Testing imports...")
    from fm.modules.update_data._view.center_panel_components.file_pane_v2 import UDFileView
    print("[OK] UDFileView import successful")
    
    from fm.modules.update_data._view.center_panel import CenterPanelManager
    print("[OK] CenterPanelManager import successful")
    
    from fm.modules.update_data.ud_view import UpdateDataView
    print("[OK] UpdateDataView import successful")
    
    # Test basic component creation
    print("\nTesting component creation...")
    file_view = UDFileView()
    print("[OK] UDFileView creation successful")
    
    center_panel = CenterPanelManager()
    print("[OK] CenterPanelManager creation successful")
    print(f"[OK] Has file_pane_v2: {hasattr(center_panel, 'file_pane_v2')}")
    
    # Test basic operations
    print("\nTesting basic operations...")
    test_files = []  # Empty list for now
    file_view.set_files(test_files)
    print("[OK] set_files operation successful")
    
    current_files = file_view.get_files()
    print(f"[OK] get_files operation successful: {len(current_files)} files")
    
    selected_file = file_view.get_selected_file()
    print(f"[OK] get_selected_file operation successful: {selected_file}")
    
    # Test integration with center panel
    print("\nTesting center panel integration...")
    center_panel.use_file_pane_v2(True)
    print("[OK] Switched to file_pane_v2")
    
    center_panel.use_file_pane_v2(False)
    print("[OK] Switched back to original file_pane")
    
    print("\n[SUCCESS] All tests passed! file_pane_v2 integration is working correctly.")
    
except Exception as e:
    print(f"[ERROR] Test failed with error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
