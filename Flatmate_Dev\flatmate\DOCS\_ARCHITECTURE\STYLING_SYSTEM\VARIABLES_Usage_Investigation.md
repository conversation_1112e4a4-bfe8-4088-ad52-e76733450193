# Styling System Template Variables Investigation ({{...}})

Date: 2025-08-05  
Scope: Identify and document all usages of double-curly template variables within the Flatmate project, focusing on styling-related variables such as {{FONT}} and {{FONT_SIZE}} and how/where they are processed.

Summary
- Primary styling template variable in active use: {{FONT_SIZE}}
- {{FONT}} is referenced in architecture documentation and proposal, but no live occurrences were found in QSS or Python at the time of search
- {{FONT_SIZE}} is actively injected at runtime via Python string replacement and present in the consolidated QSS baseline
- Numerous {{...}} placeholders appear in documentation templates under .bmad-*; these are unrelated to runtime styling and are used by documentation tooling

Search Method
- Pattern: \{\{\s*[A-Za-z0-9_]+\s*\}\}
- Scope: Entire repository
- Results: 265 matches (docs and templates heavy). Relevant styling highlights below.

Findings (Styling-Relevant)

1) Consolidated QSS contains working placeholder
- Location: [flatmate/src/fm/gui/styles/flatmate_consolidated.qss](flatmate/src/fm/gui/styles/flatmate_consolidated.qss:1)
- Snippet:
  - font-size: {{FONT_SIZE}}px;

Interpretation:
- The consolidated baseline intentionally exposes a single, supported placeholder for font sizing. This is expected to be replaced before app.setStyleSheet().

2) Runtime replacement implemented in styles loader
- Location: [flatmate/src/fm/gui/styles/__init__.py](flatmate/src/fm/gui/styles/__init__.py:1)
- Snippets:
  - Counts placeholders:
    - placeholder_count = combined_stylesheet.count('{{FONT_SIZE}}')
  - Replacement using config:
    - font_size = config.get_value(ConfigKeys.App.BASE_FONT_SIZE, 14)
    - stylesheet = stylesheet.replace('{{FONT_SIZE}}', str(font_size))
  - Legacy path replacement:
    - combined = combined.replace('{{FONT_SIZE}}', str(font_size))

Interpretation:
- The pipeline already performs explicit string replacement of {{FONT_SIZE}} using a config-provided integer (default 14). This confirms a working mechanism for the template variable.
- Both the consolidated path and a legacy combined-path execute this replacement.

3) Documentation reflects restored behavior
- Location: [flatmate/DOCS/_ARCHITECTURE/STYLING_SYSTEM/AUDITING_THE_CURRENT_SYSTEM/Session_Log_20250805.md](flatmate/DOCS/_ARCHITECTURE/STYLING_SYSTEM/AUDITING_THE_CURRENT_SYSTEM/Session_Log_20250805.md:1)
- Notes:
  - Base font: {{FONT_SIZE}}px dynamic replacement system restored
  - Example replacement shown in code cells

Interpretation:
- The session log correctly captures the working state and expected behavior.

4) Historical/analysis documents show broader templating (not live)
- Location: [flatmate/DOCS/_ARCHITECTURE/STYLING_SYSTEM/Current_QSS_System_Analysis.md](flatmate/DOCS/_ARCHITECTURE/STYLING_SYSTEM/Current_QSS_System_Analysis.md:1)
- Notes:
  - Contains examples with many {{TOKEN}} placeholders (e.g., {{BG_DARK}}, {{PRIMARY}}) describing a previous or hypothetical system
- Location: [flatmate/DOCS/_ARCHITECTURE/STYLING_SYSTEM/Simple_Static_QSS_Solution.md](flatmate/DOCS/_ARCHITECTURE/STYLING_SYSTEM/Simple_Static_QSS_Solution.md:1)
  - Discusses replacement approach for {{FONT_SIZE}}

Interpretation:
- These are analysis/spec docs and may not reflect current runtime behavior. The active code path relies on {{FONT_SIZE}} only.

5) No active {{FONT}} occurrences found in code/QSS
- The search returned no {{FONT}} usages in the styles or QSS directories at present; mentions exist in the newly created brownfield doc (proposal level only).

Interpretation:
- {{FONT}} is not yet wired into the live styling pipeline. It remains a candidate for formalization (see Recommendations).

Non-Styling Matches (FYI)
- Many {{...}} placeholders exist under:
  - .bmad-core/templates/
  - .bmad-infrastructure-devops/templates/
  - These are for documentation generation (PRD, architecture, etc.), not runtime styling.

Conclusions
- {{FONT_SIZE}} is the only confirmed, functional styling template variable in current runtime use.
- The consolidated QSS and Python loader support it cleanly; the session work restored this mechanism.
- {{FONT}} is a desired addition (per stakeholder note) but not present in the code/QSS yet.

Recommendations
1) Keep {{FONT_SIZE}} supported and tested
- Maintain replacement in the new applier module when refactoring.
- Add a simple count/telemetry log (e.g., “FONT_SIZE replacements: N”) to ensure replacements actually occurred.

2) Introduce {{FONT}} as a first-class variable
- Add {{FONT}} to consolidated QSS where appropriate (e.g., global font-family declaration).
- Implement config-driven replacement in the applier (e.g., configured family string with safe fallbacks).
- Provide a conservative default chain:
  - ".AppleSystemUIFont, -apple-system, Segoe UI, Arial, sans-serif"
- Add similar telemetry: “FONT replacements: N”.

3) Guard against accidental replacements
- Ensure we only replace exact tokens {{FONT_SIZE}} and {{FONT}} (case-sensitive) to avoid collisions.
- Optionally add unit tests or lightweight checks that:
  - Verify at least one occurrence is replaced
  - Validate final stylesheet contains no dangling {{...}} tokens for these two names

4) Document and centralize variables contract
- In styles/README.md, declare supported template variables and their sources:
  - {{FONT_SIZE}}: integer from ConfigKeys.App.BASE_FONT_SIZE
  - {{FONT}}: string from configuration, with fallback chain
- Explicitly state that CSS var() is not relied upon; future experiments are separate.

Appendix: Key References
- Working QSS placeholder: [flatmate/src/fm/gui/styles/base_theme.qss](flatmate/src/fm/gui/styles/base_theme.qss:1)
- Replacement logic and flags: [flatmate/src/fm/gui/styles/__init__.py](flatmate/src/fm/gui/styles/__init__.py:1)
- Session log confirmation: [flatmate/DOCS/_ARCHITECTURE/STYLING_SYSTEM/AUDITING_THE_CURRENT_SYSTEM/Session_Log_20250805.md](flatmate/DOCS/_ARCHITECTURE/STYLING_SYSTEM/AUDITING_THE_CURRENT_SYSTEM/Session_Log_20250805.md:1)
- Proposal specifying {{FONT}} adoption: [flatmate/DOCS/_ARCHITECTURE/STYLING_SYSTEM/BROWNFIELD_Styling_System_Architecture.md](flatmate/DOCS/_ARCHITECTURE/STYLING_SYSTEM/BROWNFIELD_Styling_System_Architecture.md:1)

Next Actions (if implementing)
- Add {{FONT}} to consolidated QSS near the global font-family and wire replacement in applier.
- Add simple transform logs and a quick replacement check.
- Update styles README with variable contracts.