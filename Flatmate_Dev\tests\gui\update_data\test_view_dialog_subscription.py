import pytest

from fm.modules.update_data.services.local_event_bus import update_data_local_bus, ViewEvents
from fm.modules.update_data._ui.ui_events import DialogRequestEvent


@pytest.fixture(autouse=True)
def _clean_bus():
    update_data_local_bus.clear_event_log()
    yield
    update_data_local_bus.clear_event_log()


def test_view_like_subscription_receives_error_dialog_request_offscreen():
    """
    Lightweight offscreen-friendly interaction:
    - Simulates the View's role as the subscriber to *_DIALOG_REQUESTED
    - No real QMessageBox shown; we capture the payload that a View would render
    """
    captured = {}

    def view_like_error_handler(evt):
        # This emulates UpdateDataView's dialog handler without creating real dialogs
        captured["dialog"] = {
            "type": getattr(evt, "dialog_type", None),
            "title": getattr(evt, "title", None),
            "message": (getattr(evt, "extra_data", {}) or {}).get("message"),
        }

    # "View" subscribes to the bus
    update_data_local_bus.subscribe(ViewEvents.ERROR_DIALOG_REQUESTED.value, view_like_error_handler)

    # Presenter/Manager would emit this; we emit directly to validate contract/flow
    update_data_local_bus.emit(
        ViewEvents.ERROR_DIALOG_REQUESTED.value,
        DialogRequestEvent(
            dialog_type="error",
            title="Selection Error",
            extra_data={"message": "No supported files found in the selected folder."},
        ),
    )

    assert "dialog" in captured, "View-like handler did not receive the dialog request"
    dlg = captured["dialog"]
    assert dlg["type"] == "error"
    assert dlg["title"] == "Selection Error"
    assert dlg["message"] == "No supported files found in the selected folder."


@pytest.mark.skip(reason="Optional: integrate real UpdateDataView instance if/when test harness supports widget lifecycle here")
def test_real_view_subscription_smoke(qtbot):
    """
    Optional follow-up (skipped by default):
    - Instantiate the real UpdateDataView (when available here)
    - Connect bus to its handler
    - Emit *_DIALOG_REQUESTED and ensure handler is invoked
    """
    pass