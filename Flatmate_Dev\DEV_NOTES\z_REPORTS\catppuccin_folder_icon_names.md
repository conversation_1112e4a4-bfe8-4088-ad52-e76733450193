# Catppuccin VSCode Folder Icon Names

Below is a list of known valid folder icon names for use with the Catppuccin VSCode icon theme. Use these in your `catppuccin-icons.associations.folders` mapping.

- folder
- folder-src
- folder-documents
- folder-types
- folder-app
- folder-assets
- folder-config
- folder-database
- folder-dist
- folder-downloads
- folder-git
- folder-images
- folder-lib
- folder-node
- folder-public
- folder-scripts
- folder-test
- folder-tools
- folder-utils
- folder-vscode
- folder-workspace

**Usage Example:**
```json
"catppuccin-icons.associations.folders": {
  "docs": "folder-documents",
  "src": "folder-src",
  "assets": "folder-assets"
}
```

For the most up-to-date and complete list, refer to the Catppuccin Icons extension preview in VSCode or their [GitHub repository](https://github.com/catppuccin/vscode-icons).
