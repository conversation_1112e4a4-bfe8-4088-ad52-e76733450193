
START AGAIN :

look here, in plain english are the concerns, this is how the app should work:

#  ... the file list and therefore ud_file_view'S FILE_TREE can be populated a number of ways ...
- by the left panel - either an entire folder of files 
- or a list of selected files, depending on the selected method.
- Or when a folder for which file monitering is enabled finds new files

**either weay it is updated with a list of filepaths**

# the file tree also needs to display information about the files gleaned from the statement handlers 
 - bank, variant (if present) and filetype. 
 - this would be under a single column. it IS CURRENTLY 'status' but should be something else:  'type' or 'file info' 
 - this is currently accesed using the file_info_service: update_data/services/file_info_service.py which uses the handler registry to canvas files.
 - if a file is unknown it should just show .csv
 - the created column shows the date and time for individual files - this should obviously also be handled by the file info service.
 
 # column display - 
 'Selecteed files' column 

# The file ist is edited by the user in the file list.
The user can edit the list, add and remove, from multiple sources 
The list that finally gets processed, is the list the user has composed in the file view file tree


# Left Panel

 ## select source option group

 when files are selected, the last accessed folder is added to the source options list and displayed as the selected option.
 it persists in that option list
 for quick access
 if a folder is deleted or renamed on the hard drive, the folder should be removed from the list.
 
 
 ## select archive group 

 when an archive folder is selected the folder name is added to the archive options list and displayed. 
 It persists. if it is renamed or removed from the hard drive it is removed the list 


 ## the guide_pane (Center panel)

 the guide pane needs to comunicate to the user.  It needs to be concise but freindly.
 when new folders are encountered the user is presented with option to moniter them for new files. (i)
 the (i) icon gives a tool tip with a more in depth explanation.(integrated icon?)
 **folder mointering is toggeled PER FOLDER. NOT GLOABALLY.
 option: monitor folder for new files? - (dir_name)






#  so I dont really care a great deal how any of this is achieved, I just want a functional, maintainable app.

 in fact i just want much of the functionality I already had, and which has been lost in the refactoring  that was supposed to "improve" things and make the app "easier to maintain'. 

 I want pragmatic design, not arhictectural purity, or overated oop complexity.
 
 The quesiton is , how SHOULD this be handled.
 Because the current set up seems to be a mare. 
 I dont need a 12 page architectural treatise. 


 This is a discussion, i want simple ,  concise, clear answers in plain english.
 Options, pros cons and recs.

-----------

# Give your suggestions below:


