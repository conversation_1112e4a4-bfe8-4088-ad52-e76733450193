---
description: Guide Pane polish backlog
---

# Guide Pane Backlog (UX polish)

- __File discovery UX__
  - Move the "File discovery" checkbox into the Source header row.
  - Remove the info button; attach its tooltip to the checkbox directly.
  - Keep per-folder persistence and avoid recursive UI updates.

- __Source summary clarity__
  - Bold the folder base name; show the full path on hover tooltip.

- __File count location__
  - Show file count in the file table header (e.g. "Selected Files (N)").
  - Remove banner-based count to avoid duplication.

- __Test scenarios__
  - MRU preselect with quick-select on/off.
  - Manual file/folder selection.
  - Toggle discovery on/off; verify persisted state.
