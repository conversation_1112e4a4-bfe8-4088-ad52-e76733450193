from __future__ import annotations

from typing import Optional

from PySide6.QtWidgets import QWidget, QLabel

from .base_components import Section, SlotRow


class ArchiveSection(Section):
    """
    Archive section with a fixed 'path' slot (hidden by default).

    API:
      - set_summary(text), clear_summary()
      - set_path_html(html, tooltip), clear_path(), show_path(visible)
    """

    def __init__(self, parent: Optional[QWidget] = None) -> None:
        super().__init__("Archive", parent)

        # Path slot: just a label, single line
        self._path_label = QLabel("")
        self._path_label.setWordWrap(False)
        self._path_label.setTextInteractionFlags(self._path_label.textInteractionFlags())
        self._path_row = SlotRow()
        self._path_row.set_content(self._path_label)
        self._path_row.set_horizontal_padding(8)
        self._path_row.set_visible(False)
        self.add_to_body(self._path_row)

    # --- Summary/info line ---
    def set_summary(self, text: str) -> None:
        self.set_info(text or "")

    def clear_summary(self) -> None:
        self.set_info("")

    # --- Path slot ---
    def set_path_html(self, html: str, tooltip: str = "") -> None:
        self._path_label.setText(html or "")
        self._path_label.setToolTip(tooltip or "")
        self._path_row.set_visible(bool(html))

    def clear_path(self) -> None:
        self._path_label.setText("")
        self._path_label.setToolTip("")
        self._path_row.set_visible(False)

    def show_path(self, visible: bool) -> None:
        self._path_row.set_visible(bool(visible))
