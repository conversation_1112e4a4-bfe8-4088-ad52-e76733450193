# Project-Documentation-Guide

Status: Authoritative  
Scope: How we write, organize, name, link, and maintain documentation across this repository.

Why this guide
Consistent documentation reduces cognitive load, speeds onboarding, and prevents drift. This guide standardizes naming, structure, linking, and maintenance so contributors can quickly find, update, and trust docs.

Core Principles
- Single Source of Truth: Each topic should have one authoritative doc. Point related docs back to it.
- Minimal Drift: Prefer updating the authoritative doc over spawning new variants.
- Clear Ownership: Each authoritative doc states its scope and status at the top.
- Evidence by Default: Embed minimal evidence inline; link or archive heavy artifacts in EVIDENCE when needed.
- Practical First: Optimize for developer workflow and grep/find operations.

Naming Conventions
- Protocols and Guides filenames use hyphen-separators:
  - testing-protocol.md
  - testing-smoke-tests.md
  - project-documentation-guide.md
  - session-documentation-guide.md
  - report-writing-guide.md
  - review-this-codebase-onboarding-guide.md
- PRDs and other docs:
  - Keep existing conventions for PRDs to avoid namespace clutter.
- Headings within documents:
  - Use Title-Case where appropriate; be consistent.
  - For protocol and guide headings, hyphenated section titles are acceptable where helpful for grep consistency.

Directory Structure
- Canonical guides live in: flatmate/DOCS/_PROTOCOLS/GUIDES/
- Product/architecture docs live in: DOCS/ or module-local workspaces as appropriate
- Module-local work-in-progress workspaces are allowed near code when scoped to that module:
  - Example: flatmate/src/fm/modules/update_data/UD_REFACTORING_docs_workspace/
- Evidence storage:
  - Inline-by-default for small snippets
  - Use EVIDENCE/ for large logs, screenshots, or heavy artifacts during sessions
  - Reference evidence from session logs or PRDs when relevant

Document Types and Purpose
- Protocol: Step-by-step operational procedures (e.g., testing-protocol.md, session-documentation-guide.md)
- Guide: Explanatory material and best practices (e.g., report-writing-guide.md)
- PRD: Product requirements and acceptance criteria for a specific initiative
- Deep-Dive: Authoritative architecture/domain exposition
- RFC: Proposal and decision record for structural changes (e.g., directory/API exposure)

Authoritative Docs and Cross-Linking
- Always link to the authoritative source when referencing a concept:
  - Event Contracts: flatmate/src/fm/modules/update_data/_ui/ui_event_contracts.md
  - Update Data UI Deep-Dive: flatmate/DOCS/_GUIDES/Update-Data-UI-Deep-Dive.md
  - Testing Protocol: flatmate/DOCS/_PROTOCOLS/GUIDES/testing-protocol.md
- If a doc derives from or constrains another, state the hierarchy at the top and add “Links” section.

Change Management
- When renaming guides/protocols, update all inbound references repo-wide.
- Keep a simple “Change Log” at bottom of authoritative docs for significant changes.
- Document conventions tweaks in this guide and update links accordingly.

Session Documentation
- Use session-documentation-guide.md to set up and log work sessions.
- Maintain a SESSION_LOG.md for non-trivial sessions.
- Capture:
  - Actions taken
  - Discoveries
  - Decisions
  - Next steps
  - Evidence references

Testing Documentation
- Follow testing-protocol.md for:
  - Test taxonomy (smoke, contract, integration)
  - Git Bash and CMD/PowerShell run commands
  - Offscreen options and harness usage
- Keep smoke tests fast and minimal; run them first locally and in CI.

PRD Guidance
- Keep PRD naming and placement consistent with existing repository conventions.
- Start with clear “Goals/Non-Goals/Acceptance Criteria/References”.
- Link back to authoritative guides and deep-dives instead of duplicating content.

RFCs (Lightweight)
- Use RFCs for structural changes (e.g., API exposure, directory naming adjustments).
- Include options considered, decision rationale, migration steps, and link updates required.
- Locate RFCs near the affected module’s workspace or in a central RFC folder if cross-cutting.

Consistency Checklist
- [ ] File is in the correct directory (e.g., _PROTOCOLS/GUIDES for guides)
- [ ] Filename matches hyphen convention for protocols/guides
- [ ] Scope and status are declared at top
- [ ] Links to authoritative sources are present and accurate
- [ ] No duplication with existing docs; if overlap exists, consolidate or cross-link
- [ ] Evidence references included where applicable
- [ ] If renames occurred, inbound links were updated

Quick Links
- Testing Protocol: flatmate/DOCS/_PROTOCOLS/GUIDES/testing-protocol.md
- Session Documentation Guide: flatmate/DOCS/_PROTOCOLS/GUIDES/session-documentation-guide.md
- Report Writing Guide: flatmate/DOCS/_PROTOCOLS/GUIDES/report-writing-guide.md
- Review/Onboarding Guide: flatmate/DOCS/_PROTOCOLS/GUIDES/review-this-codebase-onboarding-guide.md

Change Log
- v1.0: Initial version defining authoritative naming, placement, and cross-linking conventions for the project.
