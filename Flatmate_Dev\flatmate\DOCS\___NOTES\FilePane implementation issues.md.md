# FilePane implementation issues.md
# user test notes:
Thursday, August 7, 2025 @ 10:50:16 PM


issue: Column: 'Name' is not stretching to fill available space. It should also read "Selected Files"
## the the parnet folder un this test reads:
[icon] ~/Downloads

## the containg folder reads 
[icon] ~/Doenloads\_flatma... (truncated by column)
- issue: path seperator mismatch on windows 
- issue: the file names are heavily truncated by the column width especially on smaller machines (tablet size )

-issue: The columns are not resizable

Issue: The column order should be 

`Selected Files`, `File Info`, `Size`, `Created`
It is currently 
`Name`, `File Info`, `Size`, `created`, `Type`

- The `Selected Files` column should stretch to fill available space.

The File Info curretly reads eg kiwibank | basic 

It should read e.g. `Kiwibank Basic CSV` (with no pipes)

currently `.csv` is in the `Type` Column

We should lose the `type` collumn as it is redundant.

Instead opting for the statement handler: `bank, variant, file type`  approach.

It may be neccesary to examine what the current file info object is actually returnng 



-----
# 25-08-11 @ 10:11:11

## issues: 
### GuidePane:

 In the guide pane, the filepaths are long and not particularly readable, although I do want more wholesome information in the guide pane about the filepaths. I'd like to format them a bit so that they're more readable. The other issue is that the source file has capitals and is capitalised in places in line with how the folders are actually named. In the archive section, there  the filepaths is all lower case, so this is a discrepancy. For one thing, the file path could point from the user folder. ~/OneDrive/Documents/Accounts/**bank_statement_downloads_2025** and the base name could be bold
 in either case  |
 -a right click should allow to 'open in explorer'
 -a hover should show the full path as a tool tip

 - issue: the file paths are not formatted in a way that is easy to read.
 - issue: they are inconsistently formatted 

### UDFileView:

Issues: 
In the first line 

 we have the folder path from the user folder (~) to the files' grandparent folder : ~/Downloads
 in the second line in the tree we this again in a file path  ~/Downloads/_flatmete_file_moniter_test
we should either have only the one folder name in the two nodes of the tree, or have only the 2nd line. 


