# Story A — Update Data: Table View Visual Quality and Interactions (Phase 3.1)

Status: Draft  
Owner: Scrum Master (Bob)  
Source PRD: [PRD_table_view_and_ui_enhancements.md](PRD_table_view_and_ui_enhancements.md)

## Story

As a user working in Update Data, I need a table view that looks professional and behaves predictably (stable column widths, clear show/hide affordances, consistent visual states) so that I can browse and manage file rows with less friction and higher legibility.

## Goals

- Column Resizing & Stable Stretch: Sensible default widths; a stretch strategy that avoids horizontal jitter and does not hide key columns.
- Show/Hide Columns: Provide a lightweight and discoverable affordance to toggle a defined set of columns.
- Visual States: Ensure selection/highlight consistency across dataset changes; empty/error states are visually clear and non-jarring.
- Optional Persistence: If low-risk, support session-level or config-level persistence for column widths or visibility (confirm scope below).

## Out of Scope

- Wholesale redesign of the Update Data module UI.
- Heavy data model refactors beyond what is required for the above enhancements.
- Introduction of new runtime dependencies unless specifically justified by the implementation.

## Acceptance Criteria

1) After FileListUpdatedEvent, visible row count equals dataset size; no flicker on consecutive updates.  
2) Column widths are reasonable by default; last section stretch does not hide key columns.  
3) Users can hide/show defined columns via a clear control or a header context menu entry.  
4) Selection and highlight remain consistent across add/remove and rebinds.  
5) Manual visual check documented; any automated checks added where feasible.

(Adapted from PRD lines 48–53)

## Clarifications Required

- Columns in scope for show/hide:
  - Specify the exact column identifiers intended for toggling in Phase 3.1.
- Affordance placement:
  - Choose: Toolbar menu item OR Table header context menu (or both, if minimal).
- Persistence scope for this phase:
  - Choose one:
    - Session-only (non-durable, resets on app restart)
    - Persisted config (file/key path to be documented)
    - Not in scope for Phase 3.1 (document rationale)

Record final decisions in Dev Notes before implementation begins.

## Technical Notes

- Respect existing lifecycle constraints around dataset rebinds to avoid flicker.
- Apply low-risk defaults for row height, padding, and header styling to improve readability.
- Confirm alternating row color consistency (already applied in v0 per PRD).
- Keep changes focused on UI behavior and simple configuration surfaces.

## Testing

Commands (run from repo root):

- Git Bash:
  ./.venv_fm313/Scripts/python.exe -m pytest -q tests/gui/update_data -k "smoke or harden or subscription" --maxfail=1 --disable-warnings
- Windows CMD/PowerShell:
  .venv_fm313\Scripts\python.exe -m pytest -q tests\gui\update_data -k "smoke or harden or subscription" --maxfail=1 --disable-warnings
- Optional headless:
  Add --offscreen (supported in conftest)

Evidence:
- Add brief notes and a minimal screenshot set into SESSION_LOG.md:
  - Default table
  - After column hide/show
  - After FileListUpdatedEvent (no flicker, correct row count)
  - Selection/highlight consistency after rebind

## Risks & Mitigations

- Risk: Over-scoping aesthetics causing regressions.
  - Mitigation: Keep to low-risk defaults; validate with smoke tests and manual notes.
- Risk: Visual automation complexity in the Table View lifecycle.
  - Mitigation: Combine smoke/contract tests with minimal manual evidence; consider pytest-qt later.

## Tasks / Subtasks

- Define column defaults and stretch behavior
- Implement show/hide affordance at chosen location
- Ensure visual states (selection/highlight, empty/error) are consistent
- Optional: Implement chosen persistence level (or explicitly mark as out of scope)
- Update any relevant UI docs or inline code comments

## Dev Notes

- Record final decisions for:
  - Columns in scope for toggling
  - Affordance placement
  - Persistence scope and storage details (if applicable)