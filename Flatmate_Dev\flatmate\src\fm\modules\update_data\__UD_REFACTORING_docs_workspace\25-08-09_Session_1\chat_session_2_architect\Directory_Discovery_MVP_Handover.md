# Directory Discovery MVP – Handover

Date: 2025-08-09
Owner: Update Data (Architect: <PERSON>)

## Executive Summary
- Manual file selection flows are unchanged (select folder → root files; select files → specific files).
- Scanning and extension parsing logic has been centralised into core utilities (no duplication in UI layer).
- When a folder is selected manually, we record directory discovery state in an in-memory registry (`DirectoryInfoService`): discovery enabled + last scan time, and archive destination resolved from the left panel setting.
- No live folder monitoring is enabled by these changes. No user-facing discovery toggle yet.

## Current System Behaviour
- **Manual selection (unchanged)**
  - From left panel, user chooses:
    - Select Folder → UI lists root files only from that folder.
    - Select Files → user picks individual files.
  - Files are enriched by `FileInfoService` and displayed via the existing pipeline (`FileInfoManager` → publish FILE_LIST_UPDATED with enriched `FileInfoData`).

- **Discovery metadata recording (new, passive)**
  - On folder selection, we update the directory registry:
    - `enable_discovery(folder, archive_dest)`
    - `mark_scanned_now(folder)`
  - Archive destination resolution rule (MVP):
    - Same as Source → the source folder.
    - Select Location → `state.save_location` if set.

- **What’s explicitly NOT included**
  - No continuous/live monitoring is started by these changes.
  - No explicit user-facing discovery toggle exists yet.

## Code Changes (by file)
- `fm/core/directory/utils/__init__.py`
  - Created package marker.
- `fm/core/directory/utils/extensions.py`
  - Added `normalise_ext(filename) -> str` and `parse_supported_extensions(value, default) -> list[str]`.
- `fm/core/directory/utils/file_scan.py`
  - Added `scan_root_files(path: Path, include_exts: Iterable[str]) -> list[Path]` (non-recursive root scan).
- `fm/modules/update_data/_ui/_view/shared_components/file_selector.py`
  - Delegates extension parsing and scanning to core utils (`parse_supported_extensions`, `scan_root_files`).
  - Preserves existing UI dialogue behaviour and logging.
- `fm/modules/update_data/_ui/_presenter/file_config_manager.py`
  - Injected `DirectoryInfoService`.
  - On folder selection path, call `_update_directory_info(folder_path)` which resolves archive destination, calls `enable_discovery(...)`, and `mark_scanned_now(...)`.

## Architectural Rationale
- **Separation of concerns**: scanning + extension parsing belong to core utilities, not the UI layer.
- **Single source of truth**: supported extensions come from `ud_keys.Files.SUPPORTED` via `parse_supported_extensions`.
- **MVP registry**: record per-folder discovery intent and last scan, without introducing persistence or live monitoring.
- **Presenter cleanliness**: no Qt coupling introduced; uses services and view interface only. Logging via `fm.core.services.logger.log`.

## Decisions Needed
1. **Discovery Toggle**
   - Keep auto-enable on folder select, or gate via a user-facing toggle?
   - Recommendation: Add explicit “Enable file discovery for this folder” toggle. If accepted, remove auto-enable on folder select.
2. **Live Monitoring (Auto-Queue)**
   - When enabled (per-folder), start/stop `folder_monitor_service` and pipe discoveries into `FileInfoManager`.
   - Decision: scope and timing for enabling automatic add.
3. **Persistence**
   - MVP registry is in-memory only. Decide where/how to persist `DirectoryInfoService` state (config vs DB) and load at startup.
4. **Naming**
   - Consider renaming `FileInfoService` to `FileMetadataService` to avoid confusion with discovery.

## Recommended Next Steps
- **Add discovery toggle (per-folder)**
  - View event (e.g. DISCOVERY_TOGGLED with folder, enabled) + presenter/manager handler.
  - enable → `DirectoryInfoService.enable_discovery`; disable → `disable_discovery`.
  - Remove auto-enable-on-folder-select if toggle is adopted.
- **Wire live monitoring (optional now, target state)**
  - If toggle enabled: start/stop `folder_monitor_service` for that folder, route `FILES_DISCOVERED` into `FileInfoManager.add_files`.
- **Audit duplicates**
  - Replace any residual ad-hoc scans/extension parsing with calls to core utils.
- **Persistence hooks**
  - Implement load/save for `DirectoryInfoService` (config-backed for MVP), restore states on startup.
- **Docs**
  - Update Architecture Plan with this wiring summary and toggle decision.

## Testing Checklist
- Select Files: enrich + display as before.
- Select Folder: root files listed, enrich + display; registry shows discovery enabled, last_scan set; archive summary reflects save option.
- If toggle implemented: verify enable/disable updates registry and, if live monitoring wired, new files are auto-added only when enabled.

## Risks / Notes
- In-memory registry is volatile; without persistence, discovery states are lost per session.
- Auto-enable on folder select could surprise users; prefer explicit toggle for clarity.
- Ensure no Qt coupling creeps into presenter; continue to use view interface and local bus for intents.

## References
- Core utils: `fm/core/directory/utils/extensions.py`, `fm/core/directory/utils/file_scan.py`
- Registry: `fm/core/directory/services/directory_info_service.py`
- UI delegating: `modules/update_data/_ui/_view/shared_components/file_selector.py`
- Presenter integration: `modules/update_data/_ui/_presenter/file_config_manager.py`
- Enrichment pipeline: `modules/update_data/services/file_info_service.py`, `modules/update_data/_ui/_presenter/file_info_manager.py`
