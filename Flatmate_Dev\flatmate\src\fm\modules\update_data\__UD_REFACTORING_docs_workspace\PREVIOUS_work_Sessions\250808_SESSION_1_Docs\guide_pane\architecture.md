# Guide Pane Architecture (Update Data)

This document captures the minimal, pragmatic architecture for the Update Data guide pane, aligned with our MVP pattern and recent MRU/archive work.

- Files:
  - `src/fm/modules/update_data/_ui/_view/center_panel_components/guide_pane.py`
  - `src/fm/modules/update_data/_ui/ud_view.py`
  - `src/fm/modules/update_data/_ui/interface/i_view_interface.py`
  - `src/fm/modules/update_data/_ui/_presenter/file_config_manager.py`
  - `src/fm/modules/update_data/services/local_event_bus.py`

- Goals:
  - Provide clear, contextual instructions and status.
  - Show archive summary persistently ("Archive: Same as Source" or a path).
  - Offer contextual options (monitor folder, auto‑queue new files) without clutter.
  - Maintain strict MVP boundaries: presenter ↔ view via interface; events only when multiple listeners/async are needed.

## Components and Contracts

- View Widget: `GuidePaneWidget` (Qt)
  - Purpose: Render instructions, stateful status, persistent archive summary, and contextual controls.
  - Key methods:
    - `display(message: str, state: str = 'info', format_type: str = 'plain')`
    - `set_state(state: str, context: dict | None)` and `set_status(state: str, **context)`
    - `set_archive_summary(text: str)` — persistent single‑line archive summary
    - `show_source_context_options(monitor_enabled: bool, auto_queue_enabled: bool)`
  - Signals (View → Presenter via View):
    - `publish_toggle_folder_monitoring_requested(bool)`
    - `publish_toggle_auto_queue_requested(bool)` (optional; only wire if event exists)

- View Interface: `IUpdateDataView`
  - Presenter‑facing API (no Qt references):
    - `set_guide_archive_summary(text: str)`
    - `show_guide_source_options(monitor_enabled: bool = False, auto_queue_enabled: bool = False)`
    - Existing methods for archive/source options, dialogs, etc.

- Concrete View: `UpdateDataView`
  - Implements the interface by delegating to `GuidePaneWidget`:
    - `set_guide_archive_summary` → `guide_pane.set_archive_summary(text)` with fallback to `display()`
    - `show_guide_source_options` → `guide_pane.show_source_context_options(...)`
  - Wires widget signals to Local Event Bus:
    - `publish_toggle_folder_monitoring_requested` → `ViewEvents.FOLDER_MONITORING_TOGGLED`
    - Optionally `publish_toggle_auto_queue_requested` → `ViewEvents.AUTO_QUEUE_TOGGLED` (guarded)
  - Provides synchronous folder dialog for archive selection via `QFileDialog.getExistingDirectory`.

- Presenter: e.g., `file_config_manager.py`
  - Uses interface methods only:
    - Calls `set_guide_archive_summary("Archive: Same as Source")` or `set_guide_archive_summary(f"Archive: {path}")` on save changes.
    - Calls `show_guide_source_options(monitor_enabled=..., auto_queue_enabled=...)` when source context is active.
  - Listens to Local Event Bus for toggles and dialog results (as needed) and updates model/state accordingly.

- Events (Local Bus): `services/local_event_bus.py`
  - Use events only when multiple listeners/async are required.
  - Current:
    - `FOLDER_MONITORING_TOGGLED` — emitted from view when user toggles monitoring.
    - Optional: `AUTO_QUEUE_TOGGLED` — define only when the feature needs bus propagation.

## UX Behaviour

- Instructions and Status
  - Main message uses `display()` or `set_state()`/`set_status()` with colour‑coded state.
  - Avoids noisy updates; use brief, informative text.

- Archive Summary
  - Shown below the main message as a dedicated label, not replacing the message.
  - Values:
    - "Archive: Same as Source" when `SaveOptions.SAME_AS_SOURCE` is active.
    - "Archive: <folder path>" for custom locations or MRU selection.

- Contextual Options
  - `Monitor Folder` and `Auto‑Queue New Files` appear together when source context is relevant.
  - Both toggles are reflected to the presenter via Local Event Bus (folder monitoring unconditionally; auto‑queue only if event is present).

## MVP Compliance

- Presenter never imports Qt or accesses widget fields; uses `IUpdateDataView` only.
- View/Widget owns Qt logic and maps signals to the Local Event Bus.
- Events are not duplicated where direct interface calls suffice.

## Extension Points

- Tooltips for MRU entries showing full paths (source and archive).
- Optional confirm flow for archive MRU selection (mirroring source quick‑select preference), behind a preference key.
- Guide pane: add compact history of actions (non‑intrusive, capped length) if needed.

## Testing Checklist

- Guide pane shows welcome/initial instructions on module load.
- Selecting source updates instructions/state without breaking archive summary.
- Changing save option updates the archive summary correctly.
- Toggling `Monitor Folder` emits `FOLDER_MONITORING_TOGGLED` with correct bool.
- If defined, toggling `Auto‑Queue` emits `AUTO_QUEUE_TOGGLED` with correct bool.
- MRU archive selection updates summary immediately and persists MRU.
- Folder dialog returns selected path and updates summary/state.

## Notes

- UK spelling throughout UI text where possible.
- Keep code explicit and readable; avoid speculative clutter.
- Group related functionality with clear section comments in code (API at top; internal helpers; error handling at end).
