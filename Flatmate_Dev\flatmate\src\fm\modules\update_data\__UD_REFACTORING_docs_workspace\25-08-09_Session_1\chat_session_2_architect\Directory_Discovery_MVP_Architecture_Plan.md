# Directory Discovery MVP — Architecture Plan

Date: 2025-08-09
Owner: Architect (<PERSON>)

## Context & Goal
- Goal: Zero-config golden path — files in the current source folder automatically appear in the Update Data file list.
- MVP scope: Single current source folder; per-folder toggle; “new files” = files in folder root only; rely on archiving to keep root clean; no DB/history checks.

## Scope (MVP)
- One current source folder (exposed in `guide_pane.py`).
- Toggle text: “Enable file discovery for this folder”. Per-folder state.
- Root-only scan. Supported extensions only (centralised).
- Archive destination: from left panel setting (Same as Source). No actual archiving logic changed in this MVP.
- No events for this flow; presenter orchestrates directly via interfaces/services.

## Ownership & Boundaries
- `fm/core/directory/models/directory_info.py`: immutable `DirectoryInfo` datum.
- `fm/core/directory/services/directory_info_service.py`: in-memory registry + minimal API. No hidden IO.
- Scanning: separate utility (pure function). No service IO.
- Presenter (Guide Pane Presenter): orchestration owner; reads UI state, calls service, triggers scan, updates view.
- View: expose explicit file-list API (prefer file_pane_v2 methods where active). No Qt coupling in presenter.

## Flow (Enable Discovery)
1) User toggles “Enable file discovery for this folder” in `guide_pane.py`.
2) Presenter resolves `current_folder: Path` and `archive_dest: Path` (from left panel setting).
3) Presenter calls `DirectoryInfoService.enable_discovery(current_folder, archive_dest)`.
4) Presenter calls scan: `scan_root_files(current_folder, include_exts)`.
5) Presenter updates view file list (v2 API if active): `view.set_files([...])`.
6) Presenter calls `DirectoryInfoService.mark_scanned_now(current_folder)`.

Disable path:
- Presenter calls `DirectoryInfoService.disable_discovery(current_folder)`; no auto-refresh.

## Scanning Utility (new)
- Location: `fm/core/directory/utils.py`
- Function:
  ```python
  from pathlib import Path
  from typing import Iterable

  def scan_root_files(path: Path, include_exts: set[str]) -> list[Path]:
      """Non-recursive scan of `path` returning files matching include_exts (case-insensitive)."""
      ...
  ```
- Behaviour:
  - Non-recursive; ignore subfolders.
  - Case-insensitive extension match; normalise to lower-case.
  - Return absolute `Path` items.

## Interfaces & Errors
- `DirectoryInfoService.enable_discovery(path, archive_dest)` raises `ValueError` if `archive_dest` is missing for a new directory — fail loud & early.
- Presenter catches only when needed to surface UX messaging. Otherwise let it propagate during development.
- Logging via `from fm.core.services.logger import log`; INFO for user actions; DEBUG for scan counts/filters.

## Checklist (Implementation)
- [ ] Add `fm/core/directory/utils.py` with `scan_root_files()` (and centralised `SUPPORTED_EXTS`).
- [ ] Wire toggle in `guide_pane.py` presenter to call service and trigger scan.
- [ ] Read archive dest from left panel setting (“Same as Source”).
- [ ] Update view file list directly (prefer v2 API: `set_files`, `clear_files`).
- [ ] Call `mark_scanned_now()` after successful refresh.
- [ ] Replace ad-hoc prints with `log.info` / `log.debug`.
- [ ] Keep event bus out of this path.

## Risks / Tech Debt
- Parallel panes (legacy vs v2): select correct API guard.
- Extension sprawl: ensure one source of truth.
- No persistence: service is in-memory (acceptable for MVP; add later if needed).

## Out of Scope (Future)
- Folder monitoring service.
- Persistence for `DirectoryInfoService` (load/save from config).
- Multi-folder support and conflict resolution.
