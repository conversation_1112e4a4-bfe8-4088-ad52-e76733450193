from __future__ import annotations

from pathlib import Path
from typing import Iterable, List, Set

from .extensions import normalise_ext


def scan_root_files(path: Path, include_exts: Iterable[str]) -> List[Path]:
    """Return files in the root of `path` whose extensions match include_exts (case-insensitive).

    - Non-recursive: only immediate children of `path`.
    - include_exts should be items without leading dots and already lower-cased.
    - Returns absolute Paths.
    """
    if path is None:
        return []

    p = path.resolve()
    if not p.exists() or not p.is_dir():
        return []

    include_set: Set[str] = {str(e).lstrip('.').lower() for e in include_exts}
    if not include_set:
        return []

    results: List[Path] = []
    for child in p.iterdir():
        if child.is_file():
            ext = normalise_ext(child.name)
            if ext in include_set:
                results.append(child.resolve())
    return results
