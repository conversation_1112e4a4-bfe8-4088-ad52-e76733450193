"""
Presenter package for Update Data module.

This package contains the consolidated presenter components:
- state_manager.py: Consolidated state and UI sync management
- file_manager.py: Consolidated file/folder selection and save location logic
- processing_manager.py: File processing logic

Consolidation completed: 6 managers → 3 managers (50% reduction)
"""

# Export the main presenter for backward compatibility
# Note: Since ud_presenter.py imports from this package, we can't import it here
# The presenter is available directly via the module import

__all__ = ['UpdateDataPresenter']