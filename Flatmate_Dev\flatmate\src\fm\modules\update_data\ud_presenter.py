#!/usr/bin/env python3
"""
Update Data presenter implementation.
Coordinates between the view and the data processing pipeline.

This is the main presenter class that has been decomposed into specialized managers:
- StateManager: Handles all presenter state
- WidgetStateManager: Handles UI synchronization
- SourceManager: Handles source selection and folder monitoring
- ArchiveManager: Handles save location and archive options
- ProcessingManager: Handles file processing and events

The presenter now acts primarily as a coordinator between these managers.
"""

from typing import Optional
import pandas as pd

from ...core.services.event_bus import Events, global_event_bus
from ...core.services.logger import log
from ...core.services.folder_monitor_service import folder_monitor_service
from .services.file_info_service import FileInfoService

# InfoBarService imported locally to avoid circular imports
# configure_auto_import imported locally to avoid circular imports
from ..base.base_presenter import BasePresenter
from ._ui.interface import IUpdateDataView
from .config.ud_config import ud_config
from .config.ud_keys import UpdateDataKeys
from .models.config import SaveOptions, SourceOptions
from .services.events import UpdateDataEvents

# MIGRATION: Import local event bus for event-driven architecture
from .services.local_event_bus import update_data_local_bus, ViewEvents, setup_global_event_bridges
# Import event dataclasses for type-safe event handling
from ._ui.ui_events import *

# Import state management from new location
from ._ui._presenter.state_coordinator import UpdateDataState, StateManager
from ._ui._presenter.file_config_manager import FileConfigManager
from ._ui._presenter.file_info_manager import FileInfoManager
from ._ui._presenter.processing_manager import ProcessingManager
from ._ui._presenter.file_discovery_manager import FileDiscoveryManager
from ._ui._presenter.guide_pane_presenter import GuidePanePresenter


class UpdateDataPresenter(BasePresenter):
    """
    Decomposed Update Data presenter.

    This presenter has been refactored from a monolithic class into a coordinated
    system of specialized managers. The presenter now primarily handles:
    - Manager instantiation and dependency injection
    - Signal routing to appropriate managers
    - Inter-manager coordination (temporary - should be refactored)
    - Module lifecycle management

    The actual business logic has been moved to specialized managers:
    - StateManager: All presenter state
    - WidgetStateManager: UI synchronization
    - SourceManager: Source selection and monitoring
    - ArchiveManager: Save location and options
    - ProcessingManager: File processing and events
    """

    def __init__(self, main_window, gui_config, gui_keys):
        """Initialize the decomposed presenter with manager coordination."""
        super().__init__(main_window, gui_config, gui_keys)

        # Set debug log level for Update Data module
        from ...core.services.logger import log

        # Directly set debug level
        log.set_level("DEBUG")
        log.debug("log level set to debug in ud_presenter.py")
        log.debug("UPDATE_DATA: Debug logging enabled for console output")

        # Initialize state manager first
        self.state = UpdateDataState()

        # Initialize services
        from ...gui.services.info_bar_service import InfoBarService
        self.info_bar_service = InfoBarService.get_instance()

        # MIGRATION: Add local event bus for event-driven architecture
        self.local_bus = update_data_local_bus

        # State tracking (legacy - will be migrated to ui_state)
        self.selected_source = None  # Dict containing source files/folder info
        self.save_location = None  # Selected save directory path
        self.job_sheet_dict = {}  # Current job sheet dictionary
        self._updating_source_option = False  # Flag to prevent signal loops

        # View manager for morphic UI
        # self.view_manager = UpdateDataViewManager()  # Missing - in archive

        # Initialize folder monitor service integration
        self.folder_monitor_service = folder_monitor_service
        # File discovery manager for per-folder toggle
        self.file_discovery_manager = FileDiscoveryManager(ud_config, self.folder_monitor_service)
        # Callback will be registered in _connect_signals after source_manager is created

        # SimpleStateCoordinator will be initialized in _connect_signals after view creation
        self.state_coordinator = None

    def _create_view(self) -> IUpdateDataView:
        """Create the view instance. Called once during setup."""
        # Import the concrete implementation locally to avoid circular dependencies
        from ._ui.ud_view import UpdateDataView

        # Return the concrete view, but the presenter will only interact with it
        # through the IUpdateDataView interface.
        view = UpdateDataView(self.main_window, gui_config=self.gui_config, gui_keys=self.gui_keys)
        # Explicit readiness callback to avoid implicit lifecycle timing
        if hasattr(view, 'set_ready_handler'):
            view.set_ready_handler(self._on_view_ready)
        return view

    def _on_view_ready(self) -> None:
        """Called by the view when setup_ui completes."""
        try:
            if getattr(self, 'file_manager', None):
                self.file_manager.ensure_options_initialized()
        except Exception as e:
            from ...core.services.logger import log
            log.error(f"[UpdateDataPresenter] on_view_ready init failed: {e}")

    def _connect_signals(self):
        """Connect view interface signals to handlers. Called once during setup."""
        # Initialize consolidated StateManager now that view is available
        guide_presenter = GuidePanePresenter(self.view, file_discovery_manager=self.file_discovery_manager)
        self.state_manager = StateManager(
            self.view,
            self.info_bar_service,
            self.folder_monitor_service,
            guide_presenter=guide_presenter,
        )
        # Update state reference to use consolidated manager
        self.state = self.state_manager.state

        # Initialize FileInfoManager (The Librarian) before FileConfigManager
        self.file_info_manager = FileInfoManager(
            folder_monitor_service=self.folder_monitor_service,
            local_bus=self.local_bus,
            file_info_service=FileInfoService()
        )

        # Initialize FileConfigManager (The Gatekeeper) with FileInfoManager dependency
        self.file_manager = FileConfigManager(
            self.view,
            self.state_manager,
            self.file_info_manager,  # Pass FileInfoManager for file list operations
            self.local_bus
        )

        # Register folder monitor callback with FileInfoManager
        self.folder_monitor_service.register_callback(self.file_info_manager._on_files_discovered)

        # Initialize ProcessingManager now that view and state_manager are available
        self.processing_manager = ProcessingManager(
            self.view,
            self.state,
            self.info_bar_service,
            self.local_bus,
            state_manager=self.state_manager,
        )

        # Route guide pane toggle intent directly to presenter (no Qt in presenter)
        if hasattr(self.view, 'set_discovery_toggle_handler'):
            self.view.set_discovery_toggle_handler(
                lambda folder, enabled: guide_presenter.on_discovery_toggled(enabled=bool(enabled), folder=folder)
            )

        # Presenter should not connect to Qt signals. Subscribe to typed user-intent events on local bus.
        self.local_bus.subscribe(ViewEvents.CANCEL_REQUESTED.value, lambda _e: self.request_transition("home"))
        self.local_bus.subscribe(ViewEvents.SOURCE_SELECT_REQUESTED.value, self.file_manager.handle_source_select)
        self.local_bus.subscribe(ViewEvents.DESTINATION_SELECT_REQUESTED.value, lambda _e: self.file_manager.handle_save_select())
        self.local_bus.subscribe(ViewEvents.SOURCE_OPTION_CHANGED.value, self.file_manager.handle_source_option_change)
        self.local_bus.subscribe(ViewEvents.SAVE_OPTION_CHANGED.value, self.file_manager.handle_save_option_change)
        self.local_bus.subscribe(ViewEvents.PROCESS_REQUESTED.value, lambda _e: self.processing_manager.handle_process())
        # Ensure presenter never shows dialogs directly; View handles via dialog events
        # Remove any legacy direct calls (none present here by inspection)

        # Database mode change routed via bus (Presenter updates config and UI text)
        self.local_bus.subscribe(ViewEvents.UPDATE_DATABASE_CHANGED.value, self._handle_update_database_change)

        # Initialize save select button state based on default save option
        initial_save_option = self.view.get_save_option()
        is_same_as_source = initial_save_option == SaveOptions.SAME_AS_SOURCE.value
        self.view.set_save_select_enabled(not is_same_as_source)

        self.state_coordinator = None
        setup_global_event_bridges()

        log.debug("Local bus subscriptions set up; Presenter has zero Qt signal knowledge")

        # Subscribe to Update Data global events using processing manager
        global_event_bus.subscribe(
            UpdateDataEvents.FILE_PROCESSING_STARTED.name,
            self.processing_manager.on_processing_started,
        )
        global_event_bus.subscribe(
            UpdateDataEvents.FILE_PROCESSING_STATS.name,
            self.processing_manager.on_processing_stats,
        )
        global_event_bus.subscribe(
            UpdateDataEvents.UNRECOGNIZED_FILES_DETECTED.name,
            self.processing_manager.on_unrecognized_files,
        )
        global_event_bus.subscribe(
            UpdateDataEvents.FILE_PROCESSING_COMPLETED.name,
            self.processing_manager.on_processing_completed,
        )

        # Presenter should not drive view file display; View subscribes to FILE_LIST_UPDATED itself.

        # Wire direct handler for discovery toggle (single-listener; no event indirection)
        if hasattr(self.view, 'set_discovery_toggle_handler'):
            self.view.set_discovery_toggle_handler(self.toggle_file_discovery)

        # Additionally, listen for FILE_LIST_UPDATED to keep presenter state in sync
        # This allows the guide pane to reflect discovered files when user didn't manually select.
        self.local_bus.subscribe(ViewEvents.FILE_LIST_UPDATED.value, self._on_file_list_updated_from_manager)

        # Direct handler wiring for discovery toggle (ensure once)
        if hasattr(self.view, 'set_discovery_toggle_handler'):
            self.view.set_discovery_toggle_handler(self.toggle_file_discovery)

    def _resolve_current_folder(self) -> str:
        """Resolve the active folder based on current state (folder source or files selection)."""
        # Prefer explicit source_path when set
        if getattr(self.state, 'source_path', ''):
            return self.state.source_path
        # Infer from first selected file
        files = getattr(self.state, 'selected_files', []) or []
        if files:
            import os
            return os.path.dirname(files[0])
        return ""


    def toggle_file_discovery(self, folder: str | None, enabled: bool) -> None:
        """Direct interface API from view: set per-folder discovery preference.

        Args:
            folder: target folder; if None/empty, we'll resolve from current state.
            enabled: desired discovery state.
        """
        resolved = str(folder or "")
        if not resolved:
            resolved = self._resolve_current_folder()
            if not resolved:
                log.warning("[UpdateDataPresenter] toggle_file_discovery: no folder resolved; ignoring")
                return
        log.debug(
            f"[UpdateDataPresenter] toggle_file_discovery: request folder='{folder}', resolved='{resolved}', enabled={bool(enabled)}"
        )
        self.file_discovery_manager.set_discovery(resolved, bool(enabled))
        log.debug(
            f"[UpdateDataPresenter] FDM.set_discovery done: folder='{resolved}', enabled={bool(enabled)}"
        )
        log.debug("[UpdateDataPresenter] syncing state to view after discovery toggle")
        self.state_manager.sync_state_to_view()

    def _refresh_content(self, **params):
        """Refresh update data content when shown.

        This method is called every time the module becomes visible.
        It handles view state setup and configuration.

        Args:
            **params: Optional parameters passed from navigation
        """
        log.debug("Refreshing UpdateData content")

        # Set the source option from config to remember user's last choice
        last_source_option = ud_config.get_value(
            UpdateDataKeys.Source.LAST_SOURCE_OPTION, default=SourceOptions.SELECT_FOLDER.value
        )
        self.view.set_source_option(last_source_option)

        # Configure view based on database mode using interface methods
        is_database_mode = self.view.get_update_database()
        # TODO: is_data_base... not accessed?
        # Set initial process button state
        self.view.set_process_button_text(self.state.process_button_text)

        # Explicitly control panel visibility - presenter is responsible for UI state
        self.main_window.show_left_panel()

        # Show the InfoBar with appropriate message
        self.info_bar_service.show()
        self.info_bar_service.publish_message(
            "Select source files or folder to begin.", "INFO"
        )

        # Populate options AFTER view widgets are ready, but only if not already initialized
        try:
            if getattr(self, 'file_manager', None):
                self.file_manager.ensure_options_initialized()
        except Exception as e:
            log.error(f"[UpdateDataPresenter] Conditional post-view refresh failed: {e}")

        self._setup_view_from_config()

        # Sync initial state to view (including guide pane)
        self.state_manager.sync_state_to_view()

        # Because modules are eager-loaded, the monitor may already be running.
        # Request a batched rescan of all monitored folders now that the view is ready,
        # so the file list and guide pane reflect current on-disk files immediately.
        try:
            from PySide6.QtCore import QTimer
            QTimer.singleShot(0, lambda: self.folder_monitor_service.trigger_rescan_all_monitored())
        except Exception:
            log.debug("[UpdateDataPresenter] Could not schedule rescan; view may still be updated by later events")

        log.debug("UpdateData content refresh complete")

    def _handle_guide_pane_message(self, message: str):
        """Handle guide pane messages including monitor folder checkbox changes."""
        if message.startswith("checkbox_changed:monitor_folder:"):
            # Extract the checkbox state (0 = unchecked, 2 = checked)
            state = message.split(":")[-1]
            is_checked = state == "2"

            # Update the monitor folder setting
            self.source_manager.handle_monitor_folder_change(is_checked)

            log.debug(f"Monitor folder checkbox changed: {is_checked}")

    def _handle_update_database_change(self, checked: bool):
        """Handle database update checkbox state change - drives UI morphing."""
        # Store the state in configuration
        ud_config.set_value(UpdateDataKeys.Database.UPDATE_DATABASE, checked)

        # Presenter may make interface calls, but must not open dialogs directly.
        # Keep UI morphing via interface methods only.
        if checked:
            # Align with interface: let the View own button text
            self.view.set_process_button_text("Update Database")
            status_msg = "Database mode: Files will be imported to database"
        else:
            self.view.set_process_button_text("Process Files")
            status_msg = "File utility mode: Files will be processed without database updates"

        # Also update save select enabled state if archive option implies so
        try:
            is_same_as_source = self.view.get_save_option() == SaveOptions.SAME_AS_SOURCE.value
            self.view.set_save_select_enabled(not is_same_as_source)
        except Exception as _e:
            # Non-fatal: interface availability can vary during startup
            pass

        self.info_bar_service.publish_message(status_msg)

    def _handle_add_files_request(self):
        """Handle request to add files from file pane (deprecated direct call).

        Event-first architecture: This method should not be invoked anymore.
        UDFileView emits add_files_requested -> UpdateDataView translates to
        Local Bus SOURCE_SELECT_REQUESTED with SELECT_FILES. FileConfigManager handles it.
        """
        try:
            # Publish the canonical intent on the local event bus instead of calling dialogs directly
            self.local_bus.emit(ViewEvents.SOURCE_SELECT_REQUESTED.value, SourceOptions.SELECT_FILES)
            log.debug("[PRESENTER] Redirected add files request to Local Bus (SOURCE_SELECT_REQUESTED:SELECT_FILES)")
        except Exception as e:
            log.error(f"Error redirecting add files request to Local Bus: {e}")

    # Removed: _on_file_list_updated. View updates file display directly from FILE_LIST_UPDATED.

    def cleanup(self):
        """Clean up before being replaced."""
        # Unsubscribe from events using processing manager
        global_event_bus.unsubscribe(
            UpdateDataEvents.FILE_PROCESSING_STARTED.name, self.processing_manager.on_processing_started
        )
        global_event_bus.unsubscribe(
            UpdateDataEvents.FILE_PROCESSING_STATS.name, self.processing_manager.on_processing_stats
        )
        global_event_bus.unsubscribe(
            UpdateDataEvents.UNRECOGNIZED_FILES_DETECTED.name,
            self.processing_manager.on_unrecognized_files,
        )
        global_event_bus.unsubscribe(
            UpdateDataEvents.FILE_PROCESSING_COMPLETED.name,
            self.processing_manager.on_processing_completed,
        )

        self.info_bar_service.hide()

        if self.view:
            self.view.cleanup()

    def _setup_view_from_config(self):
        """Set up view based on configuration."""
        # Set default save location
        self.save_location = ud_config.get_value("master")
        self.info_bar_service.publish_message(f"Save location: {self.save_location}")

        # Load recent masters
        recent_masters = ud_config.get_pref("recent_masters", default=[])
        if recent_masters:
            # TODO: Add recent masters to view
            pass


    def _on_file_list_updated_from_manager(self, event_data) -> None:
        """Keep presenter state aligned when files are discovered/added.

        Ensures the guide pane shows the correct folder and file count
        even when the user hasn't manually selected files/folder.
        """
        try:
            # Extract enriched list and source_path from dataclass or dict
            files_info = []
            source_path = ""
            if hasattr(event_data, 'files'):
                files_info = getattr(event_data, 'files', []) or []
                source_path = getattr(event_data, 'source_path', "") or ""
            elif isinstance(event_data, dict):
                files_info = event_data.get('files', []) or []
                source_path = event_data.get('source_path', "") or ""

            # Derive plain file paths from enriched objects when possible
            file_paths = []
            for item in files_info:
                try:
                    # FileInfoData has .path; fall back to string
                    path = getattr(item, 'path', None)
                    file_paths.append(path if path else str(item))
                except Exception:
                    pass

            # If we have no files, do not alter state
            if not file_paths:
                return

            # Prefer provided source_path; else infer from first file
            if not source_path:
                import os
                try:
                    source_path = os.path.dirname(file_paths[0])
                except Exception:
                    source_path = ""

            # Update state to 'folder' with current files
            if source_path:
                self.state_manager.set_source_folder(source_path, files=file_paths)
            else:
                # As a fallback, we can mark as files selection
                self.state_manager.set_source_files(file_paths)

            # Push state to view on the UI thread (avoid cross-thread UI updates)
            try:
                from PySide6.QtCore import QTimer
                QTimer.singleShot(0, lambda: self.state_manager.sync_state_to_view())
            except Exception:
                # Fallback: log only; UDView also updates guide summary directly from files_data
                log.debug("[UpdateDataPresenter] QTimer scheduling failed; UI may already be updated by view")
        except Exception as e:
            log.error(f"[UpdateDataPresenter] Failed to sync state from FILE_LIST_UPDATED: {e}")
