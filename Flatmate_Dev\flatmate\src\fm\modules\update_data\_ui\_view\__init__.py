"""
View components for the Update Data module.

This package provides a modular, component-based UI for the Update Data module.
Components are organized into the following categories:

- _common: Base components and core structural elements
- center_panel: Center panel components for main content display
- left_panel: Left panel components for navigation
- right_panel: Right panel components for settings and options
- utils: Helper utilities for the view components
"""

# Import panel layouts normally - circular imports resolved
from .center_panel_layout import CenterPanelLayout
from .left_panel_layout import LeftPanelLayout
from .right_panel_layout import RightPanelLayout

__all__ = [
    'CenterPanelLayout',
    'LeftPanelLayout',
    'RightPanelLayout',
]

# Backwards compatibility: temporary aliases for previous class names
# TODO: remove after dependent code updated
CenterPanelManager = CenterPanelLayout
LeftPanelManager = LeftPanelLayout
RightPanelManager = RightPanelLayout
