# Implementation Review Protocol

**Version**: 1.0  
**Date**: 2025-01-22
**Status**: ACTIVE  
**Purpose**: Mid-sprint implementation reviews with update-in-place approach

## 🎯 **Protocol Purpose**

Provide a structured approach for mid-sprint implementation reviews that update existing documentation rather than creating nested folders. This protocol focuses on **iterative refinement** and planning the "next assault" on implementation challenges.

## 📋 **Implementation Review Checklist (10-15 minutes)**

### **Phase 1: Review Current Implementation (5 minutes)**

#### ✅ **1.1 Assess Implementation Progress**
**Time**: 3 minutes

**AI Actions**:
- [ ] Review code changes since last implementation review
- [ ] Identify working and non-working components
- [ ] Evaluate implementation against original design
- [ ] Note any deviations or technical challenges

#### ✅ **1.2 Document Technical Insights**
**Time**: 2 minutes

**AI Actions**:
- [ ] Document any architectural insights gained
- [ ] Note patterns that worked or didn't work
- [ ] Record performance observations
- [ ] Identify integration points that need attention

### **Phase 2: Update Existing Documentation (5-7 minutes)**

#### ✅ **2.1 Update Implementation Guide**
**Location**: `DOCS/_FEATURES/<feature_name>/IMPLEMENTATION_GUIDE.md`
**Time**: 3 minutes

**AI Actions**:
- [ ] Update technical implementation details
- [ ] Revise approach based on new insights
- [ ] Modify integration instructions if needed
- [ ] Update any code examples

#### ✅ **2.2 Create Implementation Review Document**
**Location**: `DOCS/_FEATURES/<feature_name>/implementation_review_<n>.md`
**Time**: 3 minutes

**AI Actions**:
- [ ] Document current implementation state
- [ ] Note lessons learned and insights
- [ ] Plan next implementation approach
- [ ] List specific tasks for next implementation attempt

### **Phase 3: Plan Next Implementation Attempt (3 minutes)**

#### ✅ **3.1 Define Next Steps**
**Time**: 3 minutes

**AI Actions**:
- [ ] Identify specific tasks for next implementation attempt
- [ ] Prioritize tasks based on dependencies
- [ ] Estimate effort for each task
- [ ] Set clear success criteria for next attempt

## 📄 **Implementation Review Template**

```markdown
# Implementation Review <n> - <Feature Name>

**Date**: <Full Date>
**Implementation Attempt**: <n>
**Status**: <IN_PROGRESS/BLOCKED/READY_FOR_TESTING>

## 🎯 **Current Implementation State**

### What's Working ✅
- [x] Component 1 - <Brief description>
- [x] Component 2 - <Brief description>

### What's Not Working ❌
- [ ] Component 3 - <Issue description>
- [ ] Component 4 - <Issue description>

### Technical Challenges
- **Challenge 1**: <Description and current approach>
- **Challenge 2**: <Description and current approach>

## 🧠 **Implementation Insights**

### What We've Learned
- **Insight 1**: <Technical insight and implications>
- **Insight 2**: <Technical insight and implications>

### Architectural Considerations
- **Consideration 1**: <Architectural insight>
- **Consideration 2**: <Architectural insight>

### Approach Evaluation
- **What Worked**: <Successful approaches>
- **What Didn't Work**: <Unsuccessful approaches>
- **What We Should Try Next**: <New approaches to consider>

## 🚀 **Next Implementation Attempt**

### Approach Adjustments
- **Adjustment 1**: <What will change in next attempt>
- **Adjustment 2**: <What will change in next attempt>

### Specific Tasks
1. **Task 1**: <Specific implementation task>
2. **Task 2**: <Specific implementation task>
3. **Task 3**: <Specific implementation task>

### Success Criteria
- **Criterion 1**: <How we'll know this attempt succeeded>
- **Criterion 2**: <How we'll know this attempt succeeded>

## 🔧 **Technical Details**

### Code Changes Since Last Review
- `path/to/file1.py` - <What changed>
- `path/to/file2.py` - <What changed>

### New Dependencies/Requirements
- **Dependency 1**: <New dependency and purpose>
- **Dependency 2**: <New dependency and purpose>

### Integration Points
- **Integration 1**: <Description and status>
- **Integration 2**: <Description and status>

---

**Next Implementation Focus**: <Brief description of next implementation focus>
```

## 📁 **File Naming Convention**

### **Format**: `implementation_review_<n>.md`

### **Examples**:
- `implementation_review_1.md` - First implementation review
- `implementation_review_2.md` - Second implementation review
- `implementation_review_3.md` - Third implementation review

## 🚀 **Usage Instructions**

### **When to Use**:
- During mid-sprint implementation work
- After encountering significant technical challenges
- When changing implementation approach
- Before starting next implementation attempt

### **When NOT to Use**:
- For end-of-sprint reviews (use End-of-Sprint Protocol instead)
- For individual chat session handovers (use Chat Handover Protocol instead)
- For minor code changes (use session notes instead)

## ✅ **Quality Gates**

### **Before Completing Protocol**:
- [ ] Current implementation state documented
- [ ] Implementation guide updated
- [ ] Implementation review document created
- [ ] Next implementation approach planned
- [ ] Specific tasks identified for next attempt

## 📊 **Success Metrics**

### **Documentation Quality**:
- Clear understanding of current implementation state
- Actionable plan for next implementation attempt
- Updated implementation guide with latest insights
- Specific tasks identified for next steps

### **Efficiency**:
- Protocol completed in under 15 minutes
- Documentation updated in place (not nested folders)
- Next implementation can begin immediately with clear direction

---

**This protocol ensures iterative implementation refinement with clear documentation and planning.**
