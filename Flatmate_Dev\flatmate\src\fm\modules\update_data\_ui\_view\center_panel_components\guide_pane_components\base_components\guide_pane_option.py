from __future__ import annotations

from typing import Optional, Callable

from PySide6.QtWidgets import QWidget, QHBoxLayout

from fm.gui._shared_components.widgets.checkboxes import LabeledCheckBox


class GuidePaneOption(QWidget):
    """Thin wrapper over shared LabeledCheckBox.

    Public API:
    - set_text(text)
    - set_checked(checked)  # blocks signals during programmatic update
    - is_checked() -> bool
    - connect_toggled(cb)
    - set_info_tooltip(text)
    """

    def __init__(self, text: str = "", parent: Optional[QWidget] = None) -> None:
        super().__init__(parent)
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        self._lcb = LabeledCheckBox(text or "")
        layout.addWidget(self._lcb)

    # --- Public API ---
    def set_text(self, text: str) -> None:
        self._lcb.checkbox.setText(text or "")

    def set_checked(self, checked: bool) -> None:
        # Prevent feedback loops on programmatic updates
        cb = self._lcb.checkbox
        prev = cb.blockSignals(True)
        try:
            self._lcb.set_checked(bool(checked))
        finally:
            cb.blockSignals(prev)

    def is_checked(self) -> bool:
        return self._lcb.is_checked()

    def connect_toggled(self, cb: Callable[[bool], None]) -> None:
        # LabeledCheckBox exposes a boolean signal already
        self._lcb.state_changed.connect(cb)

    def set_info_tooltip(self, text: str) -> None:
        # Apply tooltip to the checkbox itself
        self._lcb.checkbox.setToolTip(text or "")
        
