# Window Title Bar Options (PySide6, Cross-Platform)

Status: Draft  
Last Updated: 2025-08-05  
Scope: Strategies to handle the OS title bar (color, visibility) without adopting complex frameless windows. Machine-dependent, pragmatic options for Windows/macOS/Linux.

Objectives
- Avoid the previous complexity of frameless/custom chrome.
- Provide native, reliable ways to remove or darken the white title bar.
- Keep solutions minimal, with platform-appropriate behavior.

Quick Decision Matrix
- Need zero white bar now → Use native Full Screen (all OS)
- Improve windowed-mode title bar on Windows → Enable Immersive Dark Mode (no frameless)
- macOS windowed-mode → Follows system light/dark automatically
- Keep consistent UX → Offer a Toggle Full Screen action in the app

Cross-Platform Options

1) Native Full Screen (recommended baseline)
- Works on Windows/macOS/Linux with no custom chrome.
- Removes the OS title bar while full screen is active.
- API:
  - Enter: main_window.showFullScreen()
  - Exit: main_window.showNormal()
- Add a toggle action (menu item + keyboard shortcut) to switch.

2) Windows: Immersive Dark Mode (windowed mode)
- Darkens the native title bar to match dark palettes without frameless.
- Uses DwmSetWindowAttribute; requires window handle (winId()).
- Minimal platform-specific code via ctypes (Windows-only).
- Limitations: toggles dark vs light; not arbitrary color.

3) macOS: System Appearance
- macOS respects system light/dark mode in windowed mode.
- If the app uses a dark palette, macOS renders a dark title bar automatically.
- Full screen is seamless and recommended for hiding title bar fully.

4) Linux
- WM/DE dependent. Full screen works consistently.
- Title bar color follows the system theme; custom darkening may not be uniformly possible without custom chrome.

Suggested Implementation

A) Toggle Full Screen (all OS)
- Provide a single method on your main window:
```python
def toggle_full_screen(self) -> None:
    if self.isFullScreen():
        self.showNormal()
    else:
        self.showFullScreen()
```
- Bind to a shortcut (e.g., F11) and a menu item View → Toggle Full Screen.

B) Windows Immersive Dark Mode (optional)
- Guard at runtime (sys.platform == "win32").
- Pseudocode outline:
```python
# windows_dark_title_bar.py (example helper)
import ctypes
from ctypes import wintypes

DWMWA_USE_IMMERSIVE_DARK_MODE = 20  # 19 on older builds

def enable_immersive_dark_mode(hwnd: int, enabled: bool) -> bool:
    try:
        dwmapi = ctypes.windll.dwmapi
        attribute = DWMWA_USE_IMMERSIVE_DARK_MODE
        value = wintypes.BOOL(1 if enabled else 0)
        res = dwmapi.DwmSetWindowAttribute(
            wintypes.HWND(hwnd),
            ctypes.c_uint(attribute),
            ctypes.byref(value),
            ctypes.sizeof(value)
        )
        return res == 0
    except Exception:
        return False
```
- Usage:
```python
if sys.platform == "win32":
    hwnd = int(main_window.winId())
    enable_immersive_dark_mode(hwnd, True)
```
- Apply once on window creation or when changing theme.

Rationale for Avoiding Frameless
- Previous attempts were complex and brittle (drag/resize hit-testing, system menu behaviors, platform divergences).
- Native full-screen and OS-supported title bar options provide 90% of the UX benefit with 10% of the effort.
- Consistency with QSS styling remains intact for the rest of the UI.

UX Recommendations
- Provide a visible Full Screen toggle in the UI with a standard shortcut (F11).
- Persist full screen preference per user/session (optional).
- On Windows with dark app theme, attempt immersive dark mode for windowed usage; silently fallback if unsupported.

Testing Checklist
- Windows 10/11:
  - Full screen: title bar gone, content sized correctly.
  - Immersive dark mode: title bar dark in windowed mode (if API succeeds).
- macOS:
  - Full screen: smooth enter/exit, title bar hides.
  - Windowed mode: title bar follows system appearance.
- Linux:
  - Full screen: behaves as expected across common DEs (GNOME/KDE).
  - Windowed mode: follows system theme.

References
- Styling system (QSS): [flatmate_consolidated.qss](flatmate/src/fm/gui/styles/flatmate_consolidated.qss)
- Windows DWM attributes (MS docs)
- PySide6 QWidget APIs: showFullScreen(), showNormal()

Open Questions / Future Work
- Expose theme toggles and pair with Windows dark title bar toggling.
- Consider documenting a lightweight wrapper for immersive dark mode as part of a platform utilities module.
- If we revisit custom chrome, evaluate qframelesswindow to reduce complexity (only if requirements demand it).