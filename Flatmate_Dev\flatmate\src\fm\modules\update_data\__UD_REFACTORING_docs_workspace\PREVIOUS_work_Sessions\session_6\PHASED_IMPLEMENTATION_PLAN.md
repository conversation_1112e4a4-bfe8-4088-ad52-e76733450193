# 🚀 Phased Implementation Plan: Update Data Runtime Fixes
**Date**: 2025-07-31  
**Architect**: <PERSON>  
**Context**: Three-phase approach to fix runtime errors  
**Priority**: Phase 1 Critical, Phase 2 High, Phase 3 Enhancement  

## Overview

This plan addresses runtime errors through **progressive enhancement** rather than breaking changes, allowing the module to work immediately while building toward the complete architectural vision.

## 🚨 PHASE 1: EMERGENCY FIXES (30 minutes)
**Goal**: Get the module working right now with minimal changes  
**Risk**: Low  
**Impact**: High  
**Priority**: 🔥 **CRITICAL - DO IMMEDIATELY**

### Fix 1A: Emergency Guide Pane Method (5 minutes)
**Problem**: `AttributeError: 'UpdateDataView' object has no attribute 'set_guide_content'`  
**Solution**: Add missing method as simple delegation

**File**: `ud_view.py` (around line 333)
```python
def set_guide_content(self, content: str) -> None:
    """Emergency compatibility method - delegates to guide pane display."""
    if hasattr(self, 'guide_pane') and self.guide_pane:
        self.guide_pane.display(content, 'text')
```

### Fix 1B: Enhanced Option String Translation (10 minutes)
**Problem**: `[WARNING] Unknown selection type: Select individual files...`  
**Solution**: Enhanced string mapping in FileManager

**File**: `_presenter/file_manager.py` (around line 80)
```python
def handle_source_select(self, selection_type):
    """Handle source selection request from the view."""
    log.debug(f"Source selection requested: {selection_type}")

    # Enhanced mapping for UI strings to internal types
    if (selection_type == 'files' or 
        selection_type == SourceOptions.SELECT_FILES or
        selection_type == "Select individual files..."):
        self._select_files()
    elif (selection_type == 'folder' or 
          selection_type == SourceOptions.SELECT_FOLDER or
          selection_type == "Select entire folder..."):
        self._select_folder()
    else:
        log.warning(f"Unknown selection type: {selection_type}")
        return
```

### Fix 1C: InfoBar Service Method
**Status**: ✅ **Already Fixed** - User changed `show_info()` to `publish_message()`

### Phase 1 Testing
**Expected Results**:
- ❌ No more `AttributeError: set_guide_content`
- ❌ No more "Unknown selection type" warnings  
- ✅ Module loads and functions without crashes
- ✅ Basic file/folder selection works

---

## ⚡ PHASE 2: PROPER GUIDE PANE INTEGRATION (2 hours)
**Goal**: Implement intended state-driven guide pane behavior from USER_FLOW_v4  
**Risk**: Medium  
**Impact**: High  
**Priority**: ⚡ **HIGH - NEXT SESSION**

### Fix 2A: StateManager Guide Pane Updates (45 minutes)
**Goal**: Replace simple text with proper state-driven guide pane calls

**Current Issue**: StateManager calls `set_guide_content()` with static text  
**Solution**: Use guide pane states that match USER_FLOW_v4 document

**File**: `_presenter/state_manager.py`

**Replace `update_guide_pane_for_folder()` method**:
```python
def update_guide_pane_for_folder(self) -> None:
    """Update guide pane for folder source type using states."""
    try:
        if hasattr(self.view, 'guide_pane') and self.view.guide_pane:
            folder_path = getattr(self.state, 'selected_folder', self.state.source_path)
            file_count = len(getattr(self.state, 'selected_files', []))
            
            # Use proper state with context
            self.view.guide_pane.set_state('folder_selected', {
                'path': folder_path,
                'count': file_count
            })
            
            # Show folder monitoring option if ready
            if self.state.can_process:
                self.view.guide_pane.show_folder_monitoring_option(
                    enabled=self.folder_monitor_service.is_monitoring()
                )
    except Exception as e:
        log.error(f"Error updating folder guide: {e}")
```

**Replace `update_guide_pane_for_files()` method**:
```python
def update_guide_pane_for_files(self) -> None:
    """Update guide pane for files source type using states."""
    try:
        if hasattr(self.view, 'guide_pane') and self.view.guide_pane:
            file_count = len(self.state.selected_files)
            
            # Use proper state with context  
            self.view.guide_pane.set_state('files_selected', {
                'count': file_count
            })
    except Exception as e:
        log.error(f"Error updating files guide: {e}")
```

### Fix 2B: View Interface Methods (45 minutes)
**Goal**: Add proper interface methods to UpdateDataView

**File**: `ud_view.py` (add after line 333)
```python
def set_guide_state(self, state: str, context: Optional[Dict[str, Any]] = None) -> None:
    """Set guide pane state with context - proper interface method."""
    if hasattr(self, 'guide_pane') and self.guide_pane:
        self.guide_pane.set_state(state, context)

def display_guide_content(self, content: str, content_type: str = 'text') -> None:
    """Display content in guide pane - proper interface method."""
    if hasattr(self, 'guide_pane') and self.guide_pane:
        self.guide_pane.display(content, content_type)
```

### Fix 2C: Update StateManager Default Case (30 minutes)
**Goal**: Use proper state for default guide pane updates

**File**: `_presenter/state_manager.py` (around line 154)
```python
def update_guide_pane(self) -> None:
    """Update guide pane content based on current state."""
    try:
        if self.state.source_type == 'folder':
            self.update_guide_pane_for_folder()
        elif self.state.source_type == 'files':
            self.update_guide_pane_for_files()
        else:
            # Default guide content - use proper state method
            if hasattr(self.view, 'guide_pane') and self.view.guide_pane:
                self.view.guide_pane.set_state('initial')
    except Exception as e:
        log.error(f"Error updating guide pane: {e}")
```

### Phase 2 Testing
**Expected Results**:
- ✅ **Rich contextual guidance** matches USER_FLOW_v4
- ✅ **State-driven messages**: "Found [X] CSV files ready for processing"
- ✅ **Progressive updates** as user makes selections
- ✅ **Interactive options** appear correctly (monitor folder checkbox)
- ✅ **Proper styling** and visual states

---

## 🏗️ PHASE 3: COMPLETE ARCHITECTURAL SOLUTION (3 hours)
**Goal**: Full interface contracts and service definitions  
**Risk**: Low  
**Impact**: Medium  
**Priority**: 📈 **ENHANCEMENT - FUTURE SESSION**

### Fix 3A: Interface Protocol Updates (1 hour)
**Goal**: Add guide pane methods to IUpdateDataView protocol

**File**: `interface/i_view_interface.py`
```python
class IUpdateDataView(Protocol):
    # === GUIDE PANE MANAGEMENT ===
    def set_guide_state(self, state: str, context: Optional[Dict[str, Any]] = None) -> None:
        """Set guide pane state with context."""
        ...
    
    def display_guide_content(self, content: str, content_type: str = 'text') -> None:
        """Display content in guide pane."""
        ...
```

### Fix 3B: Service Interface Definitions (1 hour)
**Goal**: Create proper service interfaces with type hints

**File**: `services/interfaces.py` (new file)
```python
class IInfoBarService(Protocol):
    def publish_message(self, message: str, level: str = 'info') -> None: ...
    def show_error(self, message: str) -> None: ...
    def clear_messages(self) -> None: ...

class IFolderMonitorService(Protocol):
    def is_monitoring(self) -> bool: ...
    def start_monitoring(self, path: str) -> None: ...
    def stop_monitoring(self) -> None: ...
```

### Fix 3C: Translation Layer Implementation (1 hour)
**Goal**: Proper abstraction between UI strings and business logic

**File**: `services/option_translator.py` (new file)
```python
class OptionTranslator:
    """Translates between UI display strings and internal types."""
    
    UI_TO_INTERNAL = {
        SourceOptions.SELECT_FILES: 'files',
        SourceOptions.SELECT_FOLDER: 'folder'
    }
    
    @classmethod
    def translate_source_option(cls, ui_option: str) -> str:
        return cls.UI_TO_INTERNAL.get(ui_option, ui_option.lower())
```

---

## 📋 IMPLEMENTATION PRIORITY MATRIX

| **Phase** | **Time** | **Risk** | **Impact** | **Dependencies** | **Priority** |
|-----------|----------|----------|------------|------------------|--------------|
| **Phase 1** | 30 min | Low | High | None | 🔥 **CRITICAL** |
| **Phase 2** | 2 hours | Medium | High | Phase 1 Complete | ⚡ **HIGH** |
| **Phase 3** | 3 hours | Low | Medium | Phase 2 Complete | 📈 **ENHANCEMENT** |

## Next Steps

1. **Implement Phase 1 immediately** (30 minutes)
2. **Test module functionality** - should work without errors
3. **Schedule Phase 2** for next development session (2 hours)
4. **Consider Phase 3** for future architectural enhancement (3 hours)

**Total Implementation Time**: 5.5 hours across three phases  
**Immediate Benefit**: Module works without crashes (Phase 1)  
**Full Benefit**: Rich user experience matching USER_FLOW_v4 (Phase 2)
