# Data Flow

Scope: Full project. Hybrid style with targeted references.

## Overview
Flatmate processes raw financial statement data into standardized structures, persists to a local database, and provides interactive GUI workflows for ingestion and analysis.

Main stages:
1) Ingestion (files → DataFrames)
2) Normalization (standard columns)
3) Transformation/metadata enrichment
4) Persistence (DB write/update)
5) Query and presentation (GUI table views)
6) User actions (filter/search/export), iterative

## Key Paths
- Update Data module: flatmate/src/fm/modules/update_data/
- Standards: flatmate/src/fm/core/data_services/standards/
- Database: flatmate/src/fm/core/database/
- Query processing: flatmate/src/fm/core/services/query_processing/

## Stage Details

1) Ingestion
- Source: CSVs or other supported formats
- Update Data handlers read input, infer headers, and map to target standard columns
- Behavior flags (e.g., has headers, column name row) determine mapping logic

2) Normalization to Standards
- Use canonical column names and types from standards
- Ensure consistent schema across different bank/provider formats
- Add derived columns where necessary (e.g., debit/credit split from amount)

3) Enrichment
- Optional metadata: source UID, date inferred from filename or last transaction, account mapping, tags
- Logging of anomalies and normalization decisions for traceability

4) Persistence
- DB schema owned in flatmate/src/fm/core/database/
- Repositories mediate persistence
- Migrations and SQL repository support schema evolution and queries

5) Query and Presentation
- Query processing utilities support hybrid and targeted queries
- GUI table view v2 renders results with toolbars, filters, and search
- Column visibility and order are configurable; user preferences remembered via config

6) User Actions & Feedback
- Export current view (CSV/XLSX planned)
- Filtering by date, category, account, tags
- UI status and info bars provide feedback; logs capture processing info

## Event Flow Integration
- Event bus (flatmate/src/fm/core/services/event_bus.py) is the recommended backbone for decoupled actions
- Navigation and data refresh events should be published/subscribed rather than direct calls

## Error Handling and Logging
- Centralized logging with debug/info/warn/error levels
- Aim to reduce overly verbose terminal output in production mode
- Consider structured logs for ingestion and DB writes

## Performance Considerations
- Prefer targeted queries vs full dataset where practical
- Cache or memoize expensive operations when possible
- Keep UI responsive by deferring heavy processing off the main thread when needed

## Future Enhancements
- Column management system with explicit sets (Required, DefaultVisible, UserModified)
- Consistent account naming layer and mapping
- Export pipeline integrated with query and visible column configuration
- Event-driven module transitions to reduce coupling further