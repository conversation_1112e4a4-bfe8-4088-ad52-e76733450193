"""
Guide Pane Presenter

Centralises guide message/state logic with zero Qt coupling.
Peers (other managers) call explicit methods on state changes.
"""
from __future__ import annotations
from typing import Optional

from fm.core.services.logger import log
from ..interface.i_view_interface import IUpdateDataView
from .file_discovery_manager import FileDiscoveryManager


class GuidePanePresenter:
    """Presenter for the Guide Pane.

    Keeps UI copy accurate and consistent. No Qt imports. No event bus.
    """

    def __init__(self, view: IUpdateDataView, file_discovery_manager: FileDiscoveryManager | None = None):
        self.view = view
        self._fdm = file_discovery_manager
        # Local snapshot of context to support refresh() if desired
        self._last_folder: Optional[str] = None
        self._last_listed_count: int = 0
        self._discovery_enabled: bool = False
        self._save_same_as_source: Optional[bool] = None
        self._save_path: Optional[str] = None

        log.debug("[GuidePanePresenter] Initialised")

    # === Public API – called by peer managers ===
    def on_files_listed(self, *, count: int, folder: Optional[str], source_type: str) -> None:
        """Called after canonical file list is updated.
        count: number of files listed
        folder: folder path if source is a folder, else None
        source_type: 'folder' | 'files'
        """
        self._last_folder = folder
        self._last_listed_count = max(0, int(count))

        # Compose clearer copy. If discovery is disabled and a folder is known,
        # avoid misleading "Listed 0 file(s)" and nudge the user to enable.
        if folder and not self._discovery_enabled:
            # No section notice anymore; keep banner neutral
            msg = f"Listed {self._last_listed_count} file(s)"
        elif folder:
            msg = f"Listed {self._last_listed_count} file(s) from '{folder}'"
        else:
            msg = f"Listed {self._last_listed_count} file(s)"
        # Always clear any prior section notice; tooltip carries the info now
        try:
            self.view.guide_clear_source_notice()
        except Exception:
            pass
        # Do not use the main banner for list updates; keep it clear to avoid hiding section notices
        try:
            self.view.guide_display("")  # clear banner
        except Exception:
            pass
        self.view.guide_set_status('info')
        self.view.guide_set_actions_enabled(process_enabled=(self._last_listed_count > 0), reset_enabled=True)

        # Keep Source section line consistent with context
        if folder:
            # Section already titled "Source"; avoid repeating the label
            self.view.guide_set_source_summary(f"{folder}")
        else:
            self.view.guide_set_source_summary("No files or folders selected…")

        # Re-emit archive summary if known
        self._apply_archive_summary()

        # Fetch persisted discovery state when folder context is known
        if folder and self._fdm:
            try:
                self._discovery_enabled = bool(self._fdm.get_discovery(folder))
            except Exception as e:
                log.error(f"[GuidePanePresenter] get_discovery failed for '{folder}': {e}")

        # Offer source options in context (e.g., auto-queue) when folder is known
        if folder:
            self.view.guide_show_source_context_options(discovery_enabled=self._discovery_enabled, folder=folder)

        log.debug(f"[GuidePanePresenter] on_files_listed: count={count}, folder={folder}, source_type={source_type}")

    def on_save_option_changed(self, *, same_as_source: bool, save_path: Optional[str] = None) -> None:
        """Called when save option changes in the UI."""
        self._save_same_as_source = bool(same_as_source)
        self._save_path = save_path
        self._apply_archive_summary()
        log.debug(
            f"[GuidePanePresenter] on_save_option_changed: same_as_source={same_as_source}, save_path={save_path}"
        )

    def on_discovery_toggled(self, *, enabled: bool, folder: Optional[str]) -> None:
        """Called when discovery/auto-queue is toggled for a folder."""
        self._discovery_enabled = bool(enabled)
        # Persist and reconcile monitoring via FileDiscoveryManager, if provided
        if folder and self._fdm:
            try:
                self._fdm.set_discovery(folder, bool(enabled))
                log.debug(f"[GuidePanePresenter] Persisted discovery via FDM: folder='{folder}', enabled={enabled}")
            except Exception as e:
                log.error(f"[GuidePanePresenter] set_discovery failed for '{folder}': {e}")
        log.debug(f"[GuidePanePresenter] on_discovery_toggled: folder='{folder}', enabled={enabled}")
        if folder:
            self.view.guide_show_source_context_options(discovery_enabled=enabled, folder=folder)
            # No section notice; always clear
            try:
                self.view.guide_clear_source_notice()
            except Exception:
                pass
        log.debug(f"[GuidePanePresenter] on_discovery_toggled: enabled={enabled}, folder={folder}")

    def on_processing_started(self, *, total: int) -> None:
        """Called when processing starts."""
        total = max(0, int(total))
        self.view.guide_display(f"Processing {total} file(s)…")
        self.view.guide_set_status('processing')
        self.view.guide_set_actions_enabled(process_enabled=False, reset_enabled=False)
        log.debug(f"[GuidePanePresenter] on_processing_started: total={total}")

    def on_processing_completed(self, *, success_count: int, fail_count: int) -> None:
        """Called when processing completes."""
        success_count = max(0, int(success_count))
        fail_count = max(0, int(fail_count))
        total = success_count + fail_count

        if total == 0:
            self.view.guide_display("Nothing to process.")
            self.view.guide_set_status('warning')
        elif fail_count == 0:
            self.view.guide_display(f"Successfully processed {success_count} file(s)")
            self.view.guide_set_status('success')
        else:
            self.view.guide_display(
                f"Processed {success_count}/{total} file(s). {fail_count} failed."
            )
            self.view.guide_set_status('warning' if success_count > 0 else 'error')

        # Post-state: allow reset, re-enable process if listed files remain
        self.view.guide_set_actions_enabled(process_enabled=(self._last_listed_count > 0), reset_enabled=True)
        # Keep archive summary visible
        self._apply_archive_summary()
        log.debug(
            f"[GuidePanePresenter] on_processing_completed: success={success_count}, fail={fail_count}"
        )

    def on_error(self, *, title: str, message: str) -> None:
        """Show an error message in the guide pane."""
        # Title is ignored in guide copy; preserved here for upstream logging
        self.view.guide_display(message)
        self.view.guide_set_status('error')
        self.view.guide_set_actions_enabled(process_enabled=False, reset_enabled=True)
        log.error(f"[GuidePanePresenter] ERROR: {title} - {message}")

    def refresh(self) -> None:
        """Reapply known context to the view without changing state transitions."""
        # Re-apply archive summary and discovery badge using cached context
        self._apply_archive_summary()
        if self._last_folder:
            self.view.guide_show_source_context_options(
                discovery_enabled=self._discovery_enabled, folder=self._last_folder
            )
        log.debug("[GuidePanePresenter] refresh applied")

    # === Internal helpers ===
    def _apply_archive_summary(self) -> None:
        if self._save_same_as_source is None:
            return
        if self._save_same_as_source:
            # Section already titled "Archive"; avoid repeating the label
            self.view.guide_set_archive_summary("Same as Source")
        else:
            text = f"{self._save_path}" if self._save_path else "(not set)"
            self.view.guide_set_archive_summary(text)
