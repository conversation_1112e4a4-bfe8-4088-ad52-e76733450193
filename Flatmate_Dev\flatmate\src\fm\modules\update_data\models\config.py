"""Configuration types and enums for Update Data module."""

from enum import Enum


class SourceOptions(str, Enum):
    """Source selection types."""
    SELECT_FOLDER = "Select entire folder..."
    SELECT_FILES = "Select individual files..."


class SaveOptions(str, Enum):
    """Save location types."""
    SAME_AS_SOURCE = "Same as Source Files"
    SELECT_LOCATION = "Select save location..."


# Additional configuration enums can be added here as needed
class SourceSelectionType(Enum):
    """Types of source selection methods."""
    FILES = "files"
    FOLDER = "folder"
    RECENT_FOLDER = "recent_folder"


class SaveLocationType(Enum):
    """Types of save location methods."""
    SAME_AS_SOURCE = "same_as_source"
    CUSTOM = "custom"
