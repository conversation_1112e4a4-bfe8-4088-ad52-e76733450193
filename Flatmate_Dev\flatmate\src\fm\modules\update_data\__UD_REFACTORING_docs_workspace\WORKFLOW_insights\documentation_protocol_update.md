# Documentation Protocol Update

## Background

This document standardizes our approach to documenting implementation options, refactoring plans, and technical decisions within the Flatmate project. The goal is to create a consistent, discoverable, and maintainable documentation system across all modules.

## Current Issues

1. Inconsistent documentation locations and formats across the project
2. Lack of clear guidance on where implementation plans, options, and analysis should be stored
3. Scattered documentation that makes it difficult to find relevant information

## Standardized Documentation System

### 1. Module-Specific Documentation Structure

Each module should follow this folder structure for documentation:

```
fm/modules/<module_name>/
  └── _<MODULE>_docs_workspace/
      ├── SESSION_<N>/                 # Session-specific documentation
      │   ├── session_log_<N>.md       # Documentation of what was done in the session
      │   ├── implementation_plan.md   # Detailed plan for implementation
      │   ├── implementation_options.md # Analysis of different implementation options
      │   └── <FEATURE_NAME>/          # Feature-specific documentation if needed
      ├── ARCHITECTURE/                # Module architecture documentation
      ├── outstanding_items.md         # Ongoing tasks and known issues
      └── changelog.md                 # Running changelog for the module
```

### 2. Documentation Types and Naming Conventions

| Documentation Type | Location | Naming Convention | Purpose |
|-------------------|----------|-------------------|---------|
| Implementation Options | `_<MODULE>_docs_workspace/SESSION_<N>/` | `implementation_options.md` | Analysis of different implementation approaches |
| Implementation Plan | `_<MODULE>_docs_workspace/SESSION_<N>/` | `implementation_plan.md` | Detailed step-by-step plan for implementation |
| Session Log | `_<MODULE>_docs_workspace/SESSION_<N>/` | `session_log_<N>.md` | Record of work done in a specific session |
| System Audit | `_<MODULE>_docs_workspace/SESSION_<N>/<FEATURE>/` | `system_audit_<component>.md` | Analysis of existing system component |
| Architecture Overview | `_<MODULE>_docs_workspace/ARCHITECTURE/` | `<component>_architecture.md` | Documentation of component architecture |

### 3. End-of-Session Documentation Protocol

At the end of each development session:

1. Update the session log with work completed
2. Document any new implementation options or decisions
3. Update the outstanding items list with new tasks or completed work
4. Create new implementation plans as needed for the next session

## Integration with Existing Protocols

This documentation system standardizes and extends the existing documentation protocols:

1. **End-of-Chat-Session Protocol**: Use the docs_workspace folder structure for all session notes
2. **Implementation Review Protocol**: Store all implementation reviews in the appropriate docs_workspace folder
3. **Update-Docs Protocol**: Align with docs_workspace structure instead of specific fixed paths

## Implementation Plan

1. Update the Codebase Onboarding Guide with these standards
2. Review and reorganize existing documentation to match the new structure
3. Ensure all new documentation follows these guidelines

## Documentation Example

```markdown
# Implementation Options: Feature X

## Background
Brief description of the feature and current state...

## Option Analysis
### Option 1: Approach A
- **Implementation Details**: How it would be implemented...
- **Advantages**: List of pros...
- **Disadvantages**: List of cons...

### Option 2: Approach B
...details...

## Recommendation
Recommended approach with justification...

## Implementation Plan
Steps to implement the recommended option...
```
>>We need a consistent documentation protocols 
in fact this protocol of create folder (current focus or module name)_docs_workspace,
and could contain the following files or .md files or folders: 

 - discussion.md
 - research.md 
 - planning.md


particularly key
 - system_audit.md
 - implementation_options.md
 - implementation_plan.md
 - change_log.md 
 also very useful:
  - user_flow.md (or story)
 
 then a session1/ folder
 - session_log_1.md
 - implementation_plan.md
 

  is very good for refactoring work
where apropriate, a user_story or user_flow should probably also be created (like a pick a path book)
all protocols and guides hould use the hyphon-seperator and not the underscore_seperator
All protocols need to be updated to this system



