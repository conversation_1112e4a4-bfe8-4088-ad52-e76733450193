# QProxyStyle Migration Checklist

**Date**: 2025-01-30  
**Purpose**: Step-by-step migration guide from QSS to QProxyStyle  
**Status**: Implementation Ready

## Pre-Migration Preparation

### 1. Backup Current System
- [ ] Create backup of current QSS files
  ```bash
  cp -r flatmate/src/fm/gui/styles flatmate/src/fm/gui/styles_backup_qss
  ```
- [ ] Document current visual appearance (screenshots)
- [ ] Note any custom QSS selectors or special styling
- [ ] Identify all widgets that use `setStyleSheet()` directly

### 2. Environment Setup
- [ ] Create feature branch: `git checkout -b feature/qproxystyle-migration`
- [ ] Ensure PySide6 version compatibility (6.0+)
- [ ] Set up testing environment
- [ ] Install any additional development tools

### 3. Code Analysis
- [ ] Audit current QSS usage patterns
- [ ] Identify widgets with `objectName` dependencies
- [ ] List all color variables from `palette.qss`
- [ ] Document current theme switching mechanism

## Phase 1: Foundation Implementation

### 1.1 Create Core Style Class
- [ ] Create `flatmate/src/fm/gui/styles/flatmate_style.py`
- [ ] Implement `FlatmateStyle` class extending `QProxyStyle`
- [ ] Add color palette loading from current QSS values
- [ ] Implement basic `drawPrimitive()` override

### 1.2 Update Style Application
- [ ] Modify `flatmate/src/fm/gui/styles/__init__.py`
- [ ] Update `apply_styles()` function to use QProxyStyle
- [ ] Add palette creation function
- [ ] Keep legacy functions for backward compatibility

### 1.3 Integration with Main Application
- [ ] Verify `main.py` calls `apply_styles(app)` correctly
- [ ] Test basic application startup
- [ ] Confirm no import errors
- [ ] Check that custom style is applied

### 1.4 Basic Testing
- [ ] Application starts without errors
- [ ] Main window displays correctly
- [ ] Basic colors match previous appearance
- [ ] No console warnings or errors

## Phase 2: Core Widget Styling

### 2.1 Button Styling
- [ ] Implement `_draw_button()` method
- [ ] Handle primary/secondary button types
- [ ] Add hover and pressed states
- [ ] Test button interactions

**Testing Checklist:**
- [ ] Primary buttons show correct green color (#3B8A45)
- [ ] Secondary buttons show correct styling
- [ ] Hover effects work correctly
- [ ] Pressed states are visible
- [ ] Button text is readable

### 2.2 Panel Styling
- [ ] Implement `_draw_navigation_panel()` method
- [ ] Handle left_panel, right_panel, right_side_bar
- [ ] Add border styling
- [ ] Test panel visibility

**Testing Checklist:**
- [ ] Left panel shows dark green background (#1a382f)
- [ ] Right panel styling matches left panel
- [ ] Panel borders are visible and correct
- [ ] Panel content is properly visible

### 2.3 Toolbar Styling
- [ ] Implement `_draw_toolbar()` method
- [ ] Handle TableViewToolbar specifically
- [ ] Add border radius and colors
- [ ] Test toolbar appearance

**Testing Checklist:**
- [ ] TableViewToolbar has correct border (#2A5A3A)
- [ ] Border radius is applied (4px)
- [ ] Toolbar background matches theme
- [ ] Toolbar buttons are properly styled

## Phase 3: Complex Components

### 3.1 File Tree Styling
- [ ] Implement `_draw_tree_item()` method
- [ ] Handle selection highlighting
- [ ] Add alternating row colors
- [ ] Implement `_draw_file_tree_frame()` method

**Testing Checklist:**
- [ ] File tree has correct background (#202020)
- [ ] Selection highlighting works (#3B7443)
- [ ] Hover effects are visible
- [ ] Alternating rows show subtle difference
- [ ] Tree borders are correct

### 3.2 Table View Integration
- [ ] Test table view styling
- [ ] Verify header styling
- [ ] Check row selection colors
- [ ] Test scrollbar appearance

**Testing Checklist:**
- [ ] Table headers are properly styled
- [ ] Row selection uses correct colors
- [ ] Table borders match design
- [ ] Scrollbars are themed correctly

### 3.3 Custom Widget Support
- [ ] Identify widgets using custom QSS
- [ ] Implement specific styling overrides
- [ ] Test shared component styling
- [ ] Verify module-specific widgets

## Phase 4: Advanced Features

### 4.1 Dynamic Theme Switching
- [ ] Implement `switch_theme()` method
- [ ] Add light theme color palette
- [ ] Test theme switching functionality
- [ ] Update configuration integration

**Testing Checklist:**
- [ ] Theme switching works without restart
- [ ] All widgets update to new theme
- [ ] Colors are consistent across application
- [ ] Theme preference is saved

### 4.2 Font Size Updates
- [ ] Implement `update_font_size()` method
- [ ] Test dynamic font size changes
- [ ] Verify configuration persistence
- [ ] Check widget layout adjustments

**Testing Checklist:**
- [ ] Font size changes apply immediately
- [ ] All text elements scale correctly
- [ ] Widget layouts adjust properly
- [ ] Font size preference is saved

### 4.3 System Integration
- [ ] Test system color adaptation
- [ ] Verify palette functionality
- [ ] Check accessibility features
- [ ] Test high DPI scaling

## Phase 5: Migration Cleanup

### 5.1 Remove QSS Dependencies
- [ ] Remove or comment out QSS files
- [ ] Clean up `setStyleSheet()` calls
- [ ] Update documentation
- [ ] Remove unused style loading code

### 5.2 Performance Verification
- [ ] Measure widget creation performance
- [ ] Test application startup time
- [ ] Monitor memory usage
- [ ] Verify no performance regressions

### 5.3 Final Testing
- [ ] Complete visual regression testing
- [ ] Test all application modules
- [ ] Verify theme switching
- [ ] Check font size changes
- [ ] Test system integration features

## Testing Protocols

### Visual Regression Testing
```python
# Create test script for visual verification
def test_visual_appearance():
    """Compare current appearance with screenshots."""
    # Take screenshots of key components
    # Compare with baseline images
    # Report any differences
```

### Performance Testing
```python
# Test widget creation performance
import time
from PySide6.QtWidgets import QPushButton

def test_button_creation_performance():
    """Measure button creation time."""
    start_time = time.time()
    
    buttons = []
    for i in range(1000):
        btn = QPushButton(f"Button {i}")
        buttons.append(btn)
    
    end_time = time.time()
    print(f"Created 1000 buttons in {end_time - start_time:.3f} seconds")
```

### Integration Testing
```python
# Test complete application flow
def test_application_integration():
    """Test complete application with new styling."""
    from fm.main import initialize_application
    
    app, main_window, coordinator = initialize_application()
    
    # Test module switching
    # Test widget interactions
    # Test theme changes
    # Test font size changes
```

## Rollback Plan

### If Issues Arise
1. **Immediate Rollback**
   ```bash
   git checkout main
   # or
   git revert <commit-hash>
   ```

2. **Partial Rollback**
   - Restore QSS files from backup
   - Revert `apply_styles()` function
   - Keep QProxyStyle for future work

3. **Hybrid Approach**
   - Use QProxyStyle for new components
   - Keep QSS for existing components
   - Gradual migration over time

## Success Criteria

### Must Have
- [ ] Application starts and runs normally
- [ ] Visual appearance matches previous version
- [ ] No performance regressions
- [ ] All existing functionality works

### Should Have
- [ ] Improved widget creation performance
- [ ] Dynamic theme switching works
- [ ] Font size changes work
- [ ] Better system integration

### Nice to Have
- [ ] Improved accessibility
- [ ] Better high DPI support
- [ ] Easier styling maintenance
- [ ] Better debugging capabilities

## Post-Migration Tasks

### Documentation Updates
- [ ] Update developer documentation
- [ ] Create styling guide for new system
- [ ] Document theme creation process
- [ ] Update troubleshooting guides

### Team Training
- [ ] Conduct QProxyStyle training session
- [ ] Create code examples repository
- [ ] Document best practices
- [ ] Set up code review guidelines

### Monitoring
- [ ] Monitor application performance
- [ ] Track user feedback
- [ ] Watch for styling issues
- [ ] Plan future enhancements

---

**Note**: This checklist should be used in conjunction with the main migration report and implementation examples. Each phase should be completed and tested before moving to the next phase.
