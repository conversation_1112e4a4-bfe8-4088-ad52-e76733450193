"""
Service for analyzing files and providing enriched file information.
"""
import os
from typing import List, Dict, Any
from datetime import datetime

from ..pipeline.statement_handlers._handler_registry import get_handler
from ....core.services.logger import log

class FileInfoService:
    """Service to analyze files and determine their properties."""

    @staticmethod
    def get_file_info(file_path: str) -> Dict[str, Any]:
        """
        Extract file information using handler detection.

        Args:
            file_path: Path to the file.

        Returns:
            A dictionary with file information including size and format.
        """
        normalized_path = os.path.normpath(file_path)

        if not os.path.exists(normalized_path):
            raise FileNotFoundError(f"File not found at {normalized_path}")

        size = os.path.getsize(normalized_path)
        size_str = (
            f"{size / 1024:.1f} KB"
            if size < 1024 * 1024
            else f"{size / (1024 * 1024):.1f} MB"
        )

        # Filesystem timestamps
        try:
            modified_ts = os.path.getmtime(normalized_path)
            modified_dt = datetime.fromtimestamp(modified_ts)
        except Exception:
            modified_dt = None
        try:
            # On Windows, getctime is creation time; on Unix it is metadata change time.
            # Our primary target is Windows (per workspace notes), so this is acceptable.
            created_ts = os.path.getctime(normalized_path)
            created_dt = datetime.fromtimestamp(created_ts)
        except Exception:
            created_dt = None

        handler_instance = get_handler(normalized_path)

        # Defaults reflect "unknown" semantics
        format_info = {
            "bank_type": "Unknown",
            "format_type": "Unrecognized",
            "file_type": None,
            "handler": None,
        }

        if handler_instance:
            st = handler_instance.__class__.statement_type
            # Pass through EXACTLY as declared by handlers
            format_info = {
                "bank_type": st.bank_name,
                "format_type": st.variant,
                "file_type": getattr(st, "file_type", None),
                "handler": handler_instance.__class__.__name__,
            }

        return {
            "path": normalized_path,
            "size_bytes": size,
            "size_str": size_str,
            "modified": modified_dt,
            "created": created_dt,
            **format_info,
        }

    @staticmethod
    def discover_files(file_paths: List[str]) -> List[Dict[str, Any]]:
        """
        Process a list of files and collect their information.

        Args:
            file_paths: List of file paths to process.

        Returns:
            A list of dictionaries, each containing info for a file.
        """
        return [FileInfoService.get_file_info(fp) for fp in file_paths]
