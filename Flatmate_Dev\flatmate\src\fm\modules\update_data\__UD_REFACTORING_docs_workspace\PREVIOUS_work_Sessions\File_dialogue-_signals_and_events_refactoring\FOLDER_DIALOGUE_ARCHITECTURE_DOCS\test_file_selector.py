#!/usr/bin/env python3
"""
Manual test script for file_selector.py functionality.
Run this to verify the file dialogue system is working correctly.
"""

import sys
import os
from pathlib import Path

# Add the module path to sys.path for testing
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))

try:
    from _ui._view.shared_components.file_selector import FileSelector, FileUtils
    print("✅ Successfully imported FileSelector and FileUtils")
except ImportError as e:
    print(f"❌ Import failed: {e}")
    sys.exit(1)

def test_file_utils():
    """Test FileUtils functionality."""
    print("\n=== Testing FileUtils ===")
    
    # Test get_last_used_dir
    try:
        last_dir = FileUtils.get_last_used_dir()
        print(f"✅ get_last_used_dir(): {last_dir}")
        
        # Verify it's a valid directory
        if os.path.isdir(last_dir):
            print(f"✅ Directory exists and is accessible")
        else:
            print(f"⚠️  Directory may not exist: {last_dir}")
    except Exception as e:
        print(f"❌ get_last_used_dir() failed: {e}")
    
    # Test set_last_used_dir with current directory
    try:
        test_dir = os.getcwd()
        FileUtils.set_last_used_dir(test_dir)
        print(f"✅ set_last_used_dir() with valid directory: {test_dir}")
        
        # Test with invalid directory
        FileUtils.set_last_used_dir("/invalid/path/that/does/not/exist")
        print("✅ set_last_used_dir() handled invalid directory gracefully")
    except Exception as e:
        print(f"❌ set_last_used_dir() failed: {e}")
    
    # Test discover_files_in_folder
    try:
        current_dir = os.getcwd()
        files = FileUtils.discover_files_in_folder(current_dir)
        print(f"✅ discover_files_in_folder(): Found {len(files)} files in {current_dir}")
        
        # Test with invalid folder
        files = FileUtils.discover_files_in_folder("/invalid/path")
        print(f"✅ discover_files_in_folder() handled invalid path gracefully: {len(files)} files")
    except Exception as e:
        print(f"❌ discover_files_in_folder() failed: {e}")

def test_file_selector_static_methods():
    """Test FileSelector static methods without GUI."""
    print("\n=== Testing FileSelector (non-GUI methods) ===")
    
    # Test with invalid method
    try:
        result = FileSelector.get_file_paths("invalid_method")
        print(f"❌ Should have raised ValueError for invalid method")
    except ValueError as e:
        print(f"✅ Correctly raised ValueError for invalid method: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def main():
    """Run all tests."""
    print("File Selector Test Suite")
    print("=" * 40)
    
    test_file_utils()
    test_file_selector_static_methods()
    
    print("\n=== Test Summary ===")
    print("✅ Core functionality tests completed")
    print("⚠️  GUI dialogue tests require manual interaction")
    print("📝 To test GUI dialogues, integrate with the main application")

if __name__ == "__main__":
    main()
