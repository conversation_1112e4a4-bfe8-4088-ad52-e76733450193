"""
Checkbox Components

Provides reusable checkbox components that follow the application's design patterns.
These components can be used across different modules for consistent checkbox UI.
"""

from PySide6.QtCore import Signal, Qt
from PySide6.QtWidgets import QWidget, QCheckBox, QHBoxLayout
from fm.core.services.logger import log


class Debug<PERSON><PERSON><PERSON><PERSON>ox(QCheckBox):
    """QCheckBox with verbose debug logging of user interactions.

    This helps trace exactly when the widget receives mouse/key events and when
    Qt emits clicked/toggled, including pre/post checked state.
    """

    def __init__(self, label_text: str, parent: QWidget | None = None) -> None:
        super().__init__(label_text, parent)
        # Signal-level instrumentation
        self.clicked.connect(self._on_clicked)
        self.toggled.connect(self._on_toggled)
        self.stateChanged.connect(self._on_state_changed)

    # --- Qt signal handlers (for logging only) ---
    def _on_clicked(self, checked: bool) -> None:
        log.debug(
            f"[Debug<PERSON><PERSON>ck<PERSON><PERSON>] clicked: label='{self.text()}', checked={bool(checked)}, signalsBlocked={self.signalsBlocked()}"
        )

    def _on_toggled(self, checked: bool) -> None:
        log.debug(
            f"[DebugCheckBox] toggled: label='{self.text()}', checked={bool(checked)}, signalsBlocked={self.signalsBlocked()}"
        )

    def _on_state_changed(self, state: int) -> None:
        checked = self.isChecked()
        log.debug(
            f"[DebugCheckBox] stateChanged: label='{self.text()}', state={state}, checked={bool(checked)}, signalsBlocked={self.signalsBlocked()}"
        )

    # --- Event-level instrumentation ---
    def mousePressEvent(self, event):  # type: ignore[override]
        log.debug(
            f"[DebugCheckBox] mousePress: label='{self.text()}', button={event.button()}, modifiers={event.modifiers()}, wasChecked={self.isChecked()}, isDown={self.isDown()}, signalsBlocked={self.signalsBlocked()}"
        )
        super().mousePressEvent(event)

    def mouseReleaseEvent(self, event):  # type: ignore[override]
        was = self.isChecked()
        super().mouseReleaseEvent(event)
        log.debug(
            f"[DebugCheckBox] mouseRelease: label='{self.text()}', button={event.button()}, wasChecked={was}, nowChecked={self.isChecked()}, isDown={self.isDown()}, accepted={event.isAccepted()}, signalsBlocked={self.signalsBlocked()}"
        )

    def keyPressEvent(self, event):  # type: ignore[override]
        log.debug(
            f"[DebugCheckBox] keyPress: label='{self.text()}', key={int(event.key())}, modifiers={event.modifiers()}, wasChecked={self.isChecked()}, signalsBlocked={self.signalsBlocked()}"
        )
        super().keyPressEvent(event)

    def focusInEvent(self, event):  # type: ignore[override]
        log.debug(f"[DebugCheckBox] focusIn: label='{self.text()}'")
        super().focusInEvent(event)

    def focusOutEvent(self, event):  # type: ignore[override]
        log.debug(f"[DebugCheckBox] focusOut: label='{self.text()}'")
        super().focusOutEvent(event)

    # Programmatic set visibility beyond LabeledCheckBox wrapper
    def setChecked(self, checked: bool) -> None:  # type: ignore[override]
        log.debug(
            f"[DebugCheckBox] setChecked: label='{self.text()}', to={bool(checked)}, was={self.isChecked()}, signalsBlocked={self.signalsBlocked()}"
        )
        super().setChecked(checked)


class LabeledCheckBox(QWidget):
    """
    A checkbox with a label in a horizontal layout.
    
    Signals:
        state_changed: Emitted when the checkbox state changes
    """
    
    state_changed = Signal(bool)
    
    def __init__(self, label_text, checked=False, tooltip=None, parent=None):
        """
        Initialize the labeled checkbox.
        
        Args:
            label_text: Text to display next to the checkbox
            checked: Initial state of the checkbox
            tooltip: Optional tooltip text
            parent: Parent widget
        """
        super().__init__(parent)
        self._init_ui(label_text, checked, tooltip)
        self._connect_signals()
    
    def _init_ui(self, label_text, checked, tooltip):
        """Initialize the UI components."""
        # Main layout
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        # Checkbox
        self.checkbox = DebugCheckBox(label_text)
        self.checkbox.setChecked(checked)
        if tooltip:
            self.checkbox.setToolTip(tooltip)
        
        layout.addWidget(self.checkbox)
        layout.addStretch()
    
    def _connect_signals(self):
        """Connect widget signals."""
        self.checkbox.stateChanged.connect(self._on_checkbox_state_changed)

    def _on_checkbox_state_changed(self, state: int) -> None:
        checked = self.checkbox.isChecked()
        # Log user-visible change path; SourceSection blocks signals for programmatic updates
        log.debug(f"[LabeledCheckBox] stateChanged -> emit state_changed: label='{self.checkbox.text()}', checked={checked}, signalsBlocked={self.checkbox.signalsBlocked()}")
        self.state_changed.emit(checked)

    def is_checked(self):
        """Get the current state of the checkbox."""
        return self.checkbox.isChecked()
    
    def set_checked(self, checked):
        """Set the state of the checkbox."""
        log.debug(f"[LabeledCheckBox] set_checked: label='{self.checkbox.text()}', to={bool(checked)}, was={self.checkbox.isChecked()}, signalsBlocked={self.checkbox.signalsBlocked()}")
        self.checkbox.setChecked(checked)
