import sys
from pathlib import Path

from PySide6.QtWidgets import <PERSON>A<PERSON><PERSON>, QWidget, QPushButton, QVBoxLayout, QLabel
from PySide6.QtCore import Qt


"""
Purpose
-------
Standalone probe to validate whether Qt's QSS parser (via PySide6==6.9.1 / Qt version in your venv)
supports CSS custom properties syntax like var(--color-...),
and to verify selector matching for QPushButton[type="..."].

How to run (from repo root, inside venv):
  python tests/ui/test_qss_vars.py

What it does
------------
1) Applies three different QSS payloads to a minimal widget tree:
   - A: With var(--...) tokens
   - B: Same rules but literal hex values (no vars)
   - C: Broken vars (undefined custom properties)

2) It creates two buttons with distinct "type" dynamic properties:
   - primary_btn.setProperty("type", "primary")
   - secondary_btn.setProperty("type", "secondary")
   These are the selectors your theme.qss uses.

3) It prints which QSS payload applied and shows the window so you can visually confirm:
   - If A shows colors as expected → your Qt build understands var(--...).
   - If A does NOT style but B does → var(--...) is NOT supported; avoid this syntax in QSS.
   - If neither A nor B style → selector/property mismatch or stylesheet not applied.

Notes
-----
- Qt's QSS is NOT full CSS and traditionally does NOT support CSS custom properties var(--...).
  Some forks/tools transpile variables before applying. On stock Qt, expect no var() support.
- QPushButton[type="secondary"] selector relies on setProperty("type", "secondary") before setStyleSheet().
  We do both prior to applying stylesheet here.
"""


def build_ui():
    root = QWidget()
    root.setObjectName("root")

    layout = QVBoxLayout(root)
    layout.setContentsMargins(12, 12, 12, 12)
    layout.setSpacing(8)

    info = QLabel("QSS var(--...) and selector probe")
    info.setAlignment(Qt.AlignCenter)
    layout.addWidget(info)

    primary_btn = QPushButton("Primary")
    primary_btn.setProperty("type", "primary")
    layout.addWidget(primary_btn)

    secondary_btn = QPushButton("Secondary")
    secondary_btn.setProperty("type", "secondary")
    layout.addWidget(secondary_btn)

    # Expose for external checks if needed
    root.primary_btn = primary_btn
    root.secondary_btn = secondary_btn
    root.info = info
    return root


QSS_WITH_VARS = """
/* Simulate palette import with custom properties (NOT standard QSS) */
* {
    /* Custom properties in QSS are generally unsupported, this is a probe */
    --color-primary: #2E8B57;
    --color-primary-hover: #3DA46B;
    --color-primary-pressed: #277A4B;

    --color-secondary: #3B7443;
    --color-secondary-hover: #4D8C55;
    --color-secondary-pressed: #35673C;

    --color-text-primary: #FFFFFF;
}

/* Attempt to use var(...) */
QPushButton[type="primary"] {
    background-color: var(--color-primary);
    color: var(--color-text-primary);
    border: none;
    border-radius: 6px;
    padding: 6px;
    height: 30px;
    font-weight: bold;
}
QPushButton[type="primary"]:hover {
    background-color: var(--color-primary-hover);
}
QPushButton[type="primary"]:pressed {
    background-color: var(--color-primary-pressed);
}

QPushButton[type="secondary"] {
    background-color: var(--color-secondary);
    color: var(--color-text-primary);
    border: none;
    border-radius: 6px;
    padding: 6px;
    height: 30px;
}
QPushButton[type="secondary"]:hover {
    background-color: var(--color-secondary-hover);
}
QPushButton[type="secondary"]:pressed {
    background-color: var(--color-secondary-pressed);
}
"""

QSS_NO_VARS_LITERAL = """
QPushButton[type="primary"] {
    background-color: #2E8B57;
    color: #FFFFFF;
    border: none;
    border-radius: 6px;
    padding: 6px;
    height: 30px;
    font-weight: bold;
}
QPushButton[type="primary"]:hover {
    background-color: #3DA46B;
}
QPushButton[type="primary"]:pressed {
    background-color: #277A4B;
}

QPushButton[type="secondary"] {
    background-color: #3B7443;
    color: #FFFFFF;
    border: none;
    border-radius: 6px;
    padding: 6px;
    height: 30px;
}
QPushButton[type="secondary"]:hover {
    background-color: #4D8C55;
}
QPushButton[type="secondary"]:pressed {
    background-color: #35673C;
}
"""

QSS_BROKEN_VARS = """
/* Uses var() but without defining custom properties block at all */
QPushButton[type="primary"] {
    background-color: var(--color-primary);
    color: var(--color-text-primary);
}
QPushButton[type="secondary"] {
    background-color: var(--color-secondary);
    color: var(--color-text-primary);
}
"""


def apply_and_report(widget: QWidget, qss: str, label: str):
    # Clear any previous sheet then apply
    widget.setStyleSheet("")
    widget.setStyleSheet(qss)
    print(f"[QSS APPLIED] {label} -> length={len(qss)} chars")


def main():
    app = QApplication.instance() or QApplication(sys.argv)

    root = build_ui()
    root.resize(420, 200)

    print("PySide6/QSS probe starting...")
    print("1) Applying QSS_WITH_VARS (expects failure on stock Qt)")
    apply_and_report(root, QSS_WITH_VARS, "QSS_WITH_VARS")

    print("2) Applying QSS_NO_VARS_LITERAL (should work if selectors match)")
    apply_and_report(root, QSS_NO_VARS_LITERAL, "QSS_NO_VARS_LITERAL")

    print("3) Applying QSS_BROKEN_VARS (expects no styling due to undefined vars)")
    apply_and_report(root, QSS_BROKEN_VARS, "QSS_BROKEN_VARS")

    print("\nVisual check instructions:")
    print("- After step 2, Primary should appear green (#2E8B57) and Secondary dark green (#3B7443).")
    print("- Hover and pressed states should change shades if you interact.")
    print("- Steps 1 & 3 likely show no colors if var(--...) is unsupported.")
    print("\nConclusion guide:")
    print("- If only step 2 shows correct styling -> Drop var(--...) in QSS. Use literal values or a generation step.")
    print("- If none show styling -> Selector mismatch: ensure setProperty('type', 'primary'|'secondary') and that stylesheet is applied on a common ancestor.")
    print("- If step 1 also works -> Your Qt build supports var(--...). You can keep var() usage.")

    root.show()
    sys.exit(app.exec())


if __name__ == "__main__":
    main()