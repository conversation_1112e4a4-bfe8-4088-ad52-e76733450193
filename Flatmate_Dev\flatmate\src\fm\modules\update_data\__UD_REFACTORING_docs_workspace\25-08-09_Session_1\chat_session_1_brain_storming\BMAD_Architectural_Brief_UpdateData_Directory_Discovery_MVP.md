# BMAD Architectural Brief — Update Data: Directory Discovery (MVP)

- Date: 2025-08-09
- Module: `fm.modules.update_data`
- Context: Create a “golden path” for new users — files dropped in the current source folder appear in the file list automatically with zero configuration.

## 1) Problem Statement
- Current system fails to reliably surface a per-folder “file discovery” option and add new files to the file list in real time.
- Logic is scattered (toggle, monitoring, persistence), ownership unclear, and presenter/view/event responsibilities are mixed.
- Result: No dependable, minimal path for new users to ingest files without manual steps.

## 2) Goals (MVP)
- Single current source folder (shown in `guide_pane.py`) can enable “file discovery for this folder”.
- When enabled: scan root once and start monitoring for new files; add them to the file list.
- Processed files are archived automatically to the left-panel archive destination (Same as Source).
- Presenter remains Qt‑decoupled; view updates via interface methods only.

## 3) Non‑Goals (MVP)
- Multiple folders management UI.
- DB‑level duplicate checks before listing (rely on archive policy for MVP).
- Broad service relocations/renames (beyond new additions).

## 4) Scope & Ownership
- View: `guide_pane.py` (UI only).
- Presenter: `GuidePanePresenter` (new) — orchestrates UI state/commands; calls services; updates file list through `IUpdateDataView`.
- Services (core, no UI logic):
  - `DirectoryInfoService` — per‑folder roles/settings/state (enable discovery, archive destination, last scan).
  - `folder_monitoring_service` — low‑level filesystem watcher (existing; to be integrated).
  - `ArchiveService` — optional later; MVP uses simple move via existing utilities.
- Models:
  - `DirectoryInfo` dataclass (path, role, discovery_enabled, archive_dest, last_scan).

## 5) Key Decisions
- Per‑folder toggle text: “Enable file discovery for this folder”.
- “New” files (MVP): files present in the root of the enabled source folder. We rely on archiving to keep root clean.
- Archive destination: use left panel option “Same as Source” (e.g., `<source>/archive`).
- Interface over events: presenter→view uses `IUpdateDataView` methods; avoid duplicative events.
- Namespace: folder models/services live under `fm/core/directory/models|services/`.

## 6) Interfaces & Contracts (MVP examples)
- `IUpdateDataView` (additions, not exhaustive):
  - `set_source_folder(path: Path)`
  - `show_discovery_state(enabled: bool)`
  - `add_files(paths: list[Path])`
  - `show_toast(message: str)` (optional convenience)
- `DirectoryInfoService`:
  - `enable_discovery(path, archive_dest?) -> DirectoryInfo`
  - `disable_discovery(path) -> DirectoryInfo|None`
  - `get(path) -> DirectoryInfo|None`
  - `mark_scanned_now(path) -> DirectoryInfo|None`
- `GuidePanePresenter` primary flow:
  - On toggle ON: resolve `DirectoryInfo` (archive dest from left panel), scan root, `add_files([...])`, start monitoring.
  - On toggle OFF: stop monitoring.

## 7) Data & Persistence
- MVP: `DirectoryInfoService` is in‑memory with stubbed persistence hooks (config wiring later).
- Archive policy is read from the existing left panel settings; behaviour must be deterministic.

## 8) Risks & Mitigations
- Risk: Presenter accidentally couples to Qt. Mitigation: interface-only calls, no widget/enum imports.
- Risk: Root contains already‑processed files. Mitigation (MVP): enforce archive on processing; user guidance in UI.
- Risk: Event duplication. Mitigation: do not add/consume events for this flow; record any unavoidable exceptions.

## 9) Open Questions
- Should we expose discovery state and last scan time in the guide pane UI now or later?
- Confirm name and location of the existing monitoring service we will call (final import path).
- Any file type filters for discovery, or accept all and let import stage validate?

## 10) Acceptance Criteria
- Enabling discovery on the current source folder triggers a scan; eligible files are added to the file list.
- New files dropped into the folder appear in the file list within seconds.
- Processed files are moved to the configured archive destination automatically.
- No presenter–Qt coupling; no new event bus dependencies for this flow.

## 11) Artefacts Created
- `fm/core/directory/models/directory_info.py` — `DirectoryInfo`
- `fm/core/directory/services/directory_info_service.py` — `DirectoryInfoService`
- Decisions record: `Directory_Discovery_MVP_Decisions.md`

## 12) Next Step (New Chat Session)
- Translate this brief into a technical implementation plan (tasks, file edits, interfaces to add, minimal wiring), then implement in small PR‑sized steps.
