# Auto‑Queue Toggle — Architecture Overview (Session 2)

Last updated: 2025-08-08
Scope: Ownership, placement, naming, and flows for the per‑folder Auto‑Queue toggle
Audience: Update Data engineers and AI agents

---

## 1) Public Contracts (Interfaces & Events)

- **Intent (View → Presenter/Managers)**
  - `ViewEvents.AUTO_QUEUE_TOGGLED` with payload: `enabled: bool`
  - Emitted by `ud_view.py` (Guide Pane toggle). The View acts as the switchboard only.

- **View Rendering (Presenter/Managers → View)**
  - Render source options via interface methods, e.g. `view.show_guide_source_options(auto_queue_enabled=bool)`
  - Source of truth is config (`ud_config`), not runtime monitoring state.

- **Typed Events**
  - Not required for the toggle itself (single consumer). Use interface render after persistence.
  - Use typed events for multi‑listener lifecycle (e.g., processing), not for this toggle.

References:
- Intents and ownership: `flatmate/DOCS/_GUIDES/Update-Data-UI-Deep-Dive.md` (§3.1, §5.x)
- View: `flatmate/src/fm/modules/update_data/_ui/ud_view.py`
- Local bus keys: `flatmate/src/fm/modules/update_data/services/local_event_bus.py`

---

## 2) Ownership & Placement

- **Presenter (`ud_presenter.py`)**
  - Subscribes to `AUTO_QUEUE_TOGGLED` intent.
  - Resolves the target folder (from current source or inferred from selected files).
  - Delegates business logic; keeps orchestration thin.

- **AutoQueueManager (new)** — `flatmate/src/fm/modules/update_data/_ui/_presenter/auto_queue_manager.py`
  - Single responsibility: per‑folder preference + monitoring reconciliation.
  - Methods:
    - `set_auto_queue(folder: str, enabled: bool)`
      - Persist via `ud_config.set_auto_queue_enabled(folder, enabled)`
      - Reconcile monitoring via `FolderMonitorService.set_folder_monitored(folder, enabled)`
    - `get_auto_queue(folder: str) -> bool` (reads from `ud_config`)
  - No Qt, no View access.

- **State Rendering Policy** — `flatmate/src/fm/modules/update_data/_ui/_presenter/state_coordinator.py`
  - Reads from `ud_config.is_auto_queue_enabled(folder)`
  - Calls View interface to display the Guide Pane state
  - Never reads monitoring runtime state for UI

- **Related Managers**
  - `FileConfigManager` — source selection, MRU support, helpers to determine current folder
  - `FileInfoManager` — file list enrichment; unrelated to toggle persistence

---

## 3) Data & Control Flow (Happy Path)

1. User clicks Guide Pane toggle in `ud_view.py` → View emits `AUTO_QUEUE_TOGGLED(enabled)`
2. `ud_presenter.py` receives intent → resolves `folder_path`
3. Presenter calls `AutoQueueManager.set_auto_queue(folder_path, enabled)`
4. Manager persists config and reconciles `FolderMonitorService`
5. Presenter triggers `state_manager.sync_state_to_view()` (or `state_coordinator` helper)
6. View renders checkbox state from `ud_config` via coordinator

Notes:
- UI reflects config state after any source change and on module init.
- No direct View mutations from managers; View is rendered via presenter/coordinator only.

---

## 4) Services & Boundaries

- **Keep Separate (Core services):**
  - `RecentFoldersService` — MRU/history, user preference
  - `FolderMonitorService` — runtime file‑system watching
- Rationale: different lifecycle and concerns; avoid coupling.

References:
- Session summary: `.../250808_SESSION_1_Docs/Session_Summary_and_Handover.md` (Core Services Integration)

---

## 5) Naming & Conventions

- Intent key: `AUTO_QUEUE_TOGGLED`
- Manager: `AutoQueueManager` in `_ui/_presenter/auto_queue_manager.py`
- Methods: `set_auto_queue`, `get_auto_queue`
- Config API: `ud_config.is_auto_queue_enabled(folder)`, `ud_config.set_auto_queue_enabled(folder, enabled)`
- Presenter helpers: `_resolve_current_folder()` or via `FileConfigManager.get_current_folder()`

UK spelling and explicit naming apply throughout.

---

## 6) MRU UX Decision (recap)

- Default: Open folder dialog at MRU path (confirmation‑oriented)
- Preference: `quick_select_recent_source` (default False) to allow direct queue without dialog
- Applies to Source and Archive MRUs; compact labels with disambiguation

Reference: `.../RECENT_FOLDERS_and_FILE_VIEW_refinements/notes_2025-08-08_MRU_and_GuidePane.md`

---

## 7) Minimal Refactor Checklist

- **Presenter thin‑out**
  - Replace inline toggle logic in `ud_presenter.py` with calls to `AutoQueueManager`
- **Add manager**
  - Create `_ui/_presenter/auto_queue_manager.py` with methods above
- **Rendering**
  - Ensure `state_coordinator.py` always renders from `ud_config` across all entry paths
- **Docs**
  - Link this file from `Update-Data-UI-Deep-Dive.md` (§5.x)

---

## 8) Risks & Tests

- **Risks**
  - Presenter bloat if logic not extracted
  - Accidental UI state sourced from monitoring runtime instead of config
- **Tests**
  - Unit: `ud_config` per‑folder persistence
  - Integration: toggling → monitoring reconciliation → UI reflects config after refresh

---

## 9) References (clickable)

- Guide: `flatmate/DOCS/_GUIDES/Update-Data-UI-Deep-Dive.md`
- Session 1 summary: `flatmate/src/fm/modules/update_data/__UD_REFACTORING_docs_workspace/250808_SESSION_1_Docs/Session_Summary_and_Handover.md`
- Notes: `flatmate/src/fm/modules/update_data/__UD_REFACTORING_docs_workspace/250808_SESSION_1_Docs/RECENT_FOLDERS_and_FILE_VIEW_refinements/notes_2025-08-08_MRU_and_GuidePane.md`
