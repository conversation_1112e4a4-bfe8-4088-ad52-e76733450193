import pytest

from fm.modules.update_data.services.local_event_bus import update_data_local_bus, ViewEvents
from fm.modules.update_data._ui.ui_events import FileListUpdatedEvent


@pytest.fixture(autouse=True)
def _clean_bus():
    update_data_local_bus.clear_event_log()
    yield
    update_data_local_bus.clear_event_log()


def test_smoke_happy_path_events_flow_only():
    """
    Smoke: minimal, fast verification of happy-path event flow.
    - Does not create real dialogs or windows
    - Ensures basic SOURCE_SELECT_REQUESTED and FILE_LIST_UPDATED appear on the bus
    """
    update_data_local_bus.emit(ViewEvents.SOURCE_SELECT_REQUESTED.value, "SELECT_FILES")
    update_data_local_bus.emit(
        ViewEvents.FILE_LIST_UPDATED.value,
        FileListUpdatedEvent(files=["/tmp/a.csv"], source_path="/tmp"),
    )

    log = update_data_local_bus.get_event_log(limit=10)
    seen = [e["type"] for e in log]
    assert ViewEvents.SOURCE_SELECT_REQUESTED.value in seen
    assert ViewEvents.FILE_LIST_UPDATED.value in seen


@pytest.mark.skip(reason="Pending: integrate with presenter/file manager to simulate empty-folder path")
def test_error_path_emits_error_dialog_request_when_empty_folder():
    """
    Contract intent (to be implemented):
    - When an empty folder is selected (no supported files), an ERROR_DIALOG_REQUESTED must be emitted.
    - This test is marked skipped until we wire a minimal surface to trigger the condition deterministically.
    """
    pass