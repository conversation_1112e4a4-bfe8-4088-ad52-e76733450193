# Guide Pane – Behaviour and Initial State (MVP)

## Layout
- Fit sections to content.
- File browser takes available remaining space.
- When empty, show italic placeholder in the browser area:
  - _Files selected for processing will appear here…_

## Info Bar Messaging (state-driven)
- No files selected.
- Awaiting archive location… (when Archive mode is "Select folder" and no path chosen)
- Processing…
- Processing complete.

## Initial State (no files, Archive = default)

---
### Source files section
- Folder slot 1: "No files or folders selected…"
- Per-folder options slot: empty/hidden (section fits contents)

---
### Archive section
- Archive: "Same as source folder (default)"
- Options slot: hidden (fits content)

---
### Master File section (future)
- Not shown
- Destination: (future)
- Options slot: (future)

---
### Browser
- Occupies remaining space.
- Shows the italic empty-state placeholder:
  - _Files selected for processing will appear here…_



## Design notes

- Each section follows a consistent formula and is extensible.
- Build the UI from small primitives to keep it simple and reusable:
  - Section primitive (title + body + optional footer)
  - Slot primitive (row item inside a section)
  - Option line primitive (options that pertain to a slot)
  - Prefer pre-existing styled labels and checkboxes from `fm/gui/_shared_components` where possible.

- Hierarchy (nested structure):
  - Section
    - Slot
      - Option line (pertains to its parent slot)
___________________________________________________________________________________________________
```GUIDE_PANE_content (QFrame container, transparent, no border, contentsMargins=0)
GuidePane (QFrame)
  - props: frameShape=NoFrame; style=transparent; contentsMargins=0; sizePolicy=Expanding,Preferred
  - layout: QVBoxLayout (spacing=6; margins: 0,0,0,0)

  MainMessage (SharedLabel.Info)
    - visible: optional (MVP: Top)
    - text: bound via presenter `set_main_message`
    - wordWrap=true; style=subdued

  Section: Source (QWidget)
    - layout: QVBoxLayout (spacing=4; margins: 0,0,0,0)
    - Header (SharedLabel.H2, text='Source')
    - SectionInfo (SharedLabel.Info) [optional]
    - SlotList (QVBoxLayout)
      - Slot 1 (QWidget)
        - layout: QHBoxLayout (spacing=6; margins: 0,0,0,0)
        - Label (SharedLabel.Body, text='No files or folders selected…')
        - [optional] Checkbox (SharedCheckbox) // discovery toggle etc.

  Section: Archive (QWidget)
    - layout: QVBoxLayout (spacing=4; margins: 0,0,0,0)
    - Header (SharedLabel.H2, text='Archive')
    - SectionInfo (SharedLabel.Info) [optional]
    - SummaryLine (SharedLabel.Body, text='Same as source folder (default)')
    - OptionsRow (QHBoxLayout) [visible iff ArchiveMode==SelectFolder]
      - Label (SharedLabel.Body, text='Folder:')
      - PathChooser (SharedPathPicker or QPushButton + QFileDialog)

  BrowserArea (QWidget)
    - Host container for existing FilePaneV2 (pre-built)
    - sizePolicy=Expanding,Expanding  // fills remaining space
    - Guide pane only toggles visibility and sets empty-state label when no files;
      underlying file table behaviour remains in `file_pane_v2`.
```

- Info lines (per section):
  - Optional single info line per section.
  - Levels: info | warning | success | error
  - Placement: below the section header, above slots.
  - Primitive: `InfoLine(text, level)`
  - View interface (presenter-facing):
    - `set_section_info(section_id, text, level)`
    - `clear_section_info(section_id)`
  - MVP usage examples:
    - Source section (no files): “No files selected.” (info)
    - Archive section (Select Folder without path): “Awaiting archive location…” (warning)

- Main message section (guide pane level):
  - Optional, single message for the whole pane.
  - Placement: configurable Top or Bottom (default Top for MVP).
  - Levels: info | warning | success | error.
  - Primitive: reuse shared Label component (styled) with subdued tone.
  - View interface (presenter-facing):
    - `set_main_message(text, level, position='Top'|'Bottom')`
    - `clear_main_message()`

- Collapsible sections (future):
  - Behaviour: each section can be collapsed/expanded; collapsed state hides slots and option lines.
  - Affordance: chevron next to section title; click toggles state.
  - Default: all sections expanded in MVP; feature disabled until needed.
  - View interface (presenter-facing):
    - `set_section_collapsed(section_id, collapsed: bool)`
    - Optional callback to presenter on user toggle (if required later).
  - Optional: persist last expanded/collapsed state per user/session.

- Implementation approach:
  - First refine locally under: `fm/modules/update_data/view_components/guide_pane_components/`
  - If/when stabilised, elevate shared pieces to: `fm/gui/_shared_components/guide_pane/`
  - Reuse existing shared primitives where possible; otherwise style a minimal new set.

- Phased roll-out (minimum viable path):
  1) MVP: Implement local primitives + initial state + info bar messages + empty-state browser.
  2) Consolidate: Extract any duplicated UI into local primitives, tighten API.
  3) Share: Promote stable primitives to `fm/gui/_shared_components/guide_pane/`.

## Initial state copy (Phase 1)

- Main message (Top): “No files selected.” (info)
- Source section:
  - Header label: “Source”
  - Section info: “No files selected.” (info)
  - Slot 1: “No files or folders selected…”
- Archive section:
  - Summary line: “Same as source folder (default)”
  - Section info (if Select Folder without path): “Awaiting archive location…” (warning)
- Browser area:
  - Italic empty-state: _Files selected for processing will appear here…_
  4) Enhance: Add optional features (e.g., richer per-folder options) without changing the API surface.