# Brainstorming Session: Building the Flatmate MVP with AI

## Session Setup

*   **Topic**: Establishing effective systems and processes for AI-assisted development to build the Flatmate MVP without going mad.
*   **Constraints**: Solo developer with limited experience, aiming for an MVP. Heavy reliance on AI assistance.
*   **Goal**: Focused ideation on creating sustainable and efficient development workflows.
*   **Output**: A structured document outlining the systems and processes developed during the session.

---

## Approach

*   **Method**: Progressive Flow (Divergent -> Convergent -> Synthesis)
*   **Starting Technique**: Pain Point Mapping

---

### Divergent Phase: Pain Point Mapping

*   **Constant Repetition**: Having to repeatedly explain context, requirements, and architectural patterns to AI assistants.
*   **Regression & Rework**: Simple, previously implemented features (e.g., file dialogues) become difficult to re-implement after refactoring.
*   **Feature Loss**: Existing functionality gets broken or lost during refactoring cycles.
*   **Poor AI Context**: AI assistants demonstrate a lack of understanding of the overall codebase and its structure.
*   **Pattern Violation**: AIs do not respect or adhere to the established architectural patterns of the application.
*   **Architectural Uncertainty**: Difficulty in identifying the optimal design patterns and implementing them correctly.
*   **Blocked Progress**: Stuck in a loop of tidying and fixing, preventing progress on core MVP features.
*   **Emotional Toll**: The process is leading to anger, frustration, and a sense of being stuck.

---

### Divergent Phase: Future Perfect

*   **Polished Foundation**: The app is functional, with consistent styling and a professional look and feel.
*   **Systematic Workflow**: A predictable, 'by-the-numbers' process for feature development is established: Brainstorm -> Plan -> Design -> Document -> Implement.
*   **High Velocity**: At least one or two new features are added per day.
*   **Frictionless Tasks**: Minor implementation details (e.g., file dialogs) are handled in less than an hour.
*   **Seamless AI Partnership**: The AI assistants have a perfect, deep understanding of the codebase, its patterns, and its logic, enabling them to implement features flawlessly.
*   **Pristine Codebase**: The code is described as beautiful, optimized, logical, and easy to work on.

---

### Convergent Phase: "How Might We..." Questions

**1. How might we create a system that gives an AI assistant all the context it needs, so you never have to repeat yourself?**

*   **Proposed Solution: The Project Bible.** A central, concise markdown document that serves as the single source of truth for the project's architecture, patterns, and goals. This document can be provided to the AI at the start of any new task.

    *   **Proposed Structure:**
        1.  **The "Why" (Elevator Pitch):** High-level summary of the app's purpose.
        2.  **Core Architectural Principles:** Bulleted list of key design patterns and rules.
        3.  **Key Modules & Responsibilities:** Simple breakdown of the codebase structure.
        4.  **"Golden Path" User Flows:** Step-by-step descriptions of critical user journeys.
        5.  **Critical "Do's and Don'ts":** A list of hard rules and constraints.

**2. How might we establish a clear, repeatable process for implementing new features or refactoring existing ones, so that work is predictable and regressions are minimised?**

*   **Adopted Solution: The `/feature-protocol` Workflow.** The project already contains a detailed, prescriptive workflow for feature implementation. This protocol, accessible via the `/feature-protocol` slash command, is superior to the one proposed during brainstorming and is hereby adopted as the official standard operating procedure. It mandates a specific folder structure and a set of documentation for each new feature, ensuring a rigorous, fact-based approach to development.

**3. How might we make architectural patterns and component rules so clear and accessible that an AI can follow them without deviation?**

*   **Proposed Solution: Component Recipes & Implementation Guides.** For each core architectural pattern (e.g., smart widgets, service classes), create a dedicated markdown document that acts as a step-by-step tutorial.

    *   **Content of a Recipe:**
        1.  **Purpose:** When to use this pattern.
        2.  **File Structure:** A template of the required files and directories.
        3.  **Boilerplate Code:** Starter code for each file in the structure.
        4.  **Key Principles:** A checklist of "must-haves" for the component (e.g., "Must not contain Qt code in the presenter").
        5.  **Example Usage:** A snippet showing how to integrate the new component into the application.

*   **Integration with Protocol:** The "Feature Development Protocol" will include a step to identify and use the relevant recipe for any new component.

Monday, August 4, 2025 @ 03:06:10 PM

Initial docs proposed to meet these requirements have now been shifted to:
C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\DOCS\COMPONENT_RECIPES_PROPOSED_WORKFLOWS_and_GUIDES