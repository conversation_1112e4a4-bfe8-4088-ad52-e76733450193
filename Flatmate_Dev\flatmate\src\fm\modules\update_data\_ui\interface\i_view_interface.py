"""
Abstract View Interface for Update Data Module.

Defines the contract between Presenter and View, breaking circular dependencies.
Based on analysis from REFACTOR_ANALYSIS.md
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Protocol
from PySide6.QtCore import Signal


class IUpdateDataView(Protocol):
    """
    Abstract interface for Update Data View.
    
    Presenter depends only on this interface, never on concrete view.
    Concrete view implements this interface and handles Qt widget details.
    
    This interface breaks the circular dependency chain:
    OLD: module_coordinator → ud_presenter → ud_view → center_panel → gui_components → main_window → module_coordinator
    NEW: Presenter → IUpdateDataView ← UpdateDataView (no upward dependencies)
    """
    
    # === SIGNALS (View → Presenter) ===
    # High-level domain events, not widget-specific signals
    cancel_clicked = Signal()
    source_select_requested = Signal(str)  # selection_type: "folder" or "files"
    save_select_requested = Signal()
    source_option_changed = Signal(str)
    save_option_changed = Signal(str)
    process_clicked = Signal()
    update_database_changed = Signal(bool)
    
    # === STATE QUERIES (Presenter → View) ===
    def get_save_option(self) -> str:
        """Get current save option selection."""
        ...

    def get_update_database(self) -> bool:
        """Get update database checkbox state."""
        ...

    # === STATE UPDATES (Presenter → View) ===
    def set_save_select_enabled(self, enabled: bool) -> None:
        """Enable/disable save selection controls."""
        ...

    def set_source_option(self, option: str) -> None:
        """Set source option display text."""
        ...

    def set_source_options(self, options: List[str], selected: str | None = None) -> None:
        """Set full list of source options and optionally select one."""
        ...

    def set_save_path(self, path: str) -> None:
        """Set save path display."""
        ...

    def set_archive_options(self, options: List[str], selected: str | None = None) -> None:
        """Set full list of archive options and optionally select one."""
        ...

    def set_process_button_text(self, text: str) -> None:
        """Set process button text."""
        ...

    def set_guide_archive_summary(self, text: str) -> None:
        """Display the archive summary in the guide pane (e.g., 'Same as Source' or a folder path)."""
        ...

    # === Guide pane namespaced API (Option 1) ===
    def guide_display(self, text: str) -> None:
        """Set the main guide message."""
        ...

    def guide_set_status(self, status: str) -> None:
        """Set guide status badge/state ('info'|'processing'|'warning'|'success'|'error')."""
        ...

    def guide_show_source_context_options(self, *, discovery_enabled: bool = False, folder: str | None = None) -> None:
        """Show contextual guide options for the current source folder."""
        ...

    def guide_set_archive_summary(self, text: str) -> None:
        """Update the archive summary line in the guide pane."""
        ...

    def guide_set_source_notice(self, text: str) -> None:
        """Set a secondary notice in the Source section (collapsible row)."""
        ...

    def guide_clear_source_notice(self) -> None:
        """Clear the Source section notice and hide its row."""
        ...

    def guide_set_actions_enabled(self, *, process_enabled: bool, reset_enabled: bool) -> None:
        """Enable/disable guide-context actions (e.g., process/reset affordances shown in guide)."""
        ...

    def show_guide_source_options(self, monitor_enabled: bool = False, discovery_enabled: bool = False) -> None:
        """Show contextual guide options for source handling (monitoring and discovery)."""
        ...
        #=====ACTION/PROCESS BUTTON==========

    def set_process_enabled(self, enabled: bool) -> None:
        """Enable/disable process button."""
        ...
    
    # === DIALOGS (Presenter → View) ===
    def show_folder_dialog(self, title: str, initial_dir: str) -> str:
        """Show folder selection dialog."""
        ...

    def show_files_dialog(self, title: str, initial_dir: str) -> List[str]:
        """Show file selection dialog."""
        ...

    def show_error(self, message: str) -> None:
        """Show error message to user."""
        ...

    # === DISPLAY MANAGEMENT (Presenter → View) ===
    def display_selected_source(self, source_data: Dict[str, Any]) -> None:
        """Display selected source information."""
        ...
        
    def display_enriched_file_info(self, file_info_list: List[Dict[str, Any]]) -> None:
        """Display enriched file information received from the presenter."""
        ...

    def cleanup(self) -> None:
        """Clean up view resources."""
        ...

    # === COMPONENT LIFECYCLE ===
    def show_component(self) -> None:
        """Show the view component."""
        ...

    def hide_component(self) -> None:
        """Hide the view component."""
        ...
        
    def get_current_files(self) -> List[str]:
        """Get the current list of files from the file pane."""
        ...

    def connect_file_list_manager(self, local_bus) -> None:
        """Connect file pane to FileListManager events."""
        ...
    
    # === FILE OPERATIONS (New file_pane_v2 support) ===
    def add_files(self, files: List[str]) -> None:
        """Add files to the file view."""
        ...
    
    def remove_file(self, file_path: str) -> None:
        """Remove a file from the file view."""
        ...
    
    def set_files(self, files: List[str]) -> None:
        """Set the complete list of files in the file view."""
        ...
    
    def get_selected_file(self) -> str:
        """Get the currently selected file path."""
        ...
    
    def clear_files(self) -> None:
        """Clear all files from the file view."""
        ...
