# Update Data — Systems Overview and Change Impact (Discussion)

Date: 2025-08-10
Owner: Cascade

Purpose: concise reference for back-and-forth. Please add comments inline using `>>`.

---

## 1) Processing Lifecycle — StateManager
- Purpose: single source of truth for processing state; one place to sync U<PERSON> via `StateManager.sync_state_to_view()`.
- Change impact:
  - Remove view-local `PROCESSING_*` emissions; avoid duplicate/competing updates.
  - Presenter/manager remain Qt-free; deterministic UI state from one sync point.
  - Unified status messaging (InfoBar/Guide Pane) from `state.status_message`.
- Key files: `update_data/_ui/_presenter/processing_manager.py`, `update_data/ud_presenter.py`, `StateManager`.
- Open questions:
  - >> Do we need any additional state fields for Guide Pane (e.g., pending counts)?

## 2) View Interface — `IUpdateDataView`
- Purpose: the MVP boundary for presenter→view. Explicit, testable, no Qt in presenter.
- Change impact:
  - Thin pass-through methods in `ud_view.py` to underlying widgets (e.g., `set_source_option`, `get_save_option`, `set_process_enabled`).
  - Eliminates presenter `AttributeError`s and tight coupling; improves clarity and testability.
  - Left panel stays a layout manager only; all reads/writes go through the view.
- Key files: `update_data/interface/i_view_interface.py`, `update_data/_ui/ud_view.py`.
- Open questions:
  - >> Any additional convenience methods you want on the interface for common flows?

## 3) Local Event Bus — `ViewEvents` usage
- Purpose: for multi-listener or asynchronous flows only (e.g., dialogue results, centre pane add-files intent).
- Change impact:
  - Cleaned speculative/mismatched events; prevent duplication with interface methods.
  - Standardise payloads as dataclasses per `proposed_events.md/csv`.
  - Keep `ViewEvents.ADD_FILES_REQUESTED` for centre add-files intent.
- Key files: `update_data/services/local_event_bus.py`, `proposed_events.md/csv`.
- Open questions:
  - >> Any remaining view-local events that should be migrated to interface methods?

## 4) Centre File Pane — `UDFileView`
- Purpose: owns file list UI/behaviour; emits high-level domain intents (not widget signals).
- Change impact:
  - `_connect_center_pane_intents()` in `UpdateDataView` wires `UDFileView.add_files_requested` → `ViewEvents.ADD_FILES_REQUESTED`.
  - v2 internals stay encapsulated within `UDFileView`; `UpdateDataView` treats it via stable API.
- Key files: `update_data/_ui/_view/center_panel_components/ud_file_view.py`, `update_data/_ui/ud_view.py`.
- Open questions:
  - >> Any additional intents needed from the file pane in the near term?

## 5) Guide Pane — Auto-Queue / Directory Discovery Toggle
- Purpose: per-folder discovery toggle (MVP golden path: new files in source root appear in file list).
- Change impact:
  - Move wiring to either: (a) a view interface registration method that presenter binds, or (b) a typed local intent consumed by presenter.
  - Presenter handles via `GuidePanePresenter` (no Qt coupling); future service integration (`DirectoryInfoService`).
  - Toggle text: "Enable file discovery for this folder".
- Key files: `update_data/_ui/_view/center_panel_components/guide_pane.py`, `GuidePanePresenter`, future `core/directory/models|services`.
- Open questions:
  - >> Prefer interface registration or typed local intent for the toggle?

## 6) Left Panel — Role Clarification
- Purpose: layout/aggregation of widgets; no signal re-emission.
- Change impact:
  - `UpdateDataView` calls widget APIs directly for reads/writes.
  - Labels sourced from `SourceOptions` / `SaveOptions` in `models/config.py` (single source of truth).
- Key files: `update_data/_ui/_view/left_panel_layout.py`, `.../widgets/widgets.py`, `models/config.py`.
- Open questions:
  - >> Any left panel behaviours that still rely on re-emitted signals we should delete?

## 7) Error Handling and Logging
- Purpose: fail-fast policy; do not obscure errors. Use custom logger.
- Change impact:
  - Remove broad try/except in `ud_view.py` and around adapters/dialogues.
  - Use `from fm.core.services.logger import log` for context where helpful; let errors surface.
- Key files: `update_data/_ui/ud_view.py`, `fm/core/services/logger`.
- Open questions:
  - >> Any areas where you want additional logging context?

## 8) Testing and Guardrails
- Purpose: protect lifecycle and UI syncing behaviour.
- Change impact:
  - Unit tests: `ProcessingManager.on_processing_started/..completed` call `set_processing` and `sync_state_to_view()` exactly once; no local `PROCESSING_*` emissions.
  - Smoke tests: left panel selections, process/cancel, centre "Add files" → `ADD_FILES_REQUESTED`.
- Open questions:
  - >> Any additional guardrails you want before enabling discovery by default?

---

## Summary of Expected Outcomes
- Cleaner MVP boundaries, zero Qt in presenter.
- Deterministic UI state changes via a single sync pathway.
- Reduced event noise; clearer responsibilities for interface vs events.
- Easier testing and future refactoring (file pane v2, directory discovery).

## References
- `src/fm/modules/update_data/_ui/ud_view.py`
- `src/fm/modules/update_data/interface/i_view_interface.py`
- `src/fm/modules/update_data/_ui/_view/center_panel_components/ud_file_view.py`
- `src/fm/modules/update_data/_ui/_view/center_panel_components/guide_pane.py`
- `src/fm/modules/update_data/_ui/_view/left_panel_layout.py`
- `src/fm/modules/update_data/models/config.py`
- `src/fm/modules/update_data/services/local_event_bus.py`
- `proposed_events.md` / `proposed_events.csv`
