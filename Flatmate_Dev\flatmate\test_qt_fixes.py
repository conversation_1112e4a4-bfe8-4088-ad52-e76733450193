#!/usr/bin/env python3
"""
Test script to verify Qt fixes for the file browser.
"""

import sys
import os
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PySide6.QtCore import Qt

def test_file_view():
    """Test the file view component with Qt fixes."""
    
    app = QApplication(sys.argv)
    
    # Load the stylesheets
    style_path = Path(__file__).parent / "src" / "fm" / "gui" / "styles"
    
    # Load theme.qss
    theme_qss_path = style_path / "theme.qss"
    if theme_qss_path.exists():
        with open(theme_qss_path, 'r') as f:
            theme_qss = f.read()
        app.setStyleSheet(theme_qss)
        print("✓ Loaded theme.qss")
    else:
        print("✗ Could not find theme.qss")
        return
    
    # Create main window
    window = QMainWindow()
    window.setWindowTitle("Qt File Browser Test")
    window.resize(800, 600)
    
    # Create central widget
    central_widget = QWidget()
    window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    
    try:
        # Import and create the file view
        from fm.modules.update_data._ui._view.center_panel_components.ud_file_view import UDFileView
        from fm.modules.update_data._ui._view.center_panel_components.file_pane_v2.config import FileConfig
        
        # Create file view with default config
        config = FileConfig.default()
        file_view = UDFileView(config)
        
        # Add some test files
        test_files = [
            str(Path(__file__).parent / "README.md"),
            str(Path(__file__).parent / "pyproject.toml"),
            __file__
        ]
        
        # Filter to only existing files
        existing_files = [f for f in test_files if Path(f).exists()]
        file_view.set_files(existing_files)
        
        layout.addWidget(file_view)
        
        print(f"✓ Created file view with {len(existing_files)} test files")
        print("✓ Qt fixes applied:")
        print("  - Button container changed from QWidget to QFrame")
        print("  - Object name changed to 'TableViewToolbar'")
        print("  - Button types changed to 'primary'/'secondary'")
        print("  - QSizePolicy enum usage fixed")
        print("  - Theme.qss toolbar styles uncommented")
        
    except Exception as e:
        print(f"✗ Error creating file view: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # Show window
    window.show()
    
    print("\n🎯 Test window opened. Check the file browser styling:")
    print("  - Toolbar should have subtle border")
    print("  - Add Files button should be primary styled (green)")
    print("  - Remove Selected button should be secondary styled")
    print("  - File tree should have proper styling")
    print("\nClose the window to exit.")
    
    # Run the application
    sys.exit(app.exec())

if __name__ == "__main__":
    test_file_view()
