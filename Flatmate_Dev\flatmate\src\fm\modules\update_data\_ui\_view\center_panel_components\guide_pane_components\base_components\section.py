from __future__ import annotations

from typing import Optional

from PySide6.QtWidgets import QWidget, QVBoxLayout, QSizePolicy
from PySide6.QtCore import Qt

from fm.gui._shared_components.widgets.labels import SubheadingLabel, InfoLabel
from .slot_row import SlotRow


class Section(QWidget):
    """
    Simple vertical section block with a header, optional info line, and a body container.
    """

    def __init__(self, title: str, parent: Optional[QWidget] = None, *, row_spacing: int = 6) -> None:
        super().__init__(parent)
        self.setObjectName("gp2_section")
        # Size to contents vertically; do not expand to fill
        self.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Minimum)
        self._root = QVBoxLayout(self)
        self._root.setContentsMargins(0, 0, 0, 0)
        # Tight spacing to avoid visible gap between header and content
        self._root.setSpacing(0)

        self.header = SubheadingLabel(title)
        # Remove any extra space below the header
        self.header.setContentsMargins(0, 0, 0, 0)
        try:
            self.header.setStyleSheet("margin-bottom: 0px;")
        except Exception:
            pass
        self._root.addWidget(self.header)

        self.info = InfoLabel("")
        self.info.setWordWrap(True)
        self.info.setVisible(False)
        # Default visual behaviour: left-aligned, normal weight/style
        try:
            self.info.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        except Exception:
            pass
        # Force neutral style to avoid theme making it bold/italic
        try:
            self.info.setStyleSheet("font-style: normal; font-weight: normal;")
        except Exception:
            pass
        # Wrap info label in a SlotRow to get consistent left/right padding
        self.info_row = SlotRow(horizontal_padding=8)
        # Add a small top gap above the info row so it's not glued to the header
        # We do this by giving the label a small top margin, which affects the row's height.
        try:
            m = self.info.contentsMargins()
            self.info.setContentsMargins(m.left(), max(4, m.top()), m.right(), m.bottom())
        except Exception:
            pass
        # Start with zero height when empty
        sp = self.info.sizePolicy()
        sp.setVerticalPolicy(QSizePolicy.Policy.Fixed)
        self.info.setSizePolicy(sp)
        self.info.setMinimumHeight(0)
        self.info.setMaximumHeight(0)
        self.info_row.set_content(self.info)
        self.info_row.set_visible(False)
        # Add rows inside the body for consistent spacing with other slots
        # Body container for arbitrary children (rows, labels, etc.)
        self.body = QWidget(self)
        self.body_layout = QVBoxLayout(self.body)
        # Top margin to create a consistent gap under the header
        self.body_layout.setContentsMargins(0, 4, 0, 0)
        # Section owns inter-row spacing between SlotRow children
        self.body_layout.setSpacing(max(0, int(row_spacing)))
        self._root.addWidget(self.body)
        # Now add the info_row as the first row in the body
        self.body_layout.addWidget(self.info_row)

    def set_row_spacing(self, pixels: int) -> None:
        """Set spacing between SlotRow children (horizontal padding is controlled by SlotRow)."""
        self.body_layout.setSpacing(max(0, int(pixels)))

    # --- API ---
    def set_info(self, text: str) -> None:
        value = text or ""
        self.info.setText(value)
        if value.strip():
            # Show row with natural sizing
            self.info_row.set_visible(True)
            self.info.setVisible(True)
            self.info.setMinimumHeight(0)
            self.info.setMaximumHeight(16777215)
            sp = self.info.sizePolicy()
            sp.setVerticalPolicy(QSizePolicy.Policy.Minimum)
            self.info.setSizePolicy(sp)
        else:
            # Hide and collapse to zero height (row + label)
            self.info_row.set_visible(False)
            self.info.setVisible(False)
            sp = self.info.sizePolicy()
            sp.setVerticalPolicy(QSizePolicy.Policy.Fixed)
            self.info.setSizePolicy(sp)
            self.info.setMinimumHeight(0)
            self.info.setMaximumHeight(0)

    def clear_info(self) -> None:
        self.set_info("")

    def set_info_alignment(self, alignment: Qt.AlignmentFlag) -> None:
        """Set alignment for the info label (e.g., Qt.AlignLeft | Qt.AlignVCenter)."""
        try:
            self.info.setAlignment(alignment)
        except Exception:
            # Fail fast elsewhere; alignment not critical
            pass

    def set_info_indent(self, pixels: int) -> None:
        """Apply a left indent to the info label via contents margins, preserving other margins.
        If wrapped in a SlotRow, prefer adjusting the row's horizontal padding.
        """
        try:
            if hasattr(self, "info_row") and isinstance(self.info_row, SlotRow):
                self.info_row.set_horizontal_padding(max(0, int(pixels)))
            else:
                m = self.info.contentsMargins()
                self.info.setContentsMargins(max(0, int(pixels)), m.top(), m.right(), m.bottom())
        except Exception:
            self.info.setContentsMargins(max(0, int(pixels)), 0, 0, 0)

    def add_to_body(self, widget: QWidget) -> None:
        self.body_layout.addWidget(widget)
