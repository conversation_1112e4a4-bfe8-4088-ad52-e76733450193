from __future__ import annotations

from typing import Callable, Optional, Tuple

from PySide6.QtWidgets import QWidget, QVBoxLayout

from .key_value_row import KeyValueRow


class FolderPathRow(QWidget):
    """
    Purpose-designed row that displays a folder path using the project's
    formatting rules. Internally composes a `KeyValueRow("Folder:")`.

    The formatter callable is expected to return (html, tooltip) given a raw path.
    """

    def __init__(
        self,
        parent: Optional[QWidget] = None,
        *,
        formatter: Optional[Callable[[str], Tuple[str, str]]] = None,
    ) -> None:
        super().__init__(parent)
        self._formatter: Optional[Callable[[str], Tuple[str, str]]] = formatter
        self._raw_path: Optional[str] = None

        self._layout = QVBoxLayout(self)
        self._layout.setContentsMargins(0, 0, 0, 0)
        self._layout.setSpacing(0)

        # No left-hand heading. Construct KeyValueRow with empty key to avoid
        # introducing a label we didn't ask for and to remove left gutter.
        self._row = KeyValueRow("")
        self._layout.addWidget(self._row)
        # Keep path on a single line; prevent drop to next line
        try:
            self._row.value_label.setWordWrap(False)
        except Exception:
            pass

    # --- API ---
    def set_formatter(self, formatter: Callable[[str], Tuple[str, str]]) -> None:
        self._formatter = formatter
        # Re-apply current value if any
        if self._raw_path:
            self.set_path(self._raw_path)

    def clear_path(self) -> None:
        self._raw_path = None
        self._row.set_value_html("")
        self._row.setToolTip("")

    def set_path(self, path: str) -> None:
        self._raw_path = path or None
        if not self._raw_path:
            self.clear_path()
            return
        if self._formatter:
            html, tip = self._formatter(self._raw_path)
            self._row.set_value_html(html)
            self._row.setToolTip(tip)
        else:
            # Fallback: show raw path plainly if no formatter provided
            self._row.set_value_html(self._raw_path)
            self._row.setToolTip(self._raw_path)

    def has_path(self) -> bool:
        return bool(self._raw_path)
