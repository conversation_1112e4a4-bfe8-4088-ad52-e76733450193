# FileSelector API How-To (Canonical)

Status: Canonical guide for unified file/folder selection in Update Data  
Scope: How managers open dialogs, persist last directory, and publish canonical results

Overview
- The FileManager owns dialogs.
- The View is a switchboard and never opens dialogs directly for runtime flows.
- Sub-widgets (e.g., UDFileView) emit intent signals only; no direct dialogs.
- Results flow through FileListManager via FileListUpdatedEvent for rendering.

API Summary
- FileSelector.get_paths(selection_type, initial_dir, title, parent)
  - selection_type: "files" or "folder"
  - initial_dir: starting directory; pull from ud_config/ud_keys
  - title: window title text
  - parent: the view window
- Persistence:
  - Use ud_config.set_value(ud_keys.Paths.LAST_SOURCE_DIR, new_dir)
  - Read with ud_config.get_value(ud_keys.Paths.LAST_SOURCE_DIR, default=Path.home())

Standard Flows
1) Left Panel “Select files”
   - View translates button/option to SOURCE_SELECT_REQUESTED with SELECT_FILES
   - Presenter → FileManager._select_files()
   - FileSelector.get_paths("files", last_dir, "Select CSV Files to Process", parent=view)
   - On success, FileManager._process_selected_files([...], "files")
   - FileListManager.set_files([...], source_dir or "")
   - View renders via FileListUpdatedEvent

2) Left Panel “Select folder”
   - View emits SOURCE_SELECT_REQUESTED with SELECT_FOLDER
   - Presenter → FileManager._select_folder()
   - FileSelector.get_paths("folder", last_dir, "Select Folder Containing CSV Files", parent=view)
   - On success, set LAST_SOURCE_DIR to the folder, process selections
   - On empty, emit ERROR_DIALOG_REQUESTED (“No supported files found in the selected folder.”)

3) File Pane “Add files”
   - UDFileView emits add_files_requested
   - View translates to distinct "add_files_requested" channel
   - FileManager subscribes and calls _select_files() (same dialog as case 1)

Dialog Policy
- Managers/presenter never call QMessageBox directly.
- For user feedback, emit DialogRequestEvent:
  - View subscribes to ERROR_DIALOG_REQUESTED/SUCCESS_DIALOG_REQUESTED and shows QMessageBox as needed.
- View.show_error/show_success exist as shims and emit dialog-request events internally.

Code Anchors (clickable)
- View switchboard: [ud_view.py](flatmate/src/fm/modules/update_data/_ui/ud_view.py)
- FileManager selection logic: [file_management.py._select_files()](flatmate/src/fm/modules/update_data/_ui/_presenter/file_management.py:126), [file_management.py._select_folder()](flatmate/src/fm/modules/update_data/_ui/_presenter/file_management.py:145)
- Canonical list/emit: [file_list_manager.py](flatmate/src/fm/modules/update_data/_ui/_presenter/file_list_manager.py)
- Local bus + events enum: [local_event_bus.py](flatmate/src/fm/modules/update_data/services/local_event_bus.py)
- Typed events: [ui_events.py](flatmate/src/fm/modules/update_data/_ui/ui_events.py)

Examples

Example A: Opening Files Dialog (from FileManager)
- last_dir = ud_config.get_value(ud_keys.Paths.LAST_SOURCE_DIR, default=str(Path.home()))
- paths = FileSelector.get_paths("files", last_dir, "Select CSV Files to Process", parent=view)
- if paths: self._process_selected_files(paths, "files")

Example B: Opening Folder Dialog (from FileManager)
- last_dir = ud_config.get_value(ud_keys.Paths.LAST_SOURCE_DIR, default=str(Path.home()))
- paths = FileSelector.get_paths("folder", last_dir, "Select Folder Containing CSV Files", parent=view)
- if paths:
  - folder_path = os.path.dirname(paths[0])
  - ud_config.set_value(ud_keys.Paths.LAST_SOURCE_DIR, folder_path)
  - self._process_selected_files(paths, "folder", folder_path)
- else:
  - emit ERROR_DIALOG_REQUESTED (“No supported files found in the selected folder.”)

Troubleshooting
- Double dialogs:
  - Ensure File Pane uses "add_files_requested" and not SOURCE_SELECT_REQUESTED.
- Return values from View dialogs:
  - Don’t use return paths; dialogs are manager-owned.
- Headless testing:
  - For unit tests, simulate events and assert on FileListUpdatedEvent; avoid invoking real dialogs.

Cross-links
- Contracts registry: [ui_event_contracts.md](flatmate/src/fm/modules/update_data/_ui/ui_event_contracts.md)
- Deep-dive: [Update-Data-UI-Deep-Dive.md](flatmate/DOCS/_GUIDES/Update-Data-UI-Deep-Dive.md)
- Testing protocol: [testing_protocol.md](flatmate/DOCS/_PROTOCOLS/GUIDES/testing_protocol.md)

Status
- Adopted and reflected in FileManager/UDFileView/UpdateDataView wiring.