from __future__ import annotations

from typing import List, Optional
from pathlib import Path

from PySide6.QtCore import Qt, Signal, QSize, QPoint, QUrl
from PySide6.QtGui import QDesktopServices, QAction
from PySide6.QtWidgets import (
    QFrame,
    QWidget,
    QVBoxLayout,
    QLabel,
    QSizePolicy,
    QMenu,
)

# Shared labels (simple wrappers around Q<PERSON>abel for consistent styling)
from fm.gui._shared_components.widgets.labels import InfoLabel
from .guide_pane_components.base_components.slot_row import SlotRow
from .guide_pane_components.source_section import SourceSection
from .guide_pane_components.archive_section import ArchiveSection
from fm.core.services.logger import log


class GuidePaneV2(QFrame):
    """
    Minimal Guide Pane widget (Phase 1 MVP stub).

    - Always shows Source and Archive sections.
    - Hides empty slot widgets; displays default messages when not populated.
    - Does NOT embed the file view; that remains a separate component.

    This is a standalone widget for review. No presenter/view-interface wiring yet.
    """
 
    # Emitted when the user toggles per-folder file discovery.
    # Signature matches UpdateDataView.on_enable_file_discovery_toggled(folder: str, enabled: bool)
    publish_enable_file_discovery_toggled = Signal(str, bool)

    def __init__(self, parent: Optional[QWidget] = None) -> None:
        super().__init__(parent)
        self.setFrameShape(QFrame.Shape.NoFrame)
        self.setObjectName("guide_pane_v2")
        self.setStyleSheet(
            "QFrame#guide_pane_v2 { border: 1px solid #4CAF50; border-radius: 6px; background-color: transparent; }"
        )
        self._archive_mode: str = "SameAsSource"  # or "SelectFolder"
        self._archive_path: Optional[str] = None
        self._source_path: Optional[str] = None
        self._archive_same_as_source: bool = True
        # Track which folder the Source context currently represents
        self._current_folder: Optional[str] = None
        # No registry. Sections own their fixed slots and API.

        self._build_ui()

    # ----- UI BUILD -----
    def _build_ui(self) -> None:
        root = QVBoxLayout(self)
        root.setContentsMargins(6, 4, 6, 6)
        root.setSpacing(2)
        # Make this widget size-to-contents vertically
        self.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Minimum)
        root.setSizeConstraint(QVBoxLayout.SizeConstraint.SetMinimumSize)

        # Main message row (dedicated, collapsible)
        self.main_msg = InfoLabel("")
        self.main_msg.setWordWrap(True)
        try:
            self.main_msg.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            # Neutral style to match the non-bold section info style
            self.main_msg.setStyleSheet("font-style: normal; font-weight: normal;")
            # Make copy-pastable and allow link activation
            self.main_msg.setTextInteractionFlags(
                Qt.TextInteractionFlag.TextSelectableByMouse | Qt.TextInteractionFlag.LinksAccessibleByMouse
            )
            self.main_msg.setOpenExternalLinks(True)
            m = self.main_msg.contentsMargins()
            # Trim vertical padding to be tight
            self.main_msg.setContentsMargins(m.left(), 0, m.right(), 0)
        except Exception:
            pass
        self.main_msg_row = SlotRow(horizontal_padding=8)
        self.main_msg_row.set_content(self.main_msg)
        self.main_msg_row.set_visible(False)
        root.addWidget(self.main_msg_row)

        # Source section (component)
        self.section_source = SourceSection()
        self.section_source.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Minimum)
        # Enable context menu on the source info label
        self.section_source.info.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.section_source.info.customContextMenuRequested.connect(
            lambda pos: self._show_path_context_menu(section="source", global_pos=self.section_source.info.mapToGlobal(pos))
        )
        # Wire per-folder toggle to our handler
        self.section_source.connect_enable_toggled(self._on_src_enable_toggled)
        # Show the default placeholder on first render until a Source path is provided
        self.section_source.show_message(True)
        root.addWidget(self.section_source)

        # Archive section (component)
        self.section_archive = ArchiveSection()
        self.section_archive.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Minimum)
        self.section_archive.set_info("Same as Source")
        # Enable context menu on the archive info label
        self.section_archive.info.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.section_archive.info.customContextMenuRequested.connect(
            lambda pos: self._show_path_context_menu(section="archive", global_pos=self.section_archive.info.mapToGlobal(pos))
        )

        root.addWidget(self.section_archive)

        # Do not force stretch; allow the pane to take only as much height as needed

    # ----- PUBLIC METHODS (widget API for review; not the final view interface) -----
    def set_main_message(self, text: Optional[str]) -> None:
        """Set the primary guidance message for the pane (dedicated slot)."""
        value = (text or "").strip()
        if value:
            self.main_msg.setText(value)
            self.main_msg_row.set_visible(True)
        else:
            self.clear_main_message()

    def clear_main_message(self) -> None:
        """Clear the primary guidance message and hide its row."""
        self.main_msg.setText("")
        self.main_msg_row.set_visible(False)
    # Minimal, generic slot API; containers are fixed and pre-registered above.

    def set_main_message_for_folder(self, *, prefix: str = "", folder: Optional[str]) -> None:
        """Set main message with a linkified folder path.

        - Uses _format_path_for_label to render parent+basename, bold basename.
        - Wraps the path label in an anchor to file:// so it is clickable.
        - Text remains copy-pastable.
        """
        if not folder:
            self.set_main_message(prefix)
            return
        html, tooltip = self._format_path_for_label(folder)
        from urllib.parse import quote as _urlq
        # Ensure file URL; use native path; QDesktopServices handles it
        file_url = f"file:///{_urlq(str(Path(folder)).as_posix())}"
        linked = f"<a href=\"{file_url}\" style='text-decoration:none; color:inherit;'>{html}</a>"
        msg = (prefix.strip() + " ").strip() + linked
        self.main_msg.setText(msg)
        self.main_msg.setToolTip(tooltip)
        self.main_msg_row.set_visible(True)

    def set_section_info(self, section_id: str, text: str, level: str = "info") -> None:
        if section_id == "source":
            candidate = self._extract_path_candidate(text)
            if self._looks_like_path(candidate):
                log.debug(f"[GuidePaneV2] set_section_info(source): bind folder context -> '{candidate}'")
                self._source_path = candidate
                html, tip = self._format_path_for_label(candidate)
                # Render into the Source path slot; info is only for non-path text
                self.section_source.set_path_html(html, tip)
                self.section_source.set_info("")
                self.section_source.info.setToolTip("")
                # Hide placeholder when we have a concrete source
                self.section_source.show_message(False)
                # Show per-folder options when a source path is present
                self.section_source.show_enable_option(True)
                # Bind current folder context
                self._current_folder = self._source_path
                log.debug(f"[GuidePaneV2] source context bound: current_folder='{self._current_folder}'")
                # If archive is set to Same as Source, update it immediately
                if self._archive_same_as_source:
                    base = self._source_path or self._current_folder
                    if base:
                        a_html, a_tip = self._format_path_for_label(base)
                        self.section_archive.set_info("")
                        self.section_archive.info.setToolTip("")
                        self.section_archive.set_path_html(a_html, a_tip)
            else:
                self._source_path = None
                # Clear path slot and handle info/placeholder rules
                self.section_source.clear_path()
                if text:
                    # Non-path summary goes to info label
                    self.section_source.set_info(text)
                    self.section_source.info.setToolTip(text)
                    self.section_source.show_message(False)
                else:
                    # Empty → show placeholder
                    self.section_source.set_info("")
                    self.section_source.info.setToolTip("")
                    self.section_source.show_message(True)
                # Hide per-folder options when no concrete source path
                self.section_source.show_enable_option(False)
                # Clear current folder binding
                self._current_folder = None
                log.debug("[GuidePaneV2] set_section_info(source): cleared source context; no folder bound")
        elif section_id == "archive":
            candidate = self._extract_path_candidate(text)
            if self._looks_like_path(candidate):
                self._archive_path = candidate
                html, tip = self._format_path_for_label(candidate)
                self.section_archive.set_info(html)
                self.section_archive.info.setToolTip(tip)
            else:
                self.section_archive.set_info(text)
                # keep tooltip only when a real path is shown
                if text and text != "Same as Source":
                    self.section_archive.info.setToolTip(text)
                else:
                    self.section_archive.info.setToolTip("")
        # Nudge layout to recalc height
        self.adjustSize()
        self.updateGeometry()

    def clear_section_info(self, section_id: str) -> None:
        self.set_section_info(section_id, "", "info")

    def set_source_slot_message(self, text: str) -> None:
        self.section_source.set_info(text)
        self.section_source.info.setVisible(bool(text))
        self.section_source.info_row.set_visible(bool(text))
        self.section_source.updateGeometry()

    def show_source_enable_option(self, visible: bool) -> None:
        self.section_source.show_enable_option(bool(visible))

    def set_source_enable_checked(self, checked: bool) -> None:
        self.section_source.set_enable_checked(checked)

    # --- New API aligned with Guide Pane v2 mapping ---
    def show_source_context_options(self, *, monitor_enabled: bool = False, discovery_enabled: bool = False, folder: Optional[str] = None) -> None:
        """Show per-folder discovery control and set its state.

        monitor_enabled is accepted for future use; currently informational only.
        discovery_enabled determines the checkbox state.
        Binding derives from the current Source path only.
        """
        # Prefer explicit folder if provided (e.g. presenter may call before the Source summary is set)
        target_folder = folder or self._source_path
        self._current_folder = target_folder if target_folder else None
        log.debug(
            f"[GuidePaneV2] show_source_context_options: folderParam='{folder}', resolved='{self._current_folder}', "
            f"discovery_enabled={bool(discovery_enabled)}"
        )
        # Show the option only when a concrete folder context exists
        has_context = bool(self._current_folder)
        self.section_source.show_enable_option(has_context)
        if has_context:
            # Set checkbox state; internal component blocks signals on programmatic updates
            self.section_source.set_enable_checked(bool(discovery_enabled))
            log.debug(f"[GuidePaneV2] set_enable_checked(programmatic): folder='{self._current_folder}', to={bool(discovery_enabled)}")
        # No badge indicator; checkbox alone reflects state
        self.adjustSize()
        self.updateGeometry()

    def set_discovery_badge(self, active: bool) -> None:
        """No-op: badge removed by design; checkbox shows state."""
        # Intentionally left blank per V2 design
        _ = active
        return

    # --- Source notice (secondary row) ---
    def set_source_notice(self, text: str) -> None:
        try:
            self.section_source.set_notice(text)
        except Exception:
            pass

    def clear_source_notice(self) -> None:
        try:
            self.section_source.clear_notice()
        except Exception:
            pass

    # --- Internal wiring ---
    def _on_src_enable_toggled(self, checked: bool) -> None:
        # Fail fast if no folder context - this should never happen in normal operation
        if not self._current_folder:
            error_msg = f"[GuidePaneV2] CRITICAL: Discovery toggle attempted without folder context. checked={bool(checked)}. This indicates a UI state management bug."
            log.error(error_msg)
            raise RuntimeError(f"Discovery toggle failed: no folder context set. UI state is inconsistent.")
        
        log.debug(f"[GuidePaneV2] user toggle: folder='{self._current_folder}', checked={bool(checked)}")
        self.publish_enable_file_discovery_toggled.emit(self._current_folder, bool(checked))


    def set_archive_mode(self, mode: str) -> None:
        # Keep API, but visibility is handled by ArchiveSection path slot state
        self._archive_mode = mode
        try:
            self.adjustSize()
            self.updateGeometry()
        except Exception:
            pass

    def set_archive_summary(self, text: str) -> None:
        # Mirror Source: use section info line for archive summary
        # If summary equals 'Same as Source' and we have a known source path, render that path for consistency
        text_norm = (text or "").strip()
        self._archive_same_as_source = (text_norm.lower() == "same as source")
        # Case 1: Direct path string provided -> show in path slot; keep summary minimal
        candidate = self._extract_path_candidate(text_norm)
        if candidate and self._looks_like_path(candidate):
            self._archive_path = candidate
            html, tip = self._format_path_for_label(candidate)
            self.section_archive.set_info("")
            self.section_archive.info.setToolTip("")
            self.section_archive.set_path_html(html, tip)
            return

        # Case 2: Mirror Source (Same as Source)
        if self._archive_same_as_source and (self._source_path or self._current_folder):
            base = self._source_path or self._current_folder
            self._archive_path = base
            a_html, a_tip = self._format_path_for_label(base) if base else ("", "")
            self.section_archive.set_info("")
            self.section_archive.info.setToolTip("")
            self.section_archive.set_path_html(a_html, a_tip)
            return

        # Case 3: Plain text fallback (non-path summary)
        self.section_archive.clear_path()
        self.section_archive.set_info(text_norm or "")
        self.section_archive.info.setToolTip("")

    def set_archive_path(self, path: Optional[str]) -> None:
        # Value-driven visibility: put the actual path into the archive path slot
        self._archive_path = path or None
        if self._archive_path:
            a_html, a_tip = self._format_path_for_label(self._archive_path)
            self.section_archive.set_path_html(a_html, a_tip)
            self.section_archive.set_info("")
            self.section_archive.info.setToolTip("")
        else:
            self.section_archive.clear_path()
            # Leave summary untouched here

    # ----- INTERNAL HELPERS -----
    def _looks_like_path(self, text: Optional[str]) -> bool:
        if not text:
            return False
        try:
            p = Path(text)
        except Exception:
            return False
        # Accept absolute paths or '~' home-relative inputs
        if p.is_absolute():
            return True
        s = str(text).strip()
        return s.startswith("~/") or s.startswith("~\\") or s == "~"

    def _format_path_for_label(self, path_str: str) -> tuple[str, str]:
        """Return (rich_text, tooltip) for guide pane labels.

        - Replace user home prefix with '~'.
        - Use native separators.
        - Bold basename; show parent in dim monospace.
        """
        # Expand '~' to home for processing, but keep '~' in display per spec
        raw = path_str
        if raw.strip().startswith("~"):
            try:
                expanded = str(Path.home() / raw.strip().lstrip("~\\/") )
            except Exception:
                expanded = raw
        else:
            expanded = raw

        # Restore actual on-disk casing when path exists
        try:
            expanded_cased = self._restore_actual_casing(expanded)
        except Exception:
            expanded_cased = expanded

        p = Path(expanded_cased)
        full_native = str(p)
        try:
            full_native = str(p)
        except Exception:
            pass

        # Build display with ~ for home (robust check)
        import os as _os
        home = Path.home()
        parent = p.parent
        p_str = str(p)
        h_str = str(home)
        disp_parent: str
        try:
            # Normalise for comparison (case-insensitive on Windows)
            p_norm = Path(p_str).resolve(strict=False)
            h_norm = Path(h_str).resolve(strict=False)
            # Use commonpath to detect if p is within home
            common = _os.path.commonpath([str(p_norm), str(h_norm)])
            is_under_home = (common.lower() == str(h_norm).lower())
        except Exception:
            is_under_home = p_str.lower().startswith(h_str.lower())

        if is_under_home:
            # Preserve native separator style from the target path
            sep = "\\" if "\\" in p_str else "/"
            remainder = p_str[len(h_str):]
            if remainder.startswith("/") or remainder.startswith("\\"):
                rel = "~" + remainder
            else:
                rel = "~" + sep + remainder
            disp_parent = str(Path(rel).parent)
        else:
            disp_parent = str(parent)

        basename = p.name
        # Compose single-line HTML
        html = (
            f"<span style='font-family:Consolas,monospace; color:#9AA0A6; font-size:0.9em;'>" 
            f"{disp_parent}{(chr(92) if chr(92) in full_native else '/')}" 
            f"</span><b>{basename}</b>"
        )
        # Normalise drive letter casing in tooltip on Windows
        try:
            import os as _os
            if _os.name == 'nt':
                drv, rest = _os.path.splitdrive(expanded_cased)
                if drv:
                    expanded_cased = drv.upper() + rest
        except Exception:
            pass
        tooltip = expanded_cased
        return html, tooltip

    def _restore_actual_casing(self, path_str: str) -> str:
        """Return the path with actual filesystem casing for each component, if possible.

        - If the path (or its parents) do not exist, return the original.
        - Avoids os.path.normcase (which lowercases on Windows).
        - Uses directory listings to recover the correct case per segment.
        """
        p = Path(path_str)
        # Handle drive letter specially on Windows (preserve original drive case)
        drive, tail = (p.drive, p.relative_to(p.anchor)) if p.is_absolute() else ("", p)

        # Start from root/anchor for absolute paths, else from current working dir
        base = Path(p.anchor) if p.is_absolute() else Path.cwd()
        current = base
        for part in tail.parts if str(tail) else []:
            try:
                if not current.exists():
                    return path_str
                # Case-insensitive match within current directory
                match = None
                for entry in current.iterdir():
                    if entry.name.lower() == part.lower():
                        match = entry
                        break
                current = Path(current / (match.name if match else part))
            except Exception:
                return path_str
        return str(current) if str(tail) else str(p)

    def _extract_path_candidate(self, text: Optional[str]) -> Optional[str]:
        if not text:
            return None
        s = str(text).strip()
        # Common patterns we inject: "Source: <path>", "Archive: <path>"
        # Only strip known prefixes; DO NOT split on ':' generally, to preserve 'C:\' drive letters
        low = s.lower()
        for prefix in ("source:", "archive:"):
            if low.startswith(prefix):
                return s[len(prefix):].strip() or None
        return s

    def _show_path_context_menu(self, *, section: str, global_pos: QPoint) -> None:
        # Only show when we actually have a file system path
        path_str: Optional[str] = None
        if section == "source":
            path_str = self._source_path
        elif section == "archive":
            path_str = self._archive_path

        if not path_str or not self._looks_like_path(path_str):
            return

        menu = QMenu(self)
        act_open = QAction("Open in Explorer", self)

        def _open():
            try:
                QDesktopServices.openUrl(QUrl.fromLocalFile(str(Path(path_str))))
            except Exception:
                # Fallback: do nothing silently per user rule (no heavy try/except); errors surface if needed
                pass

        act_open.triggered.connect(_open)
        menu.addAction(act_open)
        menu.exec(global_pos)

    # No file view embedding here by design.

    # SlotRow provides the single visibility API; no internal helper needed.
