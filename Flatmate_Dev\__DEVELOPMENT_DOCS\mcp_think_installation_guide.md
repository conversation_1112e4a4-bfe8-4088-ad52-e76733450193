# MCP Think Tool Installation Guide

This comprehensive guide covers all available methods for implementing the MCP "think" tool for <PERSON>, prioritized by reliability and ease of implementation for Windsurf IDE.

## Understanding the Think Tool

The "think" tool gives <PERSON> a dedicated space for structured thinking during complex problem-solving tasks, particularly useful for:
- Following policies and constraints
- Making sequential decisions
- Managing long chains of tool calls
- Complex reasoning tasks

Per <PERSON>throp<PERSON>'s official documentation, the think tool is implemented as a standard JSON tool specification.

## Implementation Options

### Option 1: Official Python Package (DannyMac180/mcp-think-tool)

This is a ready-made MCP server that implements the Anthropic think tool specification.

#### Steps:
1. Install the package via pip:
   ```bash
   pip install mcp-think-tool
   ```

2. Find the installation path:
   ```bash
   where mcp-think-tool  # Windows
   # OR
   which mcp-think-tool  # Unix/Linux
   ```

3. Update your Windsurf mcp_config.json:
   ```json
   "think": {
     "command": "C:\\Users\\<USER>\\.pyenv\\pyenv-win\\shims\\mcp-think-tool.exe",
     "args": [],
     "type": "stdio",
     "pollingInterval": 30000,
     "startupTimeout": 30000,
     "restartOnFailure": true
   }
   ```

#### Pros:
- Turnkey solution specifically designed for Windsurf/Claude
- Handles all implementation details
- Actively maintained GitHub repository
- Simplest configuration once path is found

#### Cons:
- Requires finding the exact executable path
- May have Python environment conflicts

### Option 2: Sequential Thinking MCP Server

The ModelContextProtocol implementation of sequential thinking.

#### Steps:
1. Install the package globally:
   ```bash
   npm install -g @modelcontextprotocol/server-sequential-thinking
   ```

2. Update your Windsurf mcp_config.json:
   ```json
   "think": {
     "command": "npx",
     "args": [
       "@modelcontextprotocol/server-sequential-thinking"
     ],
     "type": "stdio",
     "pollingInterval": 30000,
     "startupTimeout": 30000,
     "restartOnFailure": true
   }
   ```

#### Pros:
- Maintained by the MCP project
- Node.js based (no Python dependencies)
- Works without finding executable paths
- NPX handles version management

#### Cons:
- May not implement all features of the Anthropic spec
- Requires Node.js environment

### Option 3: Custom Implementation (Anthropic Spec)

Implement the Anthropic specification directly using the tool definition from their blog post.

#### Steps:
1. Create a new Python file `think_tool_server.py`:
   ```python
   import sys
   import json
   import time
   from datetime import datetime

   # Store thought history
   thoughts = []

   def handle_request(request_str):
       try:
           request = json.loads(request_str)
           thought = request.get("thought", "")
           
           # Record the thought with timestamp
           timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
           thoughts.append({"timestamp": timestamp, "thought": thought})
           
           # Return success response
           return json.dumps({
               "thought_recorded": True,
               "timestamp": timestamp,
               "thoughts_count": len(thoughts)
           })
       except Exception as e:
           return json.dumps({"error": str(e)})

   # Simple STDIO server loop
   while True:
       try:
           line = sys.stdin.readline()
           if not line:
               break
               
           response = handle_request(line.strip())
           sys.stdout.write(response + "\n")
           sys.stdout.flush()
       except Exception as e:
           sys.stderr.write(f"Error: {str(e)}\n")
           sys.stderr.flush()
   ```

2. Update your Windsurf mcp_config.json:
   ```json
   "think": {
     "command": "python",
     "args": ["C:\\path\\to\\think_tool_server.py"],
     "type": "stdio",
     "pollingInterval": 30000,
     "startupTimeout": 30000,
     "restartOnFailure": true
   }
   ```

3. Add the tool definition to your system prompt:
   ```json
   {
     "name": "think",
     "description": "Use the tool to think about something. It will not obtain new information or change the database, but just append the thought to the log. Use it when complex reasoning or some cache memory is needed.",
     "input_schema": {
       "type": "object",
       "properties": {
         "thought": {
           "type": "string",
           "description": "A thought to think about."
         }
       },
       "required": ["thought"]
     }
   }
   ```

#### Pros:
- Complete control over implementation
- No external dependencies
- Can be customized for specific needs
- Follows official Anthropic specification

#### Cons:
- Requires manual implementation and maintenance
- Basic version lacks advanced features

### Option 4: Docker Implementation

For systems that prefer containerization.

#### Steps:
1. Create a Dockerfile with contents:
   ```dockerfile
   FROM python:3.9-slim
   RUN pip install mcp-think-tool
   ENTRYPOINT ["mcp-think-tool"]
   ```

2. Build the Docker image:
   ```bash
   docker build -t mcp-think-tool .
   ```

3. Update your Windsurf mcp_config.json:
   ```json
   "think": {
     "command": "docker",
     "args": ["run", "--rm", "-i", "mcp-think-tool"],
     "type": "stdio",
     "pollingInterval": 30000,
     "startupTimeout": 30000,
     "restartOnFailure": true
   }
   ```

#### Pros:
- Isolates dependencies
- Works regardless of local Python setup
- Portable across environments
- Easy to version and update

#### Cons:
- Requires Docker installation
- Container overhead
- Not currently available on your system

### Option 5: Virtual Environment Installation

Isolate Python dependencies while still using the official package.

#### Steps:
1. Create and activate a virtual environment:
   ```bash
   python -m venv mcp_think_env
   # Windows
   mcp_think_env\Scripts\activate
   # Unix/Linux
   source mcp_think_env/bin/activate
   ```

2. Install the package:
   ```bash
   pip install mcp-think-tool
   ```

3. Update your Windsurf mcp_config.json:
   ```json
   "think": {
     "command": "C:\\path\\to\\mcp_think_env\\Scripts\\mcp-think-tool.exe",
     "args": [],
     "type": "stdio",
     "pollingInterval": 30000,
     "startupTimeout": 30000,
     "restartOnFailure": true
   }
   ```

#### Pros:
- Isolated environment prevents conflicts
- Clean installation
- Suitable for Windows environments

#### Cons:
- More complex setup
- Additional disk space usage

## Anthropic Best Practices for Using the Think Tool

According to Anthropic's research:

1. **Strategic Prompting**
   - Include domain-specific examples
   - Show expected level of detail in reasoning
   - Demonstrate breaking down complex tasks into steps

2. **System Prompt Configuration**
   - Place complex guidelines in the system prompt rather than tool description
   - Provide clear instructions on when to use the tool

3. **When NOT to Use**
   - Non-sequential tool calls (single tool calls)
   - Simple instruction following

## Troubleshooting

### Common Issues:

1. **Command not found**: Verify the path in your mcp_config.json matches the actual executable location.
   
2. **Cannot find module**: Ensure the package is correctly installed in the environment your command points to.

3. **Tool not appearing in Claude**: Check Windsurf logs for MCP connection errors.

4. **Performance issues**: Make sure system prompt includes strategic guidance on when to use the tool.

### Quick Fixes:

1. Run `pip show mcp-think-tool` to verify installation and location.

2. For NPM implementations, run `npm list -g @modelcontextprotocol/server-sequential-thinking`.

3. Try running the command manually in a terminal to verify it works.

4. Check mcp_config.json format and syntax.

## Recommendations for Windsurf

1. **Best Option for Windows**: Option 2 (Sequential Thinking MCP) if you've already installed it globally with NPM.

2. **Most Reliable**: Option 1 (Python Package) for the official implementation.

3. **Most Flexible**: Option 3 (Custom Implementation) if you need to customize behavior.

4. **Future-Proofing**: Option 4 (Docker) once Docker is available on your system.

## Performance Optimization

For optimal performance in Windsurf:

1. Add strategic prompting examples to your Claude system prompt.
2. Place complex guidelines for the think tool in the system prompt.
3. Monitor Claude's usage and adjust prompting as needed.
4. Only use for complex sequential tasks; avoid for simple tasks.
