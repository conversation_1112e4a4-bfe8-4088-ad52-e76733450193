"""
Right panel layout for the Update Data module.
Manages the settings and options panel.
"""

from PySide6.QtCore import Signal
from PySide6.QtWidgets import QLabel, QVBoxLayout

from fm.gui._shared_components import BasePanelComponent
from .right_panel_components.options_pane import OptionsPane


class RightPanelLayout(BasePanelComponent):
    """Layout for the right panel containing settings and options."""
    
    # Signals for publishing events to subscribers
    publish_setting_changed = Signal(str, object)  # Setting name, new value
    
    def __init__(self, parent=None):
        """Initialize the right panel layout."""
        super().__init__(parent)
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Panel title
        title = QLabel("Settings")
        title.setObjectName("panel_title")
        layout.addWidget(title)
        
        # Options pane
        self.options_pane = OptionsPane()
        layout.addWidget(self.options_pane)
        
        # Add spacer
        layout.addStretch(1)
    
    def _connect_signals(self):
        """Connect signals to handlers."""
        # Subscribe to options pane signals
        self.options_pane.publish_setting_changed.connect(self._on_setting_changed)
    
    def _on_setting_changed(self, setting_name, new_value):
        """Handle setting change."""
        # Forward the signal
        self.publish_setting_changed.emit(setting_name, new_value)

    def show_component(self):
        """Show the right panel."""
        self.setVisible(True)

    def hide_component(self):
        """Hide the right panel."""
        self.setVisible(False)
