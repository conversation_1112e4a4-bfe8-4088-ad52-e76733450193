# Handover: Update Data — Auto-Queue Toggle (MVP)

Date: 2025-08-08 20:26:22 +12:00
Owner: Update Data module team
Status: Implemented; ready for QA

## Objective
Deliver a minimal per-folder Auto-Queue toggle in the Guide Pane. The UI emits the folder path with the toggle intent; the presenter delegates persistence and monitoring reconciliation.

## Key Decisions
- Presenter remains free of Qt and uses intent subscription only.
- The toggle intent carries payload `{ folder: str, enabled: bool }` (pragmatic MVP).
- UI renders strictly from persisted config (single source of truth) via `ud_config`.
- Toggle is only shown/enabled when a single folder context is resolved.

## Architecture Overview
- View emits domain intent on local bus: `AUTO_QUEUE_TOGGLED` with `{folder, enabled}`.
- Presenter subscribes and delegates to `AutoQueueManager`.
- `AutoQueueManager`:
  - Persists via `ud_config.set_auto_queue_enabled(folder, enabled)`
  - Reconciles monitoring via `FolderMonitorService.set_folder_monitored(folder, enabled)`
- State is synced to the View via `StateManager.sync_state_to_view()`.

## Files Touched
- `src/fm/modules/update_data/_ui/_view/center_panel_components/guide_pane.py`
  - Signal: `publish_toggle_auto_queue_requested` now `Signal(str, bool)`
  - `show_source_context_options(..., folder=...)` caches folder and emits `(folder, enabled)`
- `src/fm/modules/update_data/_ui/ud_view.py`
  - Bridges Guide Pane signal to local bus as `{"folder": str, "enabled": bool}`
  - `show_guide_source_options(..., folder=...)` forwards folder to Guide Pane
- `src/fm/modules/update_data/_ui/_presenter/state_coordinator.py`
  - Passes `folder_path` into `view.show_guide_source_options(..., folder=folder_path)`
  - Reads checkbox state strictly from `ud_config.is_auto_queue_enabled(folder)`
- `src/fm/modules/update_data/ud_presenter.py`
  - Subscribes to `ViewEvents.AUTO_QUEUE_TOGGLED`
  - Handler expects dict payload and calls `AutoQueueManager.set_auto_queue(...)`
- NEW: `src/fm/modules/update_data/_ui/_presenter/auto_queue_manager.py`
  - Minimal manager for persistence + monitoring reconciliation
- NEW DOCS:
  - `Auto-Queue_Toggle_Payload_Decision.md` (decision rationale)
  - `Session_Log_250808.md` (today’s session summary)

## Public Interface Changes
- View method: `show_guide_source_options(monitor_enabled=False, auto_queue_enabled=False, folder: Optional[str] = None)`
- Guide Pane signal: `publish_toggle_auto_queue_requested(str, bool)`
- Local bus event: `ViewEvents.AUTO_QUEUE_TOGGLED` now carries dict payload

## Configuration & Services
- Config API used:
  - `ud_config.is_auto_queue_enabled(folder) -> bool`
  - `ud_config.set_auto_queue_enabled(folder, enabled) -> None`
- Service API used:
  - `FolderMonitorService.set_folder_monitored(folder, enabled) -> None`

## QA Checklist
- Single folder source
  - Toggle on/off persists and reflects after restart; monitoring state reconciles
- Files source (single parent folder inferred)
  - Toggle appears and works identically
- Ambiguous/multi-folder
  - Toggle hidden/disabled; no event emitted
- Error path
  - If monitoring call fails, preference still persists and error logged

## Known Risks / Follow-ups
- Ensure `ViewEvents.AUTO_QUEUE_TOGGLED` exists and is routed by the local bus
- Ensure `ud_config.set_auto_queue_enabled(...)` is implemented
- Optional hardening: normalise folder path (`os.path.normpath`) in `AutoQueueManager`
- Audit any legacy connections expecting the old `bool`-only signal from Guide Pane

## How to Test (Manual)
1) Launch Update Data module
2) Select a folder; verify checkbox reflects persisted value
3) Toggle ON → Confirm:
   - Preference persisted (restart and check)
   - Monitoring started for that folder
4) Toggle OFF → Confirm monitoring stopped and persistence holds
5) Select files from a single folder; repeat steps 2–4

## Rollback Plan
- Revert the six touched files above to previous commits
- Remove/ignore the `AutoQueueManager` file

## Ownership & Next Steps
- Team to complete QA passes and decide on path normalisation
- Plan a configuration pane for multi-folder management post-MVP

## Related Documents

* [Auto-Queue Toggle Architecture Overview](./Auto-Queue_Toggle_Architecture_Overview.md)
* [Folder Handling Discussion](./Folder_Handling_Discussion.md)
* [Auto-Queue Toggle Payload Decision](./Auto-Queue_Toggle_Payload_Decision.md)
* [Session Log (2025-08-08)](./Session_Log_250808.md)
* [Implementation Guide & Checklist](./Auto-Queue_Implementation_Guide_and_Checklist.md)
* [Update-Data UI Deep Dive](../../../DOCS/_GUIDES/Update-Data-UI-Deep-Dive.md)
