"""
Auto-Queue Manager

Minimal manager to persist per-folder auto-queue preference and reconcile
FolderMonitorService state. Keeps presenter free of config/service details.

MVP: two methods only.
"""
from __future__ import annotations

from typing import Protocol

from fm.core.services.logger import log


class _ConfigProto(Protocol):
    def set_auto_queue_enabled(self, folder: str, enabled: bool) -> None: ...
    def is_auto_queue_enabled(self, folder: str) -> bool: ...


class _FolderMonitorProto(Protocol):
    def set_folder_monitored(self, folder: str, enabled: bool) -> None: ...


class AutoQueueManager:
    def __init__(self, config: _ConfigProto, folder_monitor_service: _FolderMonitorProto) -> None:
        self._config = config
        self._monitor = folder_monitor_service

    # ---------------------------------------------------------------------
    # API
    # ---------------------------------------------------------------------
    def set_auto_queue(self, folder: str, enabled: bool) -> None:
        if not folder:
            raise ValueError("folder must be a non-empty path")
        # Persist first (single source of truth)
        self._config.set_auto_queue_enabled(folder, bool(enabled))
        log.debug(f"[AutoQueueManager] Persisted auto-queue: folder='{folder}', enabled={enabled}")
        # Reconcile monitoring to match preference
        try:
            self._monitor.set_folder_monitored(folder, bool(enabled))
            log.debug(f"[AutoQueueManager] Reconciled monitor for '{folder}' => {enabled}")
        except Exception as e:
            # Loud but non-fatal to UI flow; state persists even if monitoring fails
            log.error(f"[AutoQueueManager] Failed to set monitor for '{folder}': {e}")

    def get_auto_queue(self, folder: str) -> bool:
        if not folder:
            return False
        try:
            return bool(self._config.is_auto_queue_enabled(folder))
        except Exception as e:
            log.error(f"[AutoQueueManager] Error reading auto-queue for '{folder}': {e}")
            return False
