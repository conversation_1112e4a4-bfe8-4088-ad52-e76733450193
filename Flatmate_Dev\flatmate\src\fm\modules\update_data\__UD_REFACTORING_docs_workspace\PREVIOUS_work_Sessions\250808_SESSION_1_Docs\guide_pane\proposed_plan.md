# Guide Pane – Proposed Plan (MVP)

Short, pragmatic plan to refine the guide pane behaviour and contracts. UK spelling; avoid speculative clutter.

## Objective
- Provide clear, contextual guidance while updating data:
  - Status of current action/state (e.g., ready/processing)
  - Persistent archive summary line
  - Single per‑folder option: Auto‑queue new files (implicitly starts/stops monitoring)
- Maintain strict MVP: presenter ↔ view via interface; events only when needed.

## Scope (current iteration)
- Status and instructions: state‑based messages (ready, processing, success, warning)
- Archive summary: dedicated label – "Archive: Same as Source" or selected path
- Contextual option: Auto‑queue new files (per folder)
- Clean wiring: view interface methods and local bus for the single toggle

## Presenter ↔ View Contracts
- Interface (`IUpdateDataView`):
  - `set_guide_archive_summary(text: str)`
  - `show_guide_source_options(monitor_enabled: bool = False, auto_queue_enabled: bool = False)`
    - Note: `monitor_enabled` is deprecated and ignored; only `auto_queue_enabled` is used.
  - Existing: `set_archive_options(...)`, `set_source_options(...)`, dialogs, etc.
- View (`UpdateDataView`):
  - Implements the interface; delegates to `GuidePaneWidget`
  - Wires widget signals to `ViewEvents` (local bus)
- Widget (`GuidePaneWidget`):
  - `display(...)`, `set_status(state, **context)`, `set_archive_summary(text)`, `show_source_context_options(...)`
  - Emits: `publish_toggle_auto_queue_requested(bool)`

## Events (Local Bus)
- `AUTO_QUEUE_TOGGLED` – single event for per‑folder auto‑queue (monitoring implied)

## Current‑state UX (baseline)
When a source folder is selected, files are queued, and archive is "Same as Source":
- Status: "Ready to process N files" (info)
- Archive summary: "Archive: Same as Source"
- Option: `[ ] Auto‑queue new files` (default from per‑folder prefs)
- Process button conveys count (outside the guide pane)

## Tasks
- [x] Add archive summary label and view method
- [x] Add contextual options API in widget + view
- [ ] Define and wire `AUTO_QUEUE_TOGGLED` in `ViewEvents`
- [ ] Remove "Monitor Folder" control from widget UI; show only auto‑queue
- [ ] Presenter: call `show_guide_source_options(auto_queue_enabled=...)` when source context becomes active (use per‑folder prefs)
- [ ] Presenter: ensure `set_status('ready', count=N)` is sent when files are queued

## Preferences
- `update_data.monitor_folder` (bool, default False)
- `update_data.auto_queue_new_files` (bool, default False)
- `update_data.quick_select_recent_source` (existing, source MRU only)

Per‑folder map:
- `update_data.auto_queue_by_folder: { "<folder_path>": true|false }`

## Testing Checklist
- Guide shows initial instructions; on source selection shows ready status and options
- Archive summary updates on save option changes (Same as Source vs custom/MRU path)
- Toggling Auto‑queue emits `AUTO_QUEUE_TOGGLED` with `{ folder_path, enabled }`
- No Qt usage in presenter; interface calls only

## Out of Scope (this iteration)
- Advanced history/log in guide pane
- Cross‑module notifications beyond local bus
- Complex preferences UI
