# Guide Pane – Observations and Ideal Behaviour (Review Draft)

This document itemises current observations and, under each, how it should work in a "perfect world". Concise, developer‑facing, predictable, and easy to configure/format.

- __Source summary blank; placeholder not visible__
  - Observation: `Source` header shows but the info line is blank and the placeholder row (`src_slot_row`) is not visible.
  - Ideal behaviour: When a real Source path is set, show formatted summary in `section_source.info` and hide the placeholder. When Source is unset/empty, hide the summary and show placeholder via `src_slot_row.set_visible(True)`.
  >> NO the problem is that empty slots ARE visible. 
  >> the other problem is that *No files or foldes selected* is NOT visible when there is no source path. 
 >> fundamentally the archive section is not informing the user, and its a massive empty void taking up space 
 not much of a guide is it ?
- __Archive summary shows; folder row hidden__
  - Observation: Archive info line displays formatted path (with `~`), but the dedicated "Folder:" row (`arc_path_container` + `KeyValueRow`) is hidden.
  - Ideal behaviour: Always show the Archive summary line. Show the "Folder:" row only when `set_archive_mode("SelectFolder")`; hide when `"SameAsSource"`.
  >> NO. the folder row is empty so it f'n well should be hidden. the question is , if we have a designated folder row why isnt the folder path in **that* f'ing row?

- __Single visibility API for rows__
  - Observation: Mixed helpers existed previously; now `SlotRow.set_visible(bool)` is available.
  - Ideal behaviour: All row visibility goes through `SlotRow.set_visible()`. No ad‑hoc `setVisible()/min/max height` code elsewhere. Hidden rows take zero space.
  > yes I thought we already sorted this? or did you fuck around and not actually implement th specifie changes? leaving legacy slop in the code !? 

- __Zero‑gap collapse; spacing ownership__
  - Observation: Collapsed rows appear to leave no gaps; spacing looks consistent.
  - Ideal behaviour: `Section` owns inter‑row spacing via `row_spacing` (default 6). `SlotRow` enforces zero vertical padding; only horizontal padding is configurable per row.
    >> what the fuck are talking about the whole fucking guide pane is fiull of empty fucking rows all over the place!
- __Horizontal padding configuration__
  - Observation: Option/detail rows benefit from a small left/right inset.
  - Ideal behaviour: Use `SlotRow.set_horizontal_padding(8)` for option/detail rows; keep 0 for placeholder rows unless needed. Vertical padding remains 0 always.

- __Presenter responsibilities (dev‑friendly contract)__
  - Observation: Inconsistent updates can leave a mixed UI state (e.g., summary not set while placeholder hidden).
  - Ideal behaviour: Presenter calls explicit high‑level view methods only:
    - `set_section_info("source", text)` for Source summary
    - `set_source_slot_message(text)` for placeholder
    - `set_archive_mode(mode)` and `set_section_info("archive", text)` for Archive
    The view manages row visibility predictably.
   - >>aRCHIVE MODE ~? WHAT IS THIS ? RIGHT NOW WE ARE CONCENTRATING ON LAYOUT, Tthe important  functionality has been working so deal with this shit later 

- __Formatting rules centralised in the view__
  - Observation: Archive formatting correct; ensure Source follows same rules.
  - Ideal behaviour: View formats paths uniformly (tilde for home, bold basename, dim monospace parent; tooltip = full absolute path with correct casing). Presenter supplies raw paths only.
>> Is it ? whats the empty spaceat the bottom,why is the foldrer path in the fcuking summary row?
- __Defaults for predictable behaviour__
  - Observation: Rows need clean defaults to avoid surprises.
  - Ideal behaviour: All `SlotRow`s start hidden by default. Placeholder row shows only when the corresponding summary text is empty. Archive folder row visibility follows `set_archive_mode()`. >> 
  by archive "mode" I asume we mean Same as Source, or an actual dir path 

- __Error handling and logging__
  - Observation: Broad try/except can hide layout issues.
  - Ideal behaviour: Fail fast. Use `from fm.core.services.logger import log` for targeted diagnostics when truly needed.
 >> broad try except blocks are for pussies who dont trust their own code and try to hide f*ckup
- __Minimal API surface for developers__
  - Observation: Smaller, explicit API reduces mistakes.
  - Ideal behaviour: Expose only a few, discoverable methods in the view:
    - `set_section_info(section_id: str, text: str, level: str = "info")`
    - `set_source_slot_message(text: str)`
    - `set_archive_mode(mode: Literal["SameAsSource", "SelectFolder"])`
    - `show_source_context_options(monitor_enabled: bool, auto_queue_enabled: bool, folder: Optional[str])`
    Internals use `SlotRow.set_visible()` consistently.
 The api surface of what exactly ? the slots? or the guidepane? The guidepane should deal with the nits and bolts of how things are displayed, this should not be exposed to calling code if youre talking about the internal api for the parent api .. then yes it should be explicit and concise with sensible defaults - configurable if needed 
 
Status: Draft for review. No code changes implied until approved.
