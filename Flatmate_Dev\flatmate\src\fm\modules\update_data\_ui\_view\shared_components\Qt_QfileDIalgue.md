# QFileDialog: Useful Properties & Flags (PyQt/PySide)

A reference for building flexible, reusable file/folder picker components using `QFileDialog` in PyQt/PySide.

---

## Key Properties & Flags


**setFileMode()**

- `QFileDialog.AnyFile`: Any file (even non-existent, for "Save As")
- `QFileDialog.ExistingFile`: Only existing files
- `QFileDialog.ExistingFiles`: Multiple existing files
- `QFileDialog.Directory`: Directories only


**setAcceptMode()**

- `QFileDialog.AcceptOpen`: For opening files
- `QFileDialog.AcceptSave`: For saving files



**setNameFilter() / setNameFilters()**

- Restrict selectable files by extension, e.g. `"CSV Files (*.csv);;All Files (*)"`



**setDirectory()**

- Set the initial directory shown in the dialog.



**setOptions()**

- `QFileDialog.ShowDirsOnly`: Only show directories (with Directory mode)
- `QFileDialog.DontResolveSymlinks`: Show symlinks as-is
- `QFileDialog.DontConfirmOverwrite`: Skip overwrite confirmation
- `QFileDialog.DontUseNativeDialog`: Use Qt's dialog instead of OS-native



**setFilter()**

- Filter for hidden files, directories, etc.



**setDefaultSuffix()**

- Automatically add a file extension if the user omits it (useful for save dialogs).



**selectedFiles()**

- Returns the selected file(s) or directory.



**setWindowTitle()**

- Set the dialog title.


---

## Usage Patterns

- **Select a folder:**
  ```python
  QFileDialog.getExistingDirectory(parent, "Select Folder", start_dir, options)
  ```
- **Select a file:**
  ```python
  QFileDialog.getOpenFileName(parent, "Select File", start_dir, "CSV Files (*.csv)")
  ```
- **Multi-file selection:**
  ```python
  dialog.setFileMode(QFileDialog.ExistingFiles)
  ```

---

## UX/Integration Tips

- Always set a meaningful window title and sensible default directory.
- Use filters to restrict file types where relevant.
- Use `ShowDirsOnly` for pure directory picking.
- Consider `DontUseNativeDialog` if you need advanced customisation.

---

See also: [Qt QFileDialog documentation](https://doc.qt.io/qt-6/qfiledialog.html)
