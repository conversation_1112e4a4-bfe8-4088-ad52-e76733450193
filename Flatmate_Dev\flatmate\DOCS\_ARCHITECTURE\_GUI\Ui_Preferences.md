# UI Preferences (QSettings)

Purpose
- Define a simple, per-user, UI-only preferences mechanism using Qt's QSettings.
- Keep UI state out of the domain configuration system to avoid overhead and licensing implications.
- Apply only to Widgets/QSS layer (no Qt Model/View usage in domain or presenter code).

Storage Location
- File: ~/.flatmate/config/ui_prefs.ini
- Resolved by: AppPaths.CONFIG_DIR / "ui_prefs.ini"
- Format: INI (QSettings.IniFormat)
- Scope: Per user, local machine

Key Strategy
- Use fully-qualified keys to keep code straightforward and namespaced by module/widget.
- Qt also supports beginGroup()/endGroup() if preferred for readability; functionally equivalent.

Recommended key patterns:
- Splitters: ui/splitters/<module>/<splitter_id>/sizes
- Columns: ui/columns/<module>/<view_id>/<column_key>
- Panels: ui/panels/<module>/<panel_id>/visible
- Windows: ui/window/<window_id>/geometry

Example usage (current implementation)
- Update Data center panel vertical splitter
  - Restore on init:
    - Key: update_data/center_splitter
    - Value type: [int, int] (list of two sizes)
  - Save on close:
    - Key: update_data/center_splitter
    - Value type: [int, int]

Code integration notes
- Instantiate QSettings with explicit path and format:
  - self._ui_prefs = QSettings(str(AppPaths.CONFIG_DIR / "ui_prefs.ini"), QSettings.IniFormat)
- Always guard QSettings usage in try/except to avoid UI crashes if disk or permissions fail.
- Validate types when restoring (e.g., ensure list[int] and correct length) and fall back to sane defaults.

Minimal example

```python
from PySide6.QtCore import QSettings
from fm.core.config.paths import AppPaths

# Init (once per widget/manager)
settings = QSettings(str(AppPaths.CONFIG_DIR / "ui_prefs.ini"), QSettings.IniFormat)

# Restore splitter sizes (with fallback)
sizes = settings.value("ui/splitters/update_data/center_splitter/sizes", None)
if isinstance(sizes, list) and all(isinstance(x, int) for x in sizes) and len(sizes) == 2:
    splitter.setSizes(sizes)
else:
    splitter.setSizes([180, 820])

# Save on close
sizes = splitter.sizes()
if isinstance(sizes, list) and all(isinstance(x, int) for x in sizes):
    settings.setValue("ui/splitters/update_data/center_splitter/sizes", sizes)
```

When to use UI preferences
- Visual/stateful UI elements with purely local impact:
  - Splitter sizes
  - Column widths/order/visibility
  - Panel visibility toggles
  - Window geometry and last-opened tab within a module
- Avoid storing anything that belongs to domain/business logic or cross-user configuration.

Do and Don’t
- Do: Keep UI-only details here; prefer simple, local persistence that requires no schema changes.
- Do: Use fully qualified keys for clarity and collision avoidance.
- Don’t: Store secrets or domain configurations; those belong in the primary config system.
- Don’t: Introduce Qt Model/View usage in domain or presenter layers.

Auditing and troubleshooting
- If preferences appear not to load, check path:
  - Windows: C:\Users\<USER>\.flatmate\config\ui_prefs.ini
  - macOS/Linux: ~/.flatmate/config/ui_prefs.ini
- Confirm the key exists and value types are as expected (lists serialize fine in QSettings INI).
- Ensure AppPaths.CONFIG_DIR exists (AppPaths.ensure_directories() is called at app startup).

Related code (locations)
- Center panel splitter persistence:
  - fm/modules/update_data/_ui/_view/center_panel_layout.py
- Styles public API and application:
  - fm/gui/styles/__init__.py
  - fm/main.py (uses public API with guarded fallback)