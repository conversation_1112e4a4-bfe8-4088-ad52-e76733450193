"""
Style management for the application.
Provides functions to load and apply styles, with hooks for future theme/font size support.
"""

from pathlib import Path
from typing import Optional
from PySide6.QtWidgets import QApplication
from fm.core.config import config
from fm.core.config.keys import ConfigKeys

from fm.core.services.logger import log

# Audit control flag - set to False to disable debugging
AUDIT_STYLESHEET = True

# Theme experiment flag: when set, attempt to load a YAML theme and apply color-only hex mapping
ENABLE_THEME_EXPERIMENT = True  # set True to try themes/theme-<name>.yaml mapping
DEFAULT_THEME_NAME = "light"     # scaffold provided
THEMES_DIR_NAME = "themes"

# Theme experiment flag: when set, attempt to load a YAML theme and apply color-only hex mapping
ENABLE_THEME_EXPERIMENT = False  # set True to try themes/themes-{name}.yaml mapping
DEFAULT_THEME_NAME = "light"     # 'light' scaffold provided; keep 'dark' to skip changes
THEMES_DIR_NAME = "themes"

# Consolidated stylesheet switch - set to True to use single consolidated file
USE_CONSOLIDATED_STYLESHEET = True  # base_theme.qss preferred; falls back to flatmate_consolidated.qss

def _audit_loader_output(combined_stylesheet: str, styles_dir: Path) -> None:
    """Audit what the loader produces before sending to Qt."""
    if not AUDIT_STYLESHEET:
        return

    # Save the combined output
    debug_file = styles_dir / "debug_combined_output.qss"
    with open(debug_file, 'w', encoding='utf-8') as f:
        f.write(combined_stylesheet)

    # Print summary
    print(f"\n=== LOADER AUDIT ===")
    print(f"Combined stylesheet saved to: {debug_file}")
    print(f"Total length: {len(combined_stylesheet)} characters")
    print(f"Total lines: {len(combined_stylesheet.splitlines())}")

    # Check for CSS variables
    var_count = combined_stylesheet.count('var(--')
    print(f"CSS variables found: {var_count}")

    # Check for font size placeholders
    placeholder_count = combined_stylesheet.count('{{FONT_SIZE}}')
    print(f"Font size placeholders found: {placeholder_count}")

    # Check for common selectors
    selectors_to_check = [
        '#left_panel',
        '#right_panel',
        'QPushButton[type="action_btn"]',
        'QWidget'
    ]

    print(f"\nSelector occurrences:")
    for selector in selectors_to_check:
        count = combined_stylesheet.count(selector)
        print(f"  {selector}: {count} times")

def _audit_css_variables(stylesheet: str) -> None:
    """Check if CSS variables are being resolved."""
    if not AUDIT_STYLESHEET:
        return

    print(f"\n=== CSS VARIABLE AUDIT ===")

    # Find all var() calls
    import re
    var_pattern = r'var\(--[\w-]+\)'
    var_matches = re.findall(var_pattern, stylesheet)

    if var_matches:
        print(f"Found {len(var_matches)} CSS variable calls:")
        for var_call in set(var_matches):  # Remove duplicates
            count = stylesheet.count(var_call)
            print(f"  {var_call}: {count} times")
    else:
        print("No CSS variable calls found")

    # Find all CSS variable definitions
    def_pattern = r'--[\w-]+:\s*[^;]+;'
    def_matches = re.findall(def_pattern, stylesheet)

    if def_matches:
        print(f"\nFound {len(def_matches)} CSS variable definitions:")
        for var_def in def_matches:
            print(f"  {var_def}")
    else:
        print("No CSS variable definitions found")


def _load_theme_mapping(styles_dir: Path, theme_name: str) -> Optional[dict]:
    """
    Load a YAML theme file and return its hex_map dict (exact literal replacements).
    Returns None if file missing or invalid.
    """
    try:
        import yaml  # type: ignore
    except Exception:
        # PyYAML may not be present; fail gracefully
        try:
            log.info("Styles: PyYAML not available; skipping theme mapping")
        except Exception:
            print("[styles] PyYAML not available; skipping theme mapping")
        return None

    theme_path = styles_dir / THEMES_DIR_NAME / f"theme-{theme_name}.yaml"
    if not theme_path.exists():
        try:
            log.info(f"Styles: theme file not found: {theme_path}")
        except Exception:
            print(f"[styles] theme file not found: {theme_path}")
        return None

    try:
        with open(theme_path, "r", encoding="utf-8") as f:
            data = yaml.safe_load(f)
        hex_map = (data or {}).get("hex_map") or {}
        if not isinstance(hex_map, dict) or not hex_map:
            try:
                log.info(f"Styles: theme '{theme_name}' has no hex_map or is not a dict; skipping")
            except Exception:
                print(f"[styles] theme '{theme_name}' has no hex_map or is not a dict; skipping")
            return None
        # Normalize keys (strip leading '#') and values (ensure they start with '#')
        normalized = {}
        for k, v in hex_map.items():
            key = str(k).lstrip("#")
            val = str(v)
            if not val.startswith("#"):
                val = f"#{val}"
            normalized[key] = val
        return normalized
    except Exception as e:
        try:
            log.info(f"Styles: failed loading theme '{theme_name}': {e}")
        except Exception:
            print(f"[styles] failed loading theme '{theme_name}': {e}")
        return None


def _apply_hex_map(stylesheet: str, hex_map: dict) -> tuple[str, dict]:
    """
    Apply exact, case-sensitive hex replacements based on hex_map where keys are hex without '#'.
    Returns (new_stylesheet, counts_dict).
    """
    counts = {}
    out = stylesheet
    for key, new_val in hex_map.items():
        literal = f"#{key}"
        count_before = out.count(literal)
        if count_before:
            out = out.replace(literal, new_val)
        counts[literal] = count_before
    return out, counts

def _load_theme_mapping(styles_dir: Path, theme_name: str) -> Optional[dict]:
    """
    Load a YAML theme file and return its hex_map dict (exact literal replacements).
    Returns None if file missing or invalid.
    """
    import json
    try:
        import yaml  # type: ignore
    except Exception:
        # PyYAML may not be present; fail gracefully
        log.info("Styles: PyYAML not available; skipping theme mapping")
        return None

    theme_path = styles_dir / THEMES_DIR_NAME / f"theme-{theme_name}.yaml"
    if not theme_path.exists():
        log.info(f"Styles: theme file not found: {theme_path}")
        return None

    try:
        with open(theme_path, "r", encoding="utf-8") as f:
            data = yaml.safe_load(f)
        hex_map = (data or {}).get("hex_map") or {}
        if not isinstance(hex_map, dict) or not hex_map:
            log.info(f"Styles: theme {theme_name} has no hex_map or is not a dict; skipping")
            return None
        # Normalize keys (strip leading '#') and values (ensure they start with '#')
        normalized = {}
        for k, v in hex_map.items():
            key = str(k).lstrip("#")
            val = str(v)
            if not val.startswith("#"):
                val = f"#{val}"
            normalized[key] = val
        return normalized
    except Exception as e:
        log.info(f"Styles: failed loading theme {theme_name}: {e}")
        return None

def _apply_hex_map(stylesheet: str, hex_map: dict) -> tuple[str, dict]:
    """
    Apply exact, case-sensitive hex replacements based on hex_map where keys are hex without '#'.
    Returns (new_stylesheet, counts_dict).
    """
    counts = {}
    out = stylesheet
    for key, new_val in hex_map.items():
        literal = f"#{key}"
        count_before = out.count(literal)
        if count_before:
            out = out.replace(literal, new_val)
        counts[literal] = count_before
    return out, counts

def load_consolidated_styles() -> str:
    """
    Load consolidated stylesheet with hardcoded colors.

    Returns:
        str: Consolidated stylesheet content

    Raises:
        FileNotFoundError: If consolidated stylesheet file is not found
    """
    styles_dir = Path(__file__).parent

    # Load consolidated stylesheet
    # Prefer base_theme.qss, fall back to flatmate_consolidated.qss for backward compatibility
    primary_file = styles_dir / "base_theme.qss"
    legacy_file = styles_dir / "flatmate_consolidated.qss"
    consolidated_file = primary_file if primary_file.exists() else legacy_file
    if not consolidated_file.exists():
        raise FileNotFoundError(f"Consolidated stylesheet not found (checked): {primary_file} and {legacy_file}")

    with open(consolidated_file, 'r', encoding='utf-8') as f:
        stylesheet = f.read()

    # Apply dynamic font size
    font_size = config.get_value(ConfigKeys.App.BASE_FONT_SIZE, 14)
    before = stylesheet.count('{{FONT_SIZE}}')
    stylesheet = stylesheet.replace('{{FONT_SIZE}}', str(font_size))
    after = stylesheet.count('{{FONT_SIZE}}')
    
    # Informational logging
    try:
        log.info(f"Styles: using consolidated stylesheet: {consolidated_file.name}  (path={consolidated_file})")
        log.info(f"Styles: BASE_FONT_SIZE={font_size}  FONT_SIZE replacements={before - after}")
    except Exception:
        # Fallback if logger not fully initialized yet
        print(f"[styles] using consolidated stylesheet: {consolidated_file.name} (path={consolidated_file})")
        print(f"[styles] BASE_FONT_SIZE={font_size}  FONT_SIZE replacements={before - after}")

    if AUDIT_STYLESHEET:
        print(f"\n=== CONSOLIDATED STYLESHEET LOADED ===")
        print(f"File: {consolidated_file}")
        print(f"Length: {len(stylesheet)} characters")
        print(f"Lines: {len(stylesheet.splitlines())}")
        print(f"FONT_SIZE replacements: {before - after}")

    return stylesheet

def load_styles() -> str:
    """
    Load and combine application styles.
    Uses consolidated stylesheet if USE_CONSOLIDATED_STYLESHEET is True,
    otherwise loads theme.qss and style.qss separately.

    Returns:
        str: Combined stylesheet content

    Raises:
        FileNotFoundError: If style files are not found
    """
    if USE_CONSOLIDATED_STYLESHEET:
        return load_consolidated_styles()

    styles_dir = Path(__file__).parent

    # Load base theme and styles
    with open(styles_dir / "theme.qss", 'r') as f:
        theme = f.read()
    with open(styles_dir / "style.qss", 'r') as f:
        style = f.read()

    # Apply any dynamic values
    font_size = config.get_value(ConfigKeys.App.BASE_FONT_SIZE, 14)
    combined = theme + "\n" + style
    combined = combined.replace('{{FONT_SIZE}}', str(font_size))

    # AUDIT: Capture loader output
    _audit_loader_output(combined, styles_dir)
    _audit_css_variables(combined)

    return combined

def _audit_qt_applied(app: QApplication, styles_dir: Path) -> None:
    """Audit what Qt actually applied after processing."""
    if not AUDIT_STYLESHEET:
        return

    # Get what Qt actually has
    actual_applied = app.styleSheet()

    # Save Qt's version
    debug_file = styles_dir / "debug_qt_applied.qss"
    with open(debug_file, 'w', encoding='utf-8') as f:
        f.write(actual_applied)

    print(f"\n=== QT APPLICATION AUDIT ===")
    print(f"Qt applied stylesheet saved to: {debug_file}")
    print(f"Applied length: {len(actual_applied)} characters")
    print(f"Applied lines: {len(actual_applied.splitlines())}")

def _audit_compare_stylesheets(loader_output: str, qt_applied: str, styles_dir: Path) -> None:
    """Compare what we sent vs what Qt applied."""
    if not AUDIT_STYLESHEET:
        return

    print(f"\n=== COMPARISON AUDIT ===")

    if loader_output == qt_applied:
        print("✓ Qt applied stylesheet UNCHANGED from loader output")
        print("⚠ CSS variables still present - Qt must be handling them internally")
    else:
        print("⚠ Qt MODIFIED the stylesheet during application")

        # Save the differences
        diff_file = styles_dir / "debug_differences.txt"
        with open(diff_file, 'w', encoding='utf-8') as f:
            f.write("=== LOADER OUTPUT ===\n")
            f.write(f"Length: {len(loader_output)} chars\n")
            f.write(f"Lines: {len(loader_output.splitlines())}\n\n")

            f.write("=== QT APPLIED ===\n")
            f.write(f"Length: {len(qt_applied)} chars\n")
            f.write(f"Lines: {len(qt_applied.splitlines())}\n\n")

            f.write("=== FIRST 1000 CHARS OF EACH ===\n")
            f.write("LOADER:\n")
            f.write(loader_output[:1000])
            f.write("\n\nQT APPLIED:\n")
            f.write(qt_applied[:1000])

        print(f"Differences saved to: {diff_file}")

def _audit_actual_widget_colors(app: QApplication, styles_dir: Path) -> None:
    """Audit what colors are actually applied to widgets."""
    if not AUDIT_STYLESHEET:
        return

    print(f"\n=== ACTUAL WIDGET COLORS AUDIT ===")

    # Find widgets by object name
    widgets_to_check = [
        'left_panel',
        'right_panel',
        'right_side_bar',
        'file_tree'
    ]

    color_report = []
    color_report.append("=== ACTUAL WIDGET COLORS ===\n")

    for widget_name in widgets_to_check:
        widgets = app.findChildren(object, widget_name)
        if widgets:
            widget = widgets[0]  # Take first match

            # Get actual colors
            palette = widget.palette()
            bg_color = palette.color(palette.Window)
            text_color = palette.color(palette.WindowText)

            print(f"{widget_name}:")
            print(f"  Background: {bg_color.name()}")
            print(f"  Text: {text_color.name()}")

            color_report.append(f"{widget_name}:\n")
            color_report.append(f"  Background: {bg_color.name()}\n")
            color_report.append(f"  Text: {text_color.name()}\n")
            color_report.append(f"  Widget type: {type(widget).__name__}\n\n")
        else:
            print(f"{widget_name}: NOT FOUND")
            color_report.append(f"{widget_name}: NOT FOUND\n")

    # Save color report
    color_file = styles_dir / "debug_actual_colors.txt"
    with open(color_file, 'w', encoding='utf-8') as f:
        f.writelines(color_report)

    print(f"Actual colors saved to: {color_file}")

def apply_styles(app: QApplication) -> None:
    """
    Apply styles to the application.

    Args:
        app: The QApplication instance

    Raises:
        FileNotFoundError: If style files are not found
    """
    styles_dir = Path(__file__).parent
    stylesheet = load_styles()
    app.setStyleSheet(stylesheet)

    # Log which stylesheet path is in use (after application)
    try:
        # Mirror selection logic to determine the path used
        primary_file = styles_dir / "base_theme.qss"
        legacy_file = styles_dir / "flatmate_consolidated.qss"
        consolidated_file = primary_file if primary_file.exists() else legacy_file
        log.info(f"Styles: applied stylesheet from: {consolidated_file}")
    except Exception:
        print(f"[styles] applied stylesheet from: {consolidated_file if 'consolidated_file' in locals() else 'unknown'}")

    # AUDIT: Capture what Qt applied
    _audit_qt_applied(app, styles_dir)

    # AUDIT: Compare loader vs Qt applied
    qt_applied = app.styleSheet()
    _audit_compare_stylesheets(stylesheet, qt_applied, styles_dir)

    # AUDIT: Check actual widget colors (delayed to allow widgets to be created)
    if AUDIT_STYLESHEET:
        # Use QTimer to delay widget inspection until after UI is built
        from PySide6.QtCore import QTimer
        QTimer.singleShot(1000, lambda: _audit_actual_widget_colors(app, styles_dir))

# Future hooks for dynamic updates
def update_font_size(app: QApplication, size: int) -> None:
    """
    Update application font size.
    Currently a placeholder for future implementation.
    
    Args:
        app: The QApplication instance
        size: New base font size
    """
    # TODO: Implement when needed
    pass

def switch_theme(app: QApplication, theme: str) -> None:
    """
    Switch application theme.
    Currently a placeholder for future implementation.
    
    Args:
        app: The QApplication instance
        theme: Theme name (e.g., 'light', 'dark')
    """
    # TODO: Implement when needed
    pass
