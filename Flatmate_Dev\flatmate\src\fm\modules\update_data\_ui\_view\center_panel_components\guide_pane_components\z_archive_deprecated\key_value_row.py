from __future__ import annotations

from typing import Optional

from PySide6.QtCore import Qt
from PySide6.QtWidgets import QWidget, QHBoxLayout, QLabel, QSizePolicy


class KeyValueRow(QWidget):
    """
    Simple horizontal row displaying a static key and a dynamic value.
    """

    def __init__(self, key_text: str, parent: Optional[QWidget] = None) -> None:
        super().__init__(parent)
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(6)

        # Key label: muted, right-aligned, fixed-ish width
        self.key_label = QLabel(key_text)
        self.key_label.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignTop)
        self.key_label.setStyleSheet("color: #a8a8a8; font-weight: 600;")
        self.key_label.setMinimumWidth(72)
        self.key_label.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Preferred)

        # Value label: prominent, wraps, selectable
        self.value_label = QLabel("")
        self.value_label.setWordWrap(True)
        self.value_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        self.value_label.setStyleSheet("color: #dddddd;")
        self.value_label.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)

        layout.addWidget(self.key_label)
        layout.addWidget(self.value_label, 1)

        # When no key is provided, hide the key label and remove extra spacing/gutter
        # so the value aligns naturally without a heading or left offset.
        if not (key_text or "").strip():
            self.key_label.hide()
            self.key_label.setMinimumWidth(0)
            # Keep size policy consistent while ensuring no reserved width
            self.key_label.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Preferred)
            layout.setSpacing(0)

    def set_value(self, text: str) -> None:
        self.value_label.setText(text or "")

    def set_value_html(self, html: str) -> None:
        """Set the value using rich-text HTML (e.g., formatted paths)."""
        self.value_label.setText(html or "")
