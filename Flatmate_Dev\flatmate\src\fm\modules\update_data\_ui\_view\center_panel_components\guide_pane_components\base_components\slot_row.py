from __future__ import annotations

from typing import Optional

from PySide6.QtWidgets import Q<PERSON>rame, QVBoxLayout, QWidget, QSizePolicy


class SlotRow(QFrame):
    """
    Borderless, uniform container for a single guide pane slot.

    - Zero vertical padding at all times.
    - Configurable horizontal padding (left/right) applied when expanded.
    - Sizes to contents vertically (Preferred, Minimum).
    - Provides set_content(widget), set_collapsed(flag), and set_horizontal_padding(pixels)
      for predictable behaviour.
    """

    def __init__(self, parent: Optional[QWidget] = None, *, horizontal_padding: int = 0) -> None:
        super().__init__(parent)
        self.setFrameShape(QFrame.Shape.NoFrame)
        self.setObjectName("slot_row")

        self._layout = QVBoxLayout(self)
        # Zero vertical padding. Horizontal padding is configurable and applied when expanded.
        self._layout.setContentsMargins(0, 0, 0, 0)
        # Leave inter-row spacing to the parent Section body layout
        self._layout.setSpacing(0)
        self.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Minimum)

        self._hpad: int = horizontal_padding
        self._content: Optional[QWidget] = None

    def set_content(self, widget: Optional[QWidget]) -> None:
        """Replace current content with ``widget``; pass ``None`` to clear the slot.

        Ensures the previous widget is fully detached from the layout and parent
        before inserting the new one. Child widgets have zero margins to avoid
        stray padding.
        """
        if self._content is widget:
            return
        if self._content is not None:
            self._layout.removeWidget(self._content)
            self._content.setParent(None)
        self._content = widget
        if widget is not None:
            # Ensure child widgets don't add stray margins
            widget.setContentsMargins(0, 0, 0, 0)
            self._layout.addWidget(widget)

    def set_collapsed(self, collapsed: bool) -> None:
        """Collapse row to zero height when collapsed=True, otherwise restore."""
        if collapsed:
            # Hide and force zero height so parent layouts leave no residual gap
            self.hide()
            # Zero vertical and horizontal padding when collapsed
            self._layout.setContentsMargins(0, 0, 0, 0)
            self.setFixedHeight(0)
            self.setMaximumHeight(0)
            self.setMinimumHeight(0)
            sp = self.sizePolicy()
            sp.setVerticalPolicy(QSizePolicy.Policy.Fixed)
            self.setSizePolicy(sp)
        else:
            # Restore natural sizing
            self.show()
            # Apply horizontal padding only; keep zero vertical padding
            self._layout.setContentsMargins(self._hpad, 0, self._hpad, 0)
            self.setMinimumHeight(0)
            self.setMaximumHeight(16777215)
            sp = self.sizePolicy()
            sp.setVerticalPolicy(QSizePolicy.Policy.Minimum)
            self.setSizePolicy(sp)

    def set_horizontal_padding(self, pixels: int) -> None:
        """Set left/right padding in pixels (vertical padding remains zero)."""
        self._hpad = max(0, int(pixels))
        # Apply immediately; if currently collapsed, margins won't affect height
        self._layout.setContentsMargins(self._hpad, 0, self._hpad, 0)

    # Convenience: single API to control visibility with zero layout footprint when hidden
    def set_visible(self, visible: bool) -> None:
        """Show or hide the row. When hidden, takes zero space in the layout."""
        self.set_collapsed(not bool(visible))
