# File Selector Implementation Plan

**Date**: August 3, 2025  
**Status**: Implementation Plan  
**Component**: File Selection Widget

## Step-by-Step Implementation Plan

### Phase 1: Rename and Refactor the Shared Widget

1. **Create the new file**
   - Create `file_selector.py` in the shared_components directory
   - Copy the optimized content from `get_files.py`

2. **Refactor class and method names**
   - Rename classes for clarity:
     - `FileSelector` as the main public interface (replacing the global function)
     - `FolderDialogue` (from SelectFolderDialogue)
     - `FileDialogue` (from SelectFileDialogue)
     - `FileUtils` remains the same
   - Update attribute names:
     - `selected_files` and `selected_folder` (already done in optimization)

3. **Update the public API**
   - Convert the global `get_file_paths()` function to a static method in `FileSelector`
   - Ensure backward compatibility with existing code

### Phase 2: Update FileManager Implementation

1. **Update imports**
   - Replace `from ..._view.shared_components.get_files import get_file_paths`
   - With `from ..._view.shared_components.file_selector import FileSelector`

2. **Update _select_files method**
   ```python
   def _select_files(self):
       """Select individual files using file dialog."""
       try:
           file_paths = FileSelector.get_file_paths(
               method='select_files',
               title="Select Files to Process",
               parent=self.view.get_main_window(),
               start_dir=None  # Will use last used dir from config
           )
           
           if file_paths:
               log.debug(f"Selected {len(file_paths)} files")
               
               # Store selected source for "same as source" functionality
               self.selected_source = file_paths
               self.state.source_type = 'files'
               
               # Enrich file info and update file list
               enriched_files = self.enrich_file_info(file_paths)
               self.file_list_manager.set_files(enriched_files)
               
               # Update state
               self.state.update_can_process()
               
               # Emit event for file discovery
               self.local_bus.emit(SourceDiscoveredEvent(
                   source_type='files',
                   file_count=len(file_paths)
               ))
       except Exception as e:
           log.error(f"Error selecting files: {e}")
           self.info_bar_service.show_error(f"Error selecting files: {str(e)}")
   ```

3. **Update _select_folder method**
   ```python
   def _select_folder(self):
       """Select a folder using folder dialog."""
       try:
           file_paths = FileSelector.get_file_paths(
               method='select_folder',
               title="Select Folder Containing Files",
               parent=self.view.get_main_window(),
               start_dir=None  # Will use last used dir from config
           )
           
           if file_paths:
               folder_path = os.path.dirname(file_paths[0]) if file_paths else ""
               
               if folder_path:
                   log.debug(f"Selected folder: {folder_path} with {len(file_paths)} files")
                   
                   # Store selected source for "same as source" functionality
                   self.selected_source = folder_path
                   self.state.source_type = 'folder'
                   
                   # Enrich file info and update file list
                   enriched_files = self.enrich_file_info(file_paths)
                   self.file_list_manager.set_files(enriched_files)
                   
                   # Update state
                   self.state.update_can_process()
                   
                   # Emit event for file discovery
                   self.local_bus.emit(SourceDiscoveredEvent(
                       source_type='folder',
                       file_count=len(file_paths),
                       folder_path=folder_path
                   ))
       except Exception as e:
           log.error(f"Error selecting folder: {e}")
           self.info_bar_service.show_error(f"Error selecting folder: {str(e)}")
   ```

### Phase 3: Update UDFileView Implementation

1. **Update imports**
   - Replace any direct QFileDialog imports
   - Add `from ...shared_components.file_selector import FileSelector`

2. **Update _on_add_files_requested method**
   ```python
   def _on_add_files_requested(self) -> None:
       """Handle add files request."""
       try:
           file_paths = FileSelector.get_file_paths(
               method='select_files',
               title="Select Files to Add",
               parent=self,
               start_dir=None  # Will use last used dir from config
           )
           
           if file_paths:
               log.debug(f"Adding {len(file_paths)} files to file view")
               for file_path in file_paths:
                   self.add_file(file_path)
       except Exception as e:
           log.error(f"Error adding files: {e}")
   ```

### Phase 4: Testing and Validation

1. **Test FileManager integration**
   - Test file selection functionality
   - Test folder selection functionality
   - Verify correct behavior on Windows vs other platforms

2. **Test UDFileView integration**
   - Test add files functionality
   - Verify consistent behavior with FileManager

3. **Edge case testing**
   - Test with empty selections
   - Test with large file lists
   - Test with various file types

### Phase 5: Cleanup and Documentation

1. **Remove old file**
   - Once all references are updated, remove `get_files.py`

2. **Update documentation**
   - Add docstring to `file_selector.py` explaining its purpose
   - Update any existing documentation referencing the file selection functionality
