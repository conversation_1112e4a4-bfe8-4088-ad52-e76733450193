# Simple Static QSS Solution

**Date**: 2025-01-30  
**Purpose**: Pragmatic fix for current QSS issues  
**Complexity**: Low - Simple file consolidation  
**Timeline**: 1-2 hours implementation

## Problem Statement

Current QSS system has:
- 3 separate files with unclear precedence
- CSS variables that don't work properly (`var(--color-primary)`)
- Difficulty adding new widget styles
- Complex loading order

## Simple Solution: Static Consolidation

### Approach
1. Replace all CSS variables with actual hex values
2. Combine all 3 QSS files into 1 consolidated file
3. Remove @import statements
4. Keep existing selectors and styling rules
5. Optional: Create separate theme files for light/dark

### Implementation

#### 1. Create Consolidated Stylesheet

```css
/* flatmate/src/fm/gui/styles/flatmate_dark.qss */

/* Global styles */
* {
    font-family: ".AppleSystemUIFont", "Helvetica Neue", Arial, sans-serif;
    font-size: 14px;
}

QWidget {
    background-color: #1E1E1E;
    color: #FFFFFF;
}

/* Main panels */
#left_panel, #right_panel {
    background-color: #1a382f;  /* Dark green nav background */
    min-width: 150px;
}

#right_side_bar {
    background-color: #1a382f;  /* Dark green nav background */
}

#left_panel QLabel {
    background: transparent;
}

#content_area {
    background-color: #1E1E1E;
}

/* Center Panel */
#center_panel_content {
    background-color: #1E1E1E;
    padding: 0;
    border: none;
}

/* Primary Action Buttons */
QPushButton[type="primary"] {
    background-color: #3B8A45;
    color: #FFFFFF;
    border: none;
    border-radius: 6px;
    padding: 6px;
    height: 35px;
    font-weight: bold;
}

QPushButton[type="primary"]:hover {
    background-color: #4BA357;
}

QPushButton[type="primary"]:pressed {
    background-color: #2E6E37;
}

/* Secondary Buttons */
QPushButton[type="secondary"] {
    background-color: #3B7443;
    color: #FFFFFF;
    border: none;
    border-radius: 6px;
    padding: 6px;
    height: 35px;
}

QPushButton[type="secondary"]:hover {
    background-color: #488E52;
}

QPushButton[type="secondary"]:pressed {
    background-color: #2E5A35;
}

/* File Tree */
#file_tree {
    border: 1px solid #333333;
    border-radius: 4px;
    background-color: #202020;
    color: #FFFFFF;
}

#file_tree::item {
    padding: 4px;
    border-bottom: 1px solid #333333;
    background: transparent;
}

#file_tree::item:selected {
    background-color: #3B7443;  /* Green selection */
    color: #FFFFFF;
}

#file_tree::item:alternate {
    background-color: #242424;
}

#file_tree::item:hover {
    background-color: #242424;
}

#file_tree QHeaderView::section {
    background-color: #242424;
    color: #FFFFFF;
    padding: 4px;
    border: none;
    border-right: 1px solid #333333;
    border-bottom: 1px solid #333333;
}

#file_tree QHeaderView {
    background-color: #242424;
}

/* Table View Toolbar */
QFrame#TableViewToolbar {
    border: 1px solid #2A5A3A;
    border-radius: 4px;
}

/* Text Display */
QTextEdit {
    background-color: #1E1E1E;
    color: #D4D4D4;
    border: none;
}

QTextEdit::selection {
    background-color: #264F78;
}

/* Input Fields */
QLineEdit {
    background-color: #202020;
    border: 1px solid #333333;
    border-radius: 3px;
    padding: 4px;
    color: #FFFFFF;
}

QLineEdit:focus {
    border: 1px solid #3B8A45;
}

/* Combo Boxes */
QComboBox {
    background-color: #202020;
    border: 1px solid #333333;
    border-radius: 3px;
    padding: 4px;
    color: #FFFFFF;
    min-width: 100px;
}

QComboBox:hover {
    border: 1px solid #3B8A45;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
    background-color: transparent;
}

QComboBox::down-arrow {
    image: none;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid #FFFFFF;
    margin-right: 4px;
}

/* Scrollbars */
QScrollBar:vertical {
    background-color: #1C2A3A;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #2C3A4A;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #3B8A45;
}
```

#### 2. Update Style Loader

```python
# flatmate/src/fm/gui/styles/__init__.py
"""
Style management for the application.
Simplified static QSS approach.
"""

from pathlib import Path
from typing import Optional
from PySide6.QtWidgets import QApplication
from ...core.config import config
from ...core.config.keys import ConfigKeys

def load_styles(theme: str = 'dark') -> str:
    """Load static stylesheet for specified theme.
    
    Args:
        theme: Theme name ('dark' or 'light')
        
    Returns:
        str: Stylesheet content
        
    Raises:
        FileNotFoundError: If stylesheet file not found
    """
    styles_dir = Path(__file__).parent
    stylesheet_file = styles_dir / f"flatmate_{theme}.qss"
    
    if not stylesheet_file.exists():
        # Fallback to dark theme
        stylesheet_file = styles_dir / "flatmate_dark.qss"
    
    with open(stylesheet_file, 'r') as f:
        content = f.read()
    
    # Replace font size placeholder if needed
    font_size = config.get_value(ConfigKeys.App.BASE_FONT_SIZE, 14)
    content = content.replace('{{FONT_SIZE}}', str(font_size))
    
    return content

def apply_styles(app: QApplication, theme: str = 'dark') -> None:
    """Apply static stylesheet to application.
    
    Args:
        app: The QApplication instance
        theme: Theme name ('dark' or 'light')
    """
    stylesheet = load_styles(theme)
    app.setStyleSheet(stylesheet)

def switch_theme(app: QApplication, theme: str) -> None:
    """Switch application theme.
    
    Note: For static stylesheets, this requires application restart
    to fully take effect.
    
    Args:
        app: The QApplication instance
        theme: Theme name ('dark' or 'light')
    """
    # Save theme preference
    config.set_value(ConfigKeys.App.THEME, theme)
    
    # Apply new stylesheet
    apply_styles(app, theme)
    
    # Note: Some widgets may need manual refresh
    for widget in app.allWidgets():
        widget.update()

def update_font_size(app: QApplication, size: int) -> None:
    """Update application font size.
    
    Args:
        app: The QApplication instance
        size: New base font size
    """
    config.set_value(ConfigKeys.App.BASE_FONT_SIZE, size)
    
    # Get current theme and reapply styles
    current_theme = config.get_value(ConfigKeys.App.THEME, 'dark')
    apply_styles(app, current_theme)
```

#### 3. Optional: Light Theme

```css
/* flatmate/src/fm/gui/styles/flatmate_light.qss */

/* Global styles */
* {
    font-family: ".AppleSystemUIFont", "Helvetica Neue", Arial, sans-serif;
    font-size: 14px;
}

QWidget {
    background-color: #FFFFFF;
    color: #000000;
}

/* Main panels */
#left_panel, #right_panel {
    background-color: #E8F5E8;  /* Light green nav background */
    min-width: 150px;
}

#right_side_bar {
    background-color: #E8F5E8;
}

/* Primary buttons */
QPushButton[type="primary"] {
    background-color: #2E7D32;
    color: #FFFFFF;
    border: none;
    border-radius: 6px;
    padding: 6px;
    height: 35px;
    font-weight: bold;
}

QPushButton[type="primary"]:hover {
    background-color: #388E3C;
}

/* File tree */
#file_tree {
    border: 1px solid #CCCCCC;
    border-radius: 4px;
    background-color: #FAFAFA;
    color: #000000;
}

#file_tree::item:selected {
    background-color: #4CAF50;
    color: #FFFFFF;
}

/* Continue with light theme colors... */
```

## Implementation Steps

### Step 1: Create Consolidated File
1. Copy current color values from `palette.qss`
2. Replace all `var(--color-name)` with actual hex values
3. Combine `theme.qss` and `style.qss` content
4. Save as `flatmate_dark.qss`

### Step 2: Update Loader
1. Modify `__init__.py` to use static file loading
2. Remove CSS variable replacement logic
3. Add theme switching support

### Step 3: Test
1. Start application - verify appearance matches current
2. Test all UI components
3. Verify no missing styles

### Step 4: Optional Light Theme
1. Create `flatmate_light.qss` with light colors
2. Test theme switching
3. Add theme selection to settings

## Benefits

### Immediate Fixes
- ✅ Eliminates CSS variable issues
- ✅ Clear single-file precedence
- ✅ Easy to add new widget styles
- ✅ Simple to understand and maintain

### Minimal Risk
- ✅ Same QSS system, just consolidated
- ✅ No architectural changes
- ✅ Easy to revert if needed
- ✅ 1-2 hour implementation

### Future Path
- ✅ Foundation for QProxyStyle migration later
- ✅ Easy theme file management
- ✅ Clear separation of concerns

## Migration Path

1. **Phase 1**: Implement static consolidation (this document)
2. **Phase 2**: Add more themes as static files
3. **Phase 3**: Consider QProxyStyle for advanced features (optional)

## File Structure After Implementation

```
flatmate/src/fm/gui/styles/
├── __init__.py              # Updated loader
├── flatmate_dark.qss        # Consolidated dark theme
├── flatmate_light.qss       # Optional light theme
└── backup/                  # Backup of old files
    ├── palette.qss
    ├── theme.qss
    └── style.qss
```

## What the Hell is QProxyStyle Anyway?

### The Reality Check

QProxyStyle is **NOT** a different framework. It's Qt's native styling system that QSS actually sits on top of. Here's what's really happening:

#### QSS vs QProxyStyle - The Truth
```
Your App
    ↓
QSS Parser (converts CSS to style calls)
    ↓
QStyleSheetStyle (internal QProxyStyle subclass)
    ↓
Native Qt Style System (QProxyStyle/QStyle)
    ↓
Widget Painting
```

**QSS is just a CSS-to-QProxyStyle converter!**

When you write:
```css
QPushButton { background-color: red; }
```

Qt internally creates a QProxyStyle subclass that does:
```python
def drawPrimitive(self, element, option, painter, widget):
    if element == PE_PanelButtonCommand:
        painter.fillRect(option.rect, QColor('red'))
```

### So What's the Difference?

| Aspect | QSS | QProxyStyle |
|--------|-----|-------------|
| **What it is** | CSS parser → QProxyStyle | Direct QProxyStyle |
| **Performance** | Parse CSS every time | No parsing overhead |
| **Flexibility** | Limited to CSS capabilities | Full painting control |
| **Live updates** | Reparse entire stylesheet | Direct method calls |
| **Debugging** | CSS debugging tools | Python debugging |

### The "Advanced" Features Aren't That Advanced

**Live theme switching:**
- QSS: `app.setStyleSheet(new_css)` (reparses everything)
- QProxyStyle: `style.update_colors(new_colors)` (direct update)

**Dynamic colors:**
- QSS: Rebuild entire CSS string with new values
- QProxyStyle: Change color dictionary, call `widget.update()`

### Why the Performance Difference?

Every time you create a widget with QSS:
1. Qt parses your CSS rules
2. Matches selectors against the widget
3. Converts CSS properties to painting instructions
4. Caches the result

With QProxyStyle:
1. Direct painting instructions (no parsing)

### The Bottom Line

QProxyStyle isn't magic - it's just **cutting out the CSS middleman**. You're writing the same painting code that QSS generates, but directly.

**Is it worth it?** Depends:
- **For simple styling**: Probably not
- **For complex themes**: Maybe
- **For performance-critical apps**: Yes
- **For dynamic styling**: Definitely

Your static QSS consolidation solves 90% of the problems with 10% of the effort.

---

**Recommendation**: Start with this simple static approach. It solves your immediate problems with minimal risk and provides a clean foundation for future enhancements.
