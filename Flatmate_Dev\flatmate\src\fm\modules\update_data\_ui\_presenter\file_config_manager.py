"""
File configuration management for Update Data module.

This module manages file/folder selection and configuration including:
- File and folder selection dialogs
- Save location selection and "same as source" logic
- Recent folders management (Quick Access)
- Source and save option changes

Renamed from file_management.py as part of Phase 3 refactoring.
"""

import os
from pathlib import Path
from typing import TYPE_CHECKING, List, Optional

from fm.core.services.logger import log
from fm.core.services.recent_folders_service import RecentFoldersService
from fm.core.directory.services.directory_alias_service import DirectoryAliasService
from fm.core.directory.services.directory_info_service import DirectoryInfoService
from fm.modules.update_data.models.config import SourceOptions, SaveOptions
from fm.modules.update_data._ui.ui_events import SourceDiscoveredEvent, FileDisplayUpdateEvent
from fm.modules.update_data.services.local_event_bus import ViewEvents
from ...config.ud_keys import UpdateDataKeys as ud_keys
from ...config.ud_config import ud_config
from .._view.shared_components.file_selector import FileSelector, FileUtils


if TYPE_CHECKING:
    from ..interface import IUpdateDataView
    from .state_coordinator import UpdateDataState, StateManager


class FileConfigManager:
    """
    Manages file/folder selection configuration and recent folders.

    This class handles:
    - File and folder selection dialogs
    - Save location selection and "same as source" functionality
    - Recent folders management (Quick Access)
    - Source and save option changes
    
    Note: File enrichment and canonical file list management is now handled
    by FileListManager as part of the Phase 1 refactoring.
    """

    def __init__(self, view: 'IUpdateDataView', state_manager: 'StateManager',
                 file_info_manager, local_bus, recent_folders_service: Optional[RecentFoldersService] = None):
        """
        Initialize the file configuration manager.

        Args:
            view: The view interface
            state_manager: Consolidated state and UI sync manager
            file_info_manager: FileInfoManager for canonical file list operations
            local_bus: Local event bus for module events
        """
        self.view = view
        self.state_manager = state_manager
        self.state = state_manager.state
        self.file_info_manager = file_info_manager
        self.local_bus = local_bus
        self.recent_folders: RecentFoldersService = recent_folders_service or RecentFoldersService()
        self._dir_alias = DirectoryAliasService(self.recent_folders)
        # Directory discovery registry (MVP, in-memory)
        self.directory_info_service = DirectoryInfoService()

        # Prevent auto-dialogs during initialization
        self._initializing = True

        # Track selected source for "same as source" functionality
        self.selected_source = None

        # Load recent folders store
        try:
            self.recent_folders.load()
        except Exception as e:
            log.error(f"[FILE_CONFIG_MANAGER] Failed to load RecentFoldersService: {e}")
        # Initialise legacy fields for compatibility with existing UI/state sync
        self.recent_source_folders = self.recent_folders.get_sources()
        self.max_recent_folders = 10
        self._mru_label_to_path: dict[str, str] = {}
        self._archive_label_to_path: dict[str, str] = {}
        # Track whether options have been populated into the view
        self._options_initialized: bool = False
        self._archive_options_initialized: bool = False

        # Restore last selected source option for persistence (Issue 3 fix)
        from ...config.ud_keys import UpdateDataKeys as ud_keys
        last_option = ud_config.get_value(ud_keys.Source.LAST_SOURCE_OPTION, SourceOptions.SELECT_FILES)
        self.state.source_option = last_option

        log.debug(f"[FILE_CONFIG_MANAGER] Initialized with recent folders support and restored option: {last_option}")

        # Subscribe to add-files channel
        self.subscribe_add_files_channel()

        # Initialization complete - allow normal operation
        self._initializing = False

    # =============================================================================
    # SOURCE SELECTION METHODS
    # =============================================================================

    def handle_source_option_change(self, option_data):
        """Handle source option change from UI - UPDATE OPTION ONLY, DON'T TRIGGER DIALOG."""
        try:
            # Extract the option from the event data
            if hasattr(option_data, 'option'):
                option = option_data.option
            else:
                option = option_data

            log.debug(f"[FILE_CONFIG_MANAGER] Source option changed: {option}")

            # Update state and save preference - DO NOT trigger file selection
            self.state.source_option = option

            # Save the last selected option to config for persistence
            from ...config.ud_keys import UpdateDataKeys as ud_keys
            ud_config.set_value(ud_keys.Source.LAST_SOURCE_OPTION, option)

            # Sync state to view to update UI
            self.state_manager.sync_state_to_view()

            log.debug(f"[FILE_CONFIG_MANAGER] Source option updated and saved: {option}")

        except Exception as e:
            log.error(f"[FILE_CONFIG_MANAGER] Error handling source option change: {e}")
    
    def handle_source_select(self, selection_type):
        """
        Handle source selection request from the view.
        
        Args:
            selection_type: Type of source selection (files/folder)
        """
        try:
            log.debug(f"[FILE_CONFIG_MANAGER] Source select requested: {selection_type}")
            
            if selection_type == SourceOptions.SELECT_FILES:
                self._select_files()
            elif selection_type == SourceOptions.SELECT_FOLDER:
                self._select_folder()
            else:
                # Check if an MRU entry label was selected
                mru_label = str(selection_type)
                folder_path = self._mru_label_to_path.get(mru_label)
                if folder_path and os.path.isdir(folder_path):
                    log.debug(f"[FILE_CONFIG_MANAGER] MRU source selected from options: {folder_path}")
                    try:
                        # Preference: quick select (skip dialog) vs confirm via dialog at MRU path
                        quick_select = bool(ud_config.get_value('update_data.quick_select_recent_source', False))
                        if quick_select:
                            files = FileUtils.discover_files_in_folder(folder_path)
                            if not files:
                                self._emit_error_dialog("No files found", f"No supported files found in:\n{folder_path}")
                                return
                            ud_config.set_value(ud_keys.Paths.LAST_SOURCE_DIR, folder_path)
                            self._update_recent_folders(folder_path)
                            self._process_selected_files(files, 'folder', folder_path)
                        else:
                            # Open files dialog initialised at MRU path for user confirmation
                            file_paths = FileSelector.get_paths(
                                selection_type='files',
                                initial_dir=folder_path,
                                title="Select Files to Process",
                                parent=None,
                            )
                            if file_paths:
                                # Derive folder from first selected file for state and MRU
                                confirmed_folder = os.path.dirname(file_paths[0])
                                ud_config.set_value(ud_keys.Paths.LAST_SOURCE_DIR, confirmed_folder)
                                self._update_recent_folders(confirmed_folder)
                                self._process_selected_files(file_paths, 'files', confirmed_folder)
                            else:
                                log.debug("[FILE_CONFIG_MANAGER] MRU selection cancelled via dialog")
                    except Exception as e:
                        log.error(f"[FILE_CONFIG_MANAGER] Error selecting MRU folder: {e}")
                else:
                    log.warning(f"[FILE_CONFIG_MANAGER] Unknown selection type: {selection_type}")
                
        except Exception as e:
            log.error(f"[FILE_CONFIG_MANAGER] Error in source selection: {e}")

    def _select_files(self):
        """Handle individual file selection."""
        try:
            # Prevent auto-dialogs during initialization (Issue 1 fix)
            if getattr(self, '_initializing', False):
                log.debug("[FILE_CONFIG_MANAGER] Skipping file dialog during initialization")
                return

            last_dir = ud_config.get_value(ud_keys.Paths.LAST_SOURCE_DIR, "") or (self.recent_folders.get_sources()[0] if self.recent_folders.get_sources() else "")
            log.debug(f"[FILE_CONFIG_MANAGER] Opening file dialog with last_dir: {last_dir}")

            file_paths = FileSelector.get_paths(
                selection_type='files',
                initial_dir=last_dir,
                title="Select Files to Process",
                parent=None
            )
            
            if file_paths:
                log.debug(f"[FILE_CONFIG_MANAGER] Dialog returned file_paths: {file_paths}")
                
                # Update last directory
                folder_path = os.path.dirname(file_paths[0])
                ud_config.set_value(ud_keys.Paths.LAST_SOURCE_DIR, folder_path)
                
                # Update recent folders
                self._update_recent_folders(folder_path)
                
                self._process_selected_files(file_paths, 'files', folder_path)
            else:
                log.debug("[FILE_CONFIG_MANAGER] File selection cancelled")
                
        except Exception as e:
            log.error(f"[FILE_CONFIG_MANAGER] Error in file selection: {e}")

    def _select_folder(self):
        """Handle folder selection."""
        try:
            # Prevent auto-dialogs during initialization (Issue 1 fix)
            if getattr(self, '_initializing', False):
                log.debug("[FILE_CONFIG_MANAGER] Skipping folder dialog during initialization")
                return

            last_dir = ud_config.get_value(ud_keys.Paths.LAST_SOURCE_DIR, "") or (self.recent_folders.get_sources()[0] if self.recent_folders.get_sources() else "")
            log.debug(f"[FILE_CONFIG_MANAGER] Opening folder dialog with last_dir: {last_dir}")

            file_paths = FileSelector.get_paths(
                selection_type='folder',
                initial_dir=last_dir,
                title="Select Folder to Process",
                parent=None
            )
            
            if file_paths:
                log.debug(f"[FILE_CONFIG_MANAGER] Dialog returned {len(file_paths)} files from folder")

                # Get folder path from first file for config/recent folders
                folder_path = os.path.dirname(file_paths[0])

                # Update last directory and recent folders
                ud_config.set_value(ud_keys.Paths.LAST_SOURCE_DIR, folder_path)
                self._update_recent_folders(folder_path)

                self._process_selected_files(file_paths, 'folder', folder_path)
            else:
                log.debug("[FILE_CONFIG_MANAGER] Folder selection cancelled")
                
        except Exception as e:
            log.error(f"[FILE_CONFIG_MANAGER] Error in folder selection: {e}")

    def _process_selected_files(self, file_paths: List[str], source_type: str, source_path: str):
        """
        Apply the selection to the file list (file view) via FileInfoManager.
        
        NOTE: This does NOT process files. Actual processing happens only when the
        user triggers the processing pipeline (e.g. clicking Process).
        
        Args:
            file_paths: List of selected file paths
            source_type: Type of source ('files' or 'folder')
            source_path: Source directory path
        """
        try:
            log.debug(f"[FILE_CONFIG_MANAGER] Handling selection of {len(file_paths)} file(s)")
            
            # Store source info for "same as source" functionality
            self.selected_source = {
                "type": source_type,
                "path": source_path,
                "file_paths": file_paths,
            }

            # Record directory discovery state for folder sources (MVP)
            if source_type == 'folder':
                self._update_directory_info(source_path)

            # Delegate to FileInfoManager
            if source_type == 'folder':
                # Windows workaround: selecting one file in the folder acts as folder selection.
                # Load all supported files from that folder (case-insensitive by config)
                try:
                    from .._view.shared_components.file_selector import FileSelector
                    all_files = FileSelector.discover_files(source_path)
                except Exception:
                    all_files = file_paths  # fallback; should not happen
                self.file_info_manager.set_files(all_files, source_path)
                # Update MRU for source folders
                self._update_recent_folders(source_path)
            else:
                # Files selection explicitly adds only those files to the file view (not processing)
                self.file_info_manager.set_files(file_paths, source_path)

            # Emit source discovered event
            self.local_bus.emit(
                ViewEvents.SOURCE_DISCOVERED.value,
                SourceDiscoveredEvent(
                    source_type=source_type,
                    files=file_paths,
                    path=source_path,
                    count=len(file_paths),
                ),
            )

            if source_type == 'folder':
                log.debug(f"[FILE_CONFIG_MANAGER] Folder selected: '{source_path}'. Loaded all supported files and updated MRU")
            else:
                log.debug(f"[FILE_CONFIG_MANAGER] Added {len(file_paths)} file(s) to file view from files selection")

            # Sync presenter state so the Guide Pane immediately reflects the source context
            try:
                if source_type == 'folder':
                    # If we computed all_files above, prefer that list for state
                    files_for_state = all_files if 'all_files' in locals() and all_files else file_paths
                    self.state_manager.set_source_folder(source_path, files=list(files_for_state or []))
                else:
                    self.state_manager.set_source_files(list(file_paths or []))
                self.state_manager.sync_state_to_view()
                log.debug("[FILE_CONFIG_MANAGER] State synchronised to view after source selection")
            except Exception as e:
                log.error(f"[FILE_CONFIG_MANAGER] Failed to sync state after selection: {e}")

            # Force the Guide Pane to show per-folder discovery controls for the resolved folder
            import os
            folder_ctx = source_path if source_type == 'folder' else (os.path.dirname(file_paths[0]) if file_paths else '')
            if folder_ctx and getattr(self.view, 'guide_pane', None):
                enabled = ud_config.is_discovery_enabled(folder_ctx)
                try:
                    self.view.guide_show_source_context_options(discovery_enabled=enabled, folder=folder_ctx)
                except Exception as e:
                    log.error(f"[FILE_CONFIG_MANAGER] Failed to show source context options: {e}")

            # If save is "same as source", ensure guide pane reflects that
            try:
                if hasattr(self.view, 'get_save_option') and self.view.get_save_option() == SaveOptions.SAME_AS_SOURCE.value:
                    if hasattr(self.view, 'set_guide_archive_summary'):
                        self.view.set_guide_archive_summary("Same as Source")
            except Exception:
                pass

        except Exception as e:
            log.error(f"[FILE_CONFIG_MANAGER] Error processing selected files: {e}")

    # =============================================================================
    # DIRECTORY DISCOVERY (MVP) INTEGRATION
    # =============================================================================
    def _update_directory_info(self, folder_path: str) -> None:
        """Enable discovery and set archive destination for the given folder.

        - Archive destination is derived from save option:
          - Same as Source -> folder_path
          - Select Location -> state.save_location (if available)
        - Marks last scan time after enablement.
        """
        try:
            p = Path(folder_path).resolve()
            if not p.exists() or not p.is_dir():
                log.warning(f"[FILE_CONFIG_MANAGER] _update_directory_info: not a directory: {folder_path}")
                return

            # Resolve archive destination according to MVP rules
            archive_dest: Optional[Path] = None
            try:
                save_opt = self.view.get_save_option() if hasattr(self.view, 'get_save_option') else None
            except Exception:
                save_opt = None

            if save_opt == SaveOptions.SAME_AS_SOURCE.value:
                archive_dest = p
            elif save_opt == SaveOptions.SELECT_LOCATION.value and getattr(self.state, 'save_location', None):
                archive_dest = Path(self.state.save_location).resolve()

            # Enable discovery; if directory is new to registry, archive_dest is required
            info = self.directory_info_service.enable_discovery(p, archive_dest=archive_dest or p)
            self.directory_info_service.mark_scanned_now(p)
            log.debug(f"[FILE_CONFIG_MANAGER] Discovery enabled for {p} (archive={info.archive_dest}) and scan marked")
        except Exception as e:
            log.error(f"[FILE_CONFIG_MANAGER] Error updating directory info: {e}")

    # =============================================================================
    # RECENT FOLDERS MANAGEMENT (Source MRU)
    # =============================================================================

    def _update_recent_folders(self, folder_path: str):
        """
        Add a folder to the top of the recent sources list and persist it.
        """
        try:
            if not folder_path or not os.path.isdir(folder_path):
                return

            # Update MRU via service (handles de-dup, cap, and save)
            self.recent_folders.add_source(folder_path)
            log.debug(
                f"[FILE_CONFIG_MANAGER] Recent source folders updated via service: top={self.recent_folders.get_sources()[:1]}"
            )

            # Refresh legacy cache and repopulate options list in the UI
            self.recent_source_folders = self.recent_folders.get_sources()
            self.state.recent_source_folders = self.recent_source_folders.copy()
            self._refresh_source_options(prefer_mru=True)
            self.state_manager.sync_state_to_view()

            log.debug(
                f"[FILE_CONFIG_MANAGER] Updated recent folders, now {len(self.recent_source_folders)} folders"
            )
        except Exception as e:
            log.error(f"[FILE_CONFIG_MANAGER] Error updating recent folders: {e}")

    def get_recent_folders(self) -> List[str]:
        """Get the list of recent source folders."""
        return self.recent_source_folders.copy()

    def clear_recent_folders(self):
        """Clear the recent source folders list."""
        try:
            self.recent_source_folders.clear()
            ud_config.set_value('recent_source_folders', [])
            self.state.recent_source_folders = []
            self.state_manager.sync_state_to_view()
            log.debug("[FILE_CONFIG_MANAGER] Cleared recent source folders")
        except Exception as e:
            log.error(f"[FILE_CONFIG_MANAGER] Error clearing recent folders: {e}")

    # =============================================================================
    # SAVE LOCATION METHODS
    # =============================================================================

    def handle_save_option_change(self, save_option):
        """Handle save location option change."""
        try:
            log.debug(f"[FILE_CONFIG_MANAGER] Save option changed: {save_option}")

            if save_option == SaveOptions.SAME_AS_SOURCE:
                self._set_same_as_source()
                if hasattr(self.view, 'set_guide_archive_summary'):
                    self.view.set_guide_archive_summary("Same as Source")
            elif save_option == SaveOptions.SELECT_LOCATION:
                self._select_save_location()
                # If a save location is already set (e.g., from previous sessions), reflect it
                if getattr(self.state, 'save_location', None) and hasattr(self.view, 'set_guide_archive_summary'):
                    self.view.set_guide_archive_summary(f"{self.state.save_location}")
            else:
                # Treat as an MRU archive label selection, or a direct folder path
                label = str(save_option)
                path = self._archive_label_to_path.get(label)
                # Fallback: case-insensitive lookup
                if not path:
                    low = label.lower()
                    for k, v in self._archive_label_to_path.items():
                        if k.lower() == low:
                            path = v
                            break
                # Fallback: the label itself is a full path
                if not path and os.path.isdir(label):
                    path = label
                if path and os.path.isdir(path):
                    self.state.save_location = path
                    ud_config.set_value(ud_keys.Paths.LAST_SAVE_DIR, path)
                    # persist MRU and refresh options
                    try:
                        self.recent_folders.add_archive(path)
                        self._refresh_archive_options(prefer_mru=True)
                    except Exception as e:
                        log.error(f"[FILE_CONFIG_MANAGER] Error updating archive MRU: {e}")
                    self.state_manager.sync_state_to_view()
                    if hasattr(self.view, 'set_guide_archive_summary'):
                        self.view.set_guide_archive_summary(f"{path}")
                else:
                    log.warning(f"[FILE_CONFIG_MANAGER] Unknown save option: {save_option}")

        except Exception as e:
            log.error(f"[FILE_CONFIG_MANAGER] Error handling save option change: {e}")

    def _set_same_as_source(self):
        """Set save location to same as source."""
        try:
            if self.selected_source and self.selected_source.get("path"):
                save_path = self.selected_source["path"]
                self.state.save_location = save_path
                self.state_manager.sync_state_to_view()
                log.debug(f"[FILE_CONFIG_MANAGER] Set save location to source: {save_path}")
            else:
                log.warning("[FILE_CONFIG_MANAGER] No source selected for 'same as source'")
        except Exception as e:
            log.error(f"[FILE_CONFIG_MANAGER] Error setting same as source: {e}")

    def _select_save_location(self):
        """Handle custom save location selection."""
        try:
            last_save_dir = ud_config.get_value(ud_keys.Paths.LAST_SAVE_DIR, "") or ""

            # Use view's native folder dialog for archive selection
            folder_path = None
            if hasattr(self.view, 'show_folder_dialog'):
                try:
                    folder_path = self.view.show_folder_dialog("Select Archive Folder", last_save_dir)
                except Exception as e:
                    log.error(f"[FILE_CONFIG_MANAGER] View folder dialog failed: {e}")

            if folder_path:
                ud_config.set_value(ud_keys.Paths.LAST_SAVE_DIR, folder_path)
                self.state.save_location = folder_path
                # Persist to archive MRU and refresh options
                try:
                    self.recent_folders.add_archive(folder_path)
                    self._refresh_archive_options(prefer_mru=True)
                except Exception as e:
                    log.error(f"[FILE_CONFIG_MANAGER] Error updating archive MRU after selection: {e}")
                self.state_manager.sync_state_to_view()
                log.debug(f"[FILE_CONFIG_MANAGER] Selected save location: {folder_path}")
                # Update guide pane summary
                if hasattr(self.view, 'set_guide_archive_summary'):
                    self.view.set_guide_archive_summary(f"{folder_path}")
            else:
                log.debug("[FILE_CONFIG_MANAGER] Save location selection cancelled")
                
        except Exception as e:
            log.error(f"[FILE_CONFIG_MANAGER] Error selecting save location: {e}")

    # =============================================================================
    # UTILITY METHODS
    # =============================================================================
    
    def subscribe_add_files_channel(self):
        """Subscribe to add files events."""
        try:
            self.local_bus.subscribe(ViewEvents.ADD_FILES_REQUESTED.value, self._on_add_files_requested)
            log.debug("[FILE_CONFIG_MANAGER] Subscribed to ADD_FILES_REQUESTED events")
        except Exception as e:
            log.error(f"[FILE_CONFIG_MANAGER] Error subscribing to add files events: {e}")

    def _on_add_files_requested(self, event_data):
        """Handle add files request."""
        try:
            log.debug("[FILE_CONFIG_MANAGER] Add files requested")
            self._select_files()
        except Exception as e:
            log.error(f"[FILE_CONFIG_MANAGER] Error handling add files request: {e}")

    def _emit_error_dialog(self, title: str, message: str):
        """Emit error dialog request."""
        try:
            from ..ui_events import DialogRequestEvent
            self.local_bus.emit(
                ViewEvents.ERROR_DIALOG_REQUESTED.value,
                DialogRequestEvent(
                    dialog_type="error",
                    title=title,
                    extra_data={"message": message}
                )
            )
        except Exception as e:
            log.error(f"[FILE_CONFIG_MANAGER] Error emitting error dialog: {e}")

    def _refresh_source_options(self, prefer_mru: bool = True):
        """
        Build combined source options incl. MRU and push to the view.
        If prefer_mru and there is at least one recent folder, select it.
        """
        # Always reload MRU store to reflect any external changes and prune missing paths
        try:
            self.recent_folders.load()
        except Exception as _e:
            pass
        # Base options (match existing UI text)
        base_options = [
            SourceOptions.SELECT_FOLDER.value,
            SourceOptions.SELECT_FILES.value,
        ]

        # Build MRU labels and mapping using DirectoryAliasService
        src_paths = []
        try:
            src_paths = [p for p in self.recent_folders.get_sources() if isinstance(p, str)]
        except Exception:
            src_paths = []
        mapping = self._dir_alias.build_mapping_for_paths(src_paths)
        labels = list(mapping.keys())
        self._mru_label_to_path = mapping
        options = base_options + labels

        # Determine selected option
        selected: Optional[str] = None
        if prefer_mru and labels:
            selected = labels[0]
        else:
            # Fall back to last saved source option (ensure string value)
            saved = getattr(self.state, 'source_option', None)
            if saved in (SourceOptions.SELECT_FOLDER, SourceOptions.SELECT_FILES):
                # Defensive: normalize enum to string
                selected = saved.value  # type: ignore[attr-defined]
            else:
                selected = saved or SourceOptions.SELECT_FILES.value

        # Push to view
        if hasattr(self.view, 'set_source_options'):
            try:
                self.view.set_source_options(options, selected)
                self._options_initialized = True
            except Exception as e:
                log.error(f"[FILE_CONFIG_MANAGER] Failed to set source options on view: {e}")

        # If an MRU label was selected: optionally quick-select files
        try:
            if selected and selected in mapping:
                quick_select = bool(ud_config.get_value('update_data.quick_select_recent_source', False))
                folder_path = mapping.get(selected, '')
                if folder_path and os.path.isdir(folder_path):
                    if quick_select:
                        # Quick-select: discover and apply files silently, which sets state appropriately
                        try:
                            files = FileUtils.discover_files_in_folder(folder_path)
                        except Exception:
                            files = []
                        if files:
                            ud_config.set_value(ud_keys.Paths.LAST_SOURCE_DIR, folder_path)
                            self._update_recent_folders(folder_path)
                            self._process_selected_files(files, 'folder', folder_path)
                    # When quick-select is disabled, do nothing here; MRU remains a UI nudge only.
                            log.debug(f"[FILE_CONFIG_MANAGER] Auto-applied MRU source silently: {folder_path} ({len(files)} files)")
        except Exception as e:
            log.error(f"[FILE_CONFIG_MANAGER] Failed to auto-apply MRU source: {e}")

    def _refresh_archive_options(self, prefer_mru: bool = True):
        """
        Build archive options including MRU and push to the view.
        Select MRU by default if available, otherwise keep current selection.
        """
        # Reload MRU store to reflect external changes and prune missing paths
        try:
            self.recent_folders.load()
        except Exception as _e:
            pass
        base_options = [
            SaveOptions.SAME_AS_SOURCE,
            SaveOptions.SELECT_LOCATION,
        ]

        # Build via DirectoryAliasService
        self._dir_alias.refresh_from_recent()
        mapping = self._dir_alias.mapping()
        self._archive_label_to_path = mapping
        labels = self._dir_alias.labels()
        options = base_options + labels

        # Choose selection
        selected: Optional[str] = None
        if prefer_mru and labels:
            selected = labels[0]
        # else keep whatever the view shows; we won't force a selection

        if hasattr(self.view, 'set_archive_options'):
            try:
                self.view.set_archive_options(options, selected)
                self._archive_options_initialized = True
            except Exception as e:
                log.error(f"[FILE_CONFIG_MANAGER] Failed to set archive options on view: {e}")

    # ===== PUBLIC API =====
    def ensure_options_initialized(self) -> None:
        """Idempotently populate source/archive options after widgets are ready.

        Only performs work if options have not yet been pushed to the view.
        """
        if not self._options_initialized:
            self._refresh_source_options(prefer_mru=True)
        if not self._archive_options_initialized:
            self._refresh_archive_options(prefer_mru=True)

    def refresh_source_options(self, prefer_mru: bool = True) -> None:
        """Explicit API to rebuild and push Source options (base + MRU)."""
        self._refresh_source_options(prefer_mru=prefer_mru)

    def refresh_archive_options(self, prefer_mru: bool = True) -> None:
        """Explicit API to rebuild and push Archive options (base + MRU)."""
        self._refresh_archive_options(prefer_mru=prefer_mru)
