# Directory Discovery – MVP Design Decisions

- __Goal__: Golden path for new users: files in the current source folder appear in the file list automatically with zero config.

- __<PERSON><PERSON> (MVP)__:
  - Single current source folder (shown in `guide_pane.py`).
  - "Enable file discovery for this folder" is per-folder (not global).
  - "New" files = files present in the root of the source folder. We rely on archiving processed files to keep root clean (no DB history check in <PERSON>).
  - Archive destination taken from left panel option "Same as Source" (i.e., `<source>/archive` or equivalent). Processed/old files are moved there.

- __Ownership & Boundaries__:
  - `GuidePanePresenter`: orchestrates UI actions/state for source folder and discovery toggle; updates file list via `IUpdateDataView` methods only.
  - `DirectoryInfoService` (new): stores per-folder roles/settings/state (path, role, discovery_enabled, archive_dest, last_scan). No UI logic.
  - `folder_monitoring_service` (existing): provides low-level watch (start/stop) for filesystem changes; integrated by presenter.
  - Event bus: not used for this flow in MVP. Presenter calls view interface directly.

- __Models & Services__:
  - `fm/core/directory/models/directory_info.py` — `DirectoryInfo` dataclass.
  - `fm/core/directory/services/directory_info_service.py` — in-memory CRUD + simple helpers (enable/disable, mark_scanned_now). Persistence hooks stubbed for later.

- __UI Contract (examples)__:
  - View interface exposes explicit methods, e.g. `set_source_folder(path)`, `show_discovery_state(enabled)`, `add_files(paths)`.
  - `GuidePanePresenter` reacts to toggle: enable discovery -> (a) initial scan to list files in root; (b) start watcher; (c) update discovery state in view.

- __Future-ready__:
  - Design anticipates multiple folders by keying on absolute paths in `DirectoryInfoService`.
  - Later: add `ImportHistoryService` for DB-level "already imported" checks; integrate richer archive policies; move/rename existing services into `fm/core/directory/...` subpackages.

- __Naming__:
  - Folder-related services and models live under: `fm/core/directory/services/` and `fm/core/directory/models/`.
  - Avoid "manager" names for services; prefer explicit `*Service`.

- __Non-Goals (MVP)__:
  - Multi-folder UI management.
  - DB duplicate checks before listing.
  - Broad refactors/renames of existing services.

- __Risks/Notes__:
  - Ensure presenter remains Qt-decoupled; only call `IUpdateDataView`.
  - Keep logic explicit; avoid speculative complexity.
  - Add minimal logging via `fm.core.services.logger.log` when wiring presenters/services.
