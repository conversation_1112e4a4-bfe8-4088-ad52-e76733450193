"""Local configuration manager for Update Data module."""

from pathlib import Path
from typing import List

from ....core.services.event_bus import Events, global_event_bus
from ....core.config.base_local_config import BaseLocalConfig
from .ud_keys import UpdateDataKeys


class UpdateDataConfig(BaseLocalConfig[UpdateDataKeys]):
    """Configuration manager for update_data module.

    Provides:
    1. Component-specific config access through BaseLocalConfig
    2. Type-safe key management through UpdateDataKeys
    3. Default value initialization from update_data_defaults.yaml
    4. Access to global event bus
    """

    def __init__(self):
        super().__init__()
        self.events = global_event_bus

        # Debug logging is now handled directly in the presenter

        # Log initialization
        self.events.publish(
            Events.LOG_EVENT,
            {
                "level": "DEBUG",
                "module": "update_data.config",
                "message": "Configuration initialized with default settings",
            },
        )

    def get_defaults_file_path(self) -> Path:
        """Get the path to the Update Data defaults.yaml file."""
        return Path(__file__).parent / "defaults.yaml"

    def get_defaults(self) -> dict:
        """Get default values for Update Data module."""
        return UpdateDataKeys.get_defaults()

    # Debug logging is now handled directly in the presenter
    # by setting the core logger's console level

    # File Processing Methods
    def get_max_file_size(self) -> int:
        """Get maximum allowed file size in bytes."""
        return self.get_value(UpdateDataKeys.Files.MAX_SIZE)

    def get_allowed_file_extensions(self) -> list[str]:
        """Get allowed file extensions for upload."""
        extensions = self.get_value(UpdateDataKeys.Files.ALLOWED_EXTENSIONS, default=[])
        
        # Simple debug print to terminal
        print(f"[DEBUG] ud_config.get_allowed_file_extensions() returning: {extensions}")
        
        # Debug: Log what extensions we're getting
        self.events.publish(
            Events.LOG_EVENT,
            {
                "level": "DEBUG",
                "module": "update_data.config",
                "message": f"Loaded file extensions: {extensions}",
            },
        )
        
        return extensions

    def is_file_allowed(self, file_path: str) -> bool:
        """Check if file is allowed based on extension."""
        return Path(file_path).suffix.lower() in self.get_allowed_file_extensions()

    def should_ignore_empty(self) -> bool:
        """Check if empty files should be ignored."""
        return self.get_value(UpdateDataKeys.Files.IGNORE_EMPTY)

    # Source Management Methods
    def get_default_source_type(self) -> str:
        """Get default source type."""
        return self.get_value(UpdateDataKeys.Source.DEFAULT_TYPE)

    def get_recent_sources(self) -> List[str]:
        """Get list of recent sources."""
        return self.get_value(UpdateDataKeys.Source.RECENT_SOURCES, default=[])

    def add_recent_source(self, source_path: str):
        """Add source to recent sources list."""
        sources = self.get_recent_sources()
        if str(source_path) not in sources:
            sources.append(str(source_path))
            if len(sources) > self.get_value(UpdateDataKeys.Logging.MAX_RECENT_JOBS):
                sources.pop(0)
            self.set_value(UpdateDataKeys.Source.RECENT_SOURCES, sources)

    # Validation Methods
    def is_strict_validation(self) -> bool:
        """Check if strict validation is enabled."""
        return self.get_value(UpdateDataKeys.Validation.STRICT_MODE)

    # Logging Methods
    def should_log_processed(self) -> bool:
        """Check if processed files should be logged."""
        return self.get_value(UpdateDataKeys.Logging.LOG_PROCESSED)

    def get_max_recent_jobs(self) -> int:
        """Get maximum number of recent jobs to track."""
        return self.get_value(UpdateDataKeys.Logging.MAX_RECENT_JOBS)

    # File Discovery (per-folder) Methods
    def get_discovery_map(self) -> dict:
        """Return the persisted per-folder file discovery map."""
        return dict(self.get_value(UpdateDataKeys.Discovery.BY_FOLDER, default={}) or {})

    def is_discovery_enabled(self, folder_path: str) -> bool:
        """Check if file discovery is enabled for a folder (normalised path key)."""
        if not folder_path:
            return False
        key = str(Path(folder_path).expanduser().resolve())
        dm = self.get_discovery_map()
        return bool(dm.get(key, False))

    def set_discovery_enabled(self, folder_path: str, enabled: bool) -> None:
        """Persist per-folder file discovery preference (normalised path key)."""
        if not folder_path:
            return
        key = str(Path(folder_path).expanduser().resolve())
        dm = self.get_discovery_map()
        dm[key] = bool(enabled)
        self.set_value(UpdateDataKeys.Discovery.BY_FOLDER, dm)

    # Path Methods
    def get_app_data_dir(self) -> str:
        """Get application data directory."""
        return self.get_path(UpdateDataKeys.Paths.DATA)

    def get_temp_dir(self) -> str:
        """Get temporary directory."""
        return self.get_path(UpdateDataKeys.Paths.TEMP)

    def set_path(self, key: UpdateDataKeys.Paths, path: str):
        """Set path in config and ensure directory exists."""
        path = str(Path(path).expanduser().resolve())
        Path(path).mkdir(parents=True, exist_ok=True)
        self.set_value(key, path)

    # Master File Management
    def update_master_location(self, path: str):
        """Update master file location in config."""
        recent_masters = self.get_value(
            UpdateDataKeys.History.RECENT_MASTERS, default=[]
        )
        if str(path) not in recent_masters:
            recent_masters.append(str(path))
            if len(recent_masters) > 10:  # Keep only last 10
                recent_masters.pop(0)
            self.set_value(UpdateDataKeys.History.RECENT_MASTERS, recent_masters)


# Global instance
ud_config = UpdateDataConfig()
