# Update Data Refactor – Consolidated Review and Action Plan

Date: 2025-08-04
Owner: Product/Architecture
Scope: Consolidate current knowledge (brainstorming, architecture index, legacy guides, and new UI guide) into a single review with gaps and a pragmatic action plan focused on Update Data.

---

## 1) Sources Consolidated

1. Brainstorming session
   - File: DOCS/brainstorming-session-results.md
   - Status: Not reviewed in this doc (content referenced; integrate highlights during next pass)

2. Architecture index
   - File: DOCS/architecture_index.md
   - Note: This appears to be at workspace root DOCS; treat as high-level map connecting current components and docs

3. Legacy documentation
   - Folder: flatmate/DOCS (multiple legacy guides and overviews)
   - Example: Codebase onboarding, module overviews, architecture subfolders

4. Update Data – Legacy Onboarding
   - File: flatmate/DOCS/_GUIDES/Codebase-Onboarding-Guide.md
   - Highlights:
     - MVP with module presenters
     - Strict shared-components import rules to avoid circular imports
     - Event-driven architecture via event_bus
     - Guidance on avoiding over-engineering of state systems
     - Documentation standards for module-local workspaces

5. Update Data – New UI Deep-Dive
   - File: flatmate/DOCS/_GUIDES/Update-Data-UI-Deep-Dive.md
   - Highlights:
     - Central Switchboard pattern: View translates Qt signals → Local Bus intents, managers emit typed state events → View renders
     - Explicit channel separation (e.g., SOURCE_SELECT_REQUESTED vs add_files_requested) to prevent dialog collisions
     - Typed state events (dataclasses) for UI rendering, dialog requests, and processing lifecycle
     - Presenter allowed for initial morphing/setup; runtime processing feedback should come via events
     - Verification checklist to assert single-canonical-rendering-path and one-dialog-per-action

6. Workspace protocols
   - Folder: flatmate/DOCS/_PROTOCOLS
   - Intent: meta-level process/protocols; not a constraint on module-local docs

7. Feature session record
   - File: flatmate/DOCS/_FEATURES/REFACTOR_update-data_event-first/SESSION_LOG.md
   - Intent: session-level progress log; keep for historical trail

---

## 2) Current-State Synthesis (Update Data)

A. Architecture and Patterns
- MVP retained; goal is improved decoupling via:
  - View as central switchboard for Qt → Intent translation
  - Managers/presenter subscribe to intents on a local event bus
  - Managers emit typed state events; View renders from these events
- Event naming and channel scoping clarified to prevent collisions:
  - Left Panel selection uses SOURCE_SELECT_REQUESTED with values (SELECT_FILES | SELECT_FOLDER)
  - File Pane “Add files” uses a distinct add_files_requested channel
- Presenter may call interface methods for initial layout/morphing; runtime processing flows should come through events for testability and single-source rendering

B. Code Anchors (examples)
- View wiring: flatmate/src/fm/modules/update_data/_ui/ud_view.py
- File selection flow:
  - Subscribe: flatmate/src/fm/modules/update_data/_ui/_presenter/file_management.py:96
  - Handler: flatmate/src/fm/modules/update_data/_ui/_presenter/file_management.py:110
- Typed UI events module: flatmate/src/fm/modules/update_data/_ui/ui_events.py
- Local event bus: flatmate/src/fm/modules/update_data/services/local_event_bus.py
- File list management and presenter orchestration present under:
  - _ui/_presenter/file_list_manager.py
  - ud_presenter.py

C. Documentation and Workspace
- Two systems observed:
  1) Module-local workspace: fm/modules/update_data/UD_REFACTORING_docs_workspace
  2) Meta protocols and features: flatmate/DOCS/_PROTOCOLS and flatmate/DOCS/_FEATURES/...
- Developer intent: keep meta/protocol docs in flatmate/DOCS and allow flexible, module-local workspaces close to code for active refactors

---

## 3) Gaps and Inconsistencies

1) Event Contracts — Partial, need authoritative registry
- Gap: While channels and typed events are described, a single authoritative registry (namespaces, payload dataclasses, ownership) is not clearly centralised.
- Impact: Risk of channel drift and collisions; onboarding overhead.

2) Presenter vs Event Boundaries — Mixed usage without explicit decision tree
- Gap: Good high-level rules exist, but code may still contain mixed direct interface updates for runtime flows.
- Impact: Duplicated rendering paths; harder to test and reason about state.

3) Dialog Ownership Policy — Needs explicit “where dialogs come from”
- Gap: The deep-dive prescribes dialog-request events from managers; ensure legacy code does not show dialogs directly from presenter or sub-widgets.
- Impact: Inconsistent user feedback mechanisms and test complexity.

4) File Selection API — Unified but not exhaustively documented
- Gap: Summary exists; needs a concise developer-facing “how-to” with parameter conventions and sample flows.
- Impact: Re-implementation variance; subtle UX issues.

5) Verification Checklist — Not automated
- Gap: The checklists are manual. No quick harness exists to run through UI golden-path checks semi-automatically.
- Impact: Regressions may slip through.

6) Documentation Location Confusion — Competing systems
- Gap: flatmate/DOCS vs module-local workspace. Protocols should explicitly endorse module-local workspaces and define where “authoritative” docs live.
- Impact: Discoverability friction; context switching.

7) Table View System — Deprioritised for this PRD pass; still a priority area
- Gap: Improvement plan exists in other folders; not integrated here. Keep as next focus after Update Data foundations are locked.

---

## 4) Decisions (Proposed)

D1. Documentation Structure
- Meta/protocol and product-level docs: flatmate/DOCS (authoritative)
- Module-local active work: fm/modules/<module>/_<MODULE>_docs_workspace/...
  - This folder is authoritative for WIP session docs and immediate module architecture notes
- Cross-link: Each module’s README references its workspace; flatmate/DOCS references module workspaces for active efforts

D2. Event Registry
- Create a canonical registry file: flatmate/src/fm/modules/update_data/_ui/ui_event_contracts.md (developer-facing) and ensure typed events in code are linked
- Registry sections: Intents (string keys, payload schema), Typed State Events (dataclasses), DialogRequest policy, Ownership (who emits/consumes), Examples

D2.1 Guides Canonical Source (Updated Policy)
- Canonical location for ALL guides: flatmate/DOCS/_GUIDES (single source of truth)
- Protocols under flatmate/DOCS/_PROTOCOLS may contain POINTERS ONLY to guides in _GUIDES (no duplicated guide content)
- Migration note:
  - If a guide exists under _PROTOCOLS/GUIDES, replace content with a short pointer linking to the canonical guide in flatmate/DOCS/_GUIDES
  - New guides must be authored in flatmate/DOCS/_GUIDES and referenced elsewhere via links
- Rationale: Avoid duplication and drift; ensure one authoritative, maintained location

D2.1 Guides Canonical Source (Updated Policy)
- Canonical location for ALL guides: flatmate/DOCS/_GUIDES (single source of truth)
- Protocols under flatmate/DOCS/_PROTOCOLS may contain POINTERS ONLY to guides in _GUIDES (no duplicated guide content)
- Migration note:
  - If a guide exists under _PROTOCOLS/GUIDES, replace content with a short pointer linking to the canonical guide in flatmate/DOCS/_GUIDES
  - New guides must be authored in flatmate/DOCS/_GUIDES and referenced elsewhere via links
- Rationale: Avoid duplication and drift; ensure one authoritative, maintained location

D3. Presenter/Events Decision Tree
- Short guide in the registry doc:
  - Initial morphing/setup → Presenter interface calls allowed
  - Runtime processing feedback, state changes, status, dialogs → Typed events only
  - Sub-widgets do not call presenter methods that cause dialogs directly; View bridges signals → intents; managers produce dialog-request events

D4. Unified File Selection API Cheatsheet
- Add to the registry doc with snippet examples
- Include parameter naming conventions, persistence notes, and usage from FileManager

D5. Verification Harness (Incremental)
- Add a minimal UI verification suite (manual/automated hybrid):
  - Scriptable steps triggering SOURCE_SELECT_REQUESTED and add_files_requested in test context
  - Assertions for: exactly-one-dialog, FILE_LIST_UPDATED drives render, PROCESSING_STARTED/COMPLETED gates enablement
- Place doc under flatmate/DOCS/_REPORTS or module workspace; tests under tests/ with PySide6 harness where feasible

---

## 5) Action Plan (Prioritised, Pragmatic)

Phase 1 — Stabilise Contracts and Docs (Update Data)
1. Create the canonical UI Event Contracts doc
   - File: flatmate/src/fm/modules/update_data/_ui/ui_event_contracts.md
   - Content: channel registry, payload schema, ownership, presenter/event decision tree, dialog policy, file selection cheatsheet, examples
2. Code sweep: enforce dialog-request events
   - Remove/replace any direct presenter/dialog calls that bypass the event flow
3. Ensure add_files_requested vs SOURCE_SELECT_REQUESTED separation in code paths
   - Verify subscription points and handlers match the deep-dive
4. Add minimal unit tests for event dataclasses and bus integration
   - Cover: payload validation, namespace uniqueness, mapping utilities (if any)

Phase 2 — Verification Harness and UX Parity
5. Build minimal UI verification harness (manual first, with scripted scaffolding)
    - Assert: one-dialog-per-action, single-render-path, processing lifecycle events
6. Update onboarding and module README
    - Link to ui_event_contracts.md and the deep-dive guide
7. Close gaps in FileSelector API reference
    - Example-based snippets and defaults
8. Consolidate guides to the canonical location
    - Ensure any overlapping content in flatmate/DOCS/_PROTOCOLS/GUIDES is converted into pointers to flatmate/DOCS/_GUIDES
    - Add a short “Canonical Guides Policy” note in protocols README
8. Consolidate guides to the canonical location
    - Ensure any overlapping content in flatmate/DOCS/_PROTOCOLS/GUIDES is converted into pointers to flatmate/DOCS/_GUIDES
    - Add a short “Canonical Guides Policy” note in protocols README

Phase 3 — Table View Next Steps (Staged)
8. Draft “Table View Improvement Plan (v0)”
   - Current-state scan, issues list, first two actionable tasks with acceptance criteria
   - Keep separate doc, but reference from this review
9. Align toolbar/dialog policies with Update Data patterns where applicable

---

## 6) Deliverables Checklist

- [ ] ui_event_contracts.md created and linked from:
  - [ ] Update-Data-UI-Deep-Dive.md
  - [ ] Module README
- [ ] Presenter/event decision tree documented
- [ ] Dialog-request ownership enforced in code
- [ ] File selection API cheatsheet added
- [ ] Minimal unit tests for event contracts
- [ ] UI verification harness outline and initial checks
- [ ] Onboarding/README links updated
- [ ] Table View Improvement Plan (v0) drafted

---

## 6.2) Evidence Convention Review (adopted policy)

Decision: Inline-by-default with optional centralised for heavy troubleshooting.

A) Inline Evidence (Default)
- Store lightweight artifacts adjacent to the session log:
  - SESSION_LOG.md
  - session_evidence/ (screenshots/, logs/, code_samples/)
- Pros: Close to context, minimal ceremony
- Usage: All FEATURE, REFACTOR, MAINTENANCE by default

B) Centralised Evidence (Optional)
- Only when volume/complexity warrants it (e.g., TROUBLESHOOT sessions or multi-day investigations):
  - EVIDENCE/{error_logs,screenshots,code_samples}/ under the session folder
- Pros: Scales for heavy evidence gathering; predictable layout

Protocol Note:
- Protocols will present Inline as default; Centralised is an option for troubleshooting-heavy work.
- Each session records the chosen mode at the top of SESSION_LOG.md:
  - Evidence Mode: inline | centralised

No retroactive migrations required; apply policy to new sessions.

---

## 6.1) Experimental Documents Evaluation (Smart Widget and others)

Scope: DOCS/COMPONENT_RECIPES_PROPOSED_WORKFLOWS_and_GUIDES

Decision: Mark as Experimental (not authoritative), with criteria for future adoption. Maintain alignment with MVP and current Update Data event-first approach.

A) Smart Widget Recipe (experimental)
- Summary: Proposes a self-contained MVP-style component with its own presenter, config, and models.
- Current concern:
  - Uses PyQt5 in examples; Flatmate uses PySide6.
  - Presenter must remain Qt-free and respect central View switchboard and event-first policy.
  - Risk of duplicating patterns already covered by shared components and module switchboard.
- Experimental guardrails (if prototyped):
  1) Presenter contains zero Qt imports; view emits high-level signals only.
  2) Widget events are bridged via the module View to canonical intents; no direct dialogs from the widget/presenter.
  3) Typed state events drive rendering; avoid parallel direct updates.
  4) File structure and naming follow project conventions; avoid introducing conflicting bases.
  5) Add explicit “integration constraints” section to the recipe linking to ui_event_contracts.md.
- Adoption criteria:
  - Demonstrated reduction in coupling or duplication vs. existing shared components
  - Clear testability benefits (component-level tests + module event integration)
  - No divergence from event-first policy; zero circular imports; performance parity
  - At least one real integration in a module without regressions across two sessions
- Action:
  - Keep in experimental folder; update examples to PySide6 and add an “Integration with Update Data” subsection referencing the canonical event registry once created.

B) Other speculative docs (placeholder)
- Keep as experimental references; add pointers indicating authoritative documents live under flatmate/DOCS/_GUIDES or module-local workspaces.
- When a speculative workflow overlaps with existing protocols, convert it into a pointer or merge into the authoritative protocol to avoid drift.

---

## 7) Resolutions to Open Questions (incorporating feedback)

1) Module-local workspaces and protocol consistency
- Decision: Use flexible module-local workspaces with a minimal baseline, and make flatmate/DOCS the meta/authoritative index.
  - Baseline structure for fm/modules/<module>/_<MODULE>_docs_workspace/:
    - SESSION_<N>/ (session_log_<N>.md, implementation_plan.md, implementation_options.md)
    - ARCHITECTURE/ (module notes/diagrams)
    - outstanding_items.md
    - changelog.md
  - Protocol harmonisation:
    - Add a “Workspace Root override” option in protocols so sessions can live near code
    - Normalise evidence folder usage across workflows:
      - EVIDENCE/{error_logs,screenshots,code_samples}
    - Guides cohesion:
      - Authoritative guides under flatmate/DOCS/_GUIDES
      - Protocols may include pointers to avoid divergence

2) Where GUI verification lives and testing protocol
- Decision: Place semi-automated GUI verification under tests/ with module-oriented structure and provide a testing protocol.
  - tests/gui/update_data/ for PySide6 harness checks (golden paths, dialogs, enablement)
  - tests/gui/shared/ for harness utilities
  - Add testing protocol pointer and outline:
    - flatmate/DOCS/_PROTOCOLS/GUIDES/testing_protocol.md (pointer; authoritative details may live centrally)
    - Include venv usage (flatmate/.venv_fm313), standard commands, and troubleshooting
  - Session docs reference these tests to cut time lost running tests

3) Intentionally non-event-driven legacy behaviours
- Decision: Default event-first; allow narrow, documented exceptions:
  - Initial morphing/setup and one-off initial UI adjustments can use presenter → view interface calls when not duplicating event-render logic
  - No dialogs directly from presenter or sub-widgets; dialog-request events remain the only path
  - Any exception must be documented in ui_event_contracts.md with rationale and a convergence path

---

## 8) Immediate Follow-ups (from feedback)

- [ ] Update flatmate/DOCS/_PROTOCOLS/README.md to document “Workspace Root override” and shared EVIDENCE folder convention
- [ ] Add testing_protocol.md pointer and outline in flatmate/DOCS/_PROTOCOLS/GUIDES/
- [ ] Ensure Update Data module workspace reflects the baseline structure and links back to this review
- [ ] Cross-link brainstorming outcomes here and in protocols index

---

## 9) References (inline and clickable)

- Architecture overview: [DOCS/architecture_overview.md](DOCS/architecture_overview.md)
- Update Data UI Deep-Dive: [Update-Data-UI-Deep-Dive.md](flatmate/DOCS/_GUIDES/Update-Data-UI-Deep-Dive.md)
- Onboarding Guide: [Codebase-Onboarding-Guide.md](flatmate/DOCS/_GUIDES/Codebase-Onboarding-Guide.md)
- View wiring: [ud_view.py](flatmate/src/fm/modules/update_data/_ui/ud_view.py)
- FileManager subscribe: [file_management.py.subscribe_add_files_channel()](flatmate/src/fm/modules/update_data/_ui/_presenter/file_management.py:96)
- FileManager handler: [file_management.py._on_files_add_requested()](flatmate/src/fm/modules/update_data/_ui/_presenter/file_management.py:110)
- File list manager: [file_list_manager.py](flatmate/src/fm/modules/update_data/_ui/_presenter/file_list_manager.py)
- Local event bus: [local_event_bus.py](flatmate/src/fm/modules/update_data/services/local_event_bus.py)
- Typed UI events: [ui_events.py](flatmate/src/fm/modules/update_data/_ui/ui_events.py)
- Feature session log: [SESSION_LOG.md](flatmate/DOCS/_FEATURES/REFACTOR_update-data_event-first/SESSION_LOG.md)

---

This review aligns the recent refactor direction, clarifies gaps, and sets a focused action plan. Next step recommendation: create ui_event_contracts.md (Phase 1.1) and run a quick code sweep to enforce dialog-request events (Phase 1.2).