# File Discovery – Handover

This document summarises the refactor from legacy “auto_queue” to “file discovery” across the Update Data module. It explains the naming, config keys, interfaces, runtime flow, logging, and how to verify behaviour.

## Scope
- Replace all live “auto_queue” terminology with discovery naming.
- Keep MVP separation: presenter ↔ view via interfaces; presenter has zero Qt.
- Add precise debug logging at view and presenter to diagnose toggle issues.

## Naming Changes (live code)
- Signal: `publish_toggle_auto_queue_requested` → `publish_enable_file_discovery_toggled` in `GuidePaneV2`.
- View handler: `on_auto_queue_toggled(...)` → `on_enable_file_discovery_toggled(folder, enabled)` in `UpdateDataView`.
- Interface args: `auto_queue_enabled` → `discovery_enabled` in:
  - `IUpdateDataView.guide_show_source_context_options(...)`
  - `IGuidePaneView.show_source_context_options(...)`
  - `UpdateDataView.show_guide_source_options(...)` (helper)
- Managers/APIs: `FileDiscoveryManager` now calls discovery config methods.

## Config Keys – important
- The per-folder preference key has been shortened from “auto_queue” to “discovery”.
- Enum: `UpdateDataKeys.AutoQueue.BY_FOLDER` → `UpdateDataKeys.Discovery.BY_FOLDER` (string: `update_data.discovery.by_folder`).
- API:
  - `ud_config.get_auto_queue_map()` → `ud_config.get_discovery_map()`
  - `ud_config.is_auto_queue_enabled(folder)` → `ud_config.is_discovery_enabled(folder)`
  - `ud_config.set_auto_queue_enabled(folder, enabled)` → `ud_config.set_discovery_enabled(folder, enabled)`

Note: No legacy alias is retained. Existing persisted values under the old key will not be read unless migrated. For development this is acceptable; clear local config or migrate once if needed.

## Public Interfaces (MVP)
- `GuidePaneV2.publish_enable_file_discovery_toggled: Signal(str folder, bool enabled)`
- `UpdateDataView.on_enable_file_discovery_toggled(folder: str, enabled: bool)` → forwards to presenter handler.
- `IUpdateDataView.guide_show_source_context_options(discovery_enabled: bool, folder: Optional[str])`
- `IGuidePaneView.show_source_context_options(discovery_enabled: bool, folder: Optional[str])`
- `GuidePanePresenter.on_discovery_toggled(enabled: bool, folder: Optional[str])`
- `FileDiscoveryManager.set_discovery(folder: str, enabled: bool)` / `.get_discovery(folder: str) -> bool`

## Runtime Flow (happy path)
1. User toggles checkbox in Guide Pane Source section.
2. `GuidePaneV2.publish_enable_file_discovery_toggled(folder, enabled)` emits.
3. `UpdateDataView.on_enable_file_discovery_toggled(...)` normalises folder and calls presenter.
4. `GuidePanePresenter.on_discovery_toggled(...)`:
   - Persists via `FileDiscoveryManager.set_discovery(...)` (single source of truth).
   - Updates view: `view.guide_show_source_context_options(discovery_enabled=..., folder=...)`.
   - Clears any source notice (if set previously).
5. `GuidePaneV2.show_source_context_options(...)` sets checkbox without re-emitting (signals blocked internally).

## Logging
- Presenter: `[GuidePanePresenter] on_discovery_toggled: folder='...', enabled=...`.
- View: `[UD_VIEW] enable_file_discovery_toggled: folder='...', enabled=...`.
- Manager: `[FileDiscoveryManager] Persisted discovery ...` and reconciliation logs at debug.

## Checkbox behaviour (feedback loop prevention)
- Programmatic updates block the checkbox signal:
  - `GuidePaneOption.set_checked(...)` temporarily calls `blockSignals(True/False)`.
  - `SourceSection.set_enable_checked(...)` delegates to `GuidePaneOption`.
  This prevents presenter-driven UI updates from re-triggering the toggle.

## Files Updated (live)
- `update_data/_ui/_view/center_panel_components/guide_pane_v2.py`
- `update_data/_ui/ud_view.py`
- `update_data/_ui/_presenter/guide_pane_presenter.py`
- `update_data/_ui/interface/i_view_interface.py`
- `update_data/_ui/interface/i_guide_pane_view.py`
- `update_data/_ui/_presenter/file_config_manager.py` (call site updated)
- `update_data/_ui/_presenter/state_coordinator.py` (reads discovery state)
- `update_data/_ui/_presenter/file_discovery_manager.py` (config API renamed)
- `update_data/config/ud_keys.py` (AutoQueue → Discovery enum, key shortened)
- `update_data/config/ud_config.py` (methods renamed to discovery_*)

## Left as-is (deprecated/archived)
- `guide_pane_components/z_archive_deprecated/guide_pane.py`: still mentions `auto_queue`. Safe to ignore; not used in the live path.
- `auto_queue_manager.py`: legacy; not used by the new flow.

## How to verify quickly
- Launch app and select a folder as source.
- Toggle “File discovery” in Guide Pane:
  - Logs show view then presenter messages with folder + state.
  - Leave and re-enter folder selection; checkbox reflects persisted state.
  - No duplicate toggles when presenter updates the checkbox.
- Check persisted key moved to `update_data.discovery.by_folder` (dev-only):
  - Inspect local config dump or add a temporary print of `ud_config.get_discovery_map()` if needed.

## Known considerations
- Existing persisted values under `update_data.auto_queue.by_folder` won’t be read unless migrated. For dev only, clear config or apply a one-off migration when promoting.
- Some unrelated Qt paint warnings may appear (“QPainter::setPen: Painter not active”); this is not caused by discovery logic but by label repaint in another component.

## Exact Event Payload (live)
- Signal: `GuidePaneV2.publish_enable_file_discovery_toggled: Signal(str folder, bool enabled)`.
- Emit site: `GuidePaneV2._on_src_enable_toggled(checked: bool)`.
- Folder binding: `self._current_folder` is set by either:
  - `GuidePaneV2.show_source_context_options(discovery_enabled=..., folder=...)`, or
  - `GuidePaneV2.set_section_info('source', 'Source: <path>')` when a path-like text is provided.

## Signal Wiring Map (live code paths)
- `SourceSection.LabeledCheckBox.state_changed(bool)` → wired via `SourceSection.connect_enable_toggled(callback)`
- `GuidePaneV2._on_src_enable_toggled(bool)` → emits `publish_enable_file_discovery_toggled(folder: str, enabled: bool)`
- `UpdateDataView.on_enable_file_discovery_toggled(folder: str, enabled: bool)` → calls presenter handler
- `UpdateDataPresenter.toggle_file_discovery(folder: str, enabled: bool)` → persists via `FileDiscoveryManager.set_discovery(...)`, then syncs UI

Refs:
- `update_data/_ui/_view/center_panel_components/guide_pane_components/source_section.py`
- `update_data/_ui/_view/center_panel_components/guide_pane_v2.py`
- `update_data/_ui/ud_view.py`
- `update_data/_ui/_presenter/ud_presenter.py` and `file_discovery_manager.py`

## Delta vs Previous Iteration
- Then: Presenter owned the option and dispatched directly with the filename/folder.
- Now: The widget (`GuidePaneV2` → `SourceSection`) emits the domain signal, and the presenter receives it via the view. It is still inherently linked to the folder path; the link is explicit through `_current_folder` binding at the widget layer.

## Known Issue and Likely Root Cause
- Symptom: First click sometimes does not propagate.
- Likely cause: `GuidePaneV2._current_folder` not set at the time of the first user click, so `_on_src_enable_toggled` logs “toggle ignored: no current_folder” and returns without emitting.
- Binding is established by `show_source_context_options(..., folder=...)` or by `set_section_info('source', '...<path>...')`. If either occurs after the user can click, the first click is ignored.

## Minimal Fix Options (choose one or combine)
- Ensure binding before interaction:
  - Call `view.guide_show_source_context_options(discovery_enabled=..., folder=<resolved_folder>)` immediately after a folder selection is set, before the Guide Pane becomes interactive.
  - Or set the Source section path early via `CenterPanelLayout.set_source_path(path)` so `set_section_info('source', ...)` binds `_current_folder` immediately.
- Disable the checkbox until bound:
  - Keep `SourceSection.show_enable_option(False)` until `_current_folder` is set, then show and set its checked state programmatically.

## Verification Checklist (first-click path)
1. Select a folder.
2. Expect logs:
   - `[GuidePaneV2] set_section_info(source): bind folder context -> '...'` or
   - `[GuidePaneV2] show_source_context_options: ... resolved='...' ...` and
   - `[GuidePaneV2] set_enable_checked(programmatic): folder='...', to=...`
3. Click once. Expect the chain:
   - `[DebugCheckBox] clicked/toggled ...`
   - `[LabeledCheckBox] stateChanged -> emit state_changed ...`
   - `[GuidePaneV2] user toggle: folder='...', checked=...`
   - `[UD_VIEW] enable_file_discovery_toggled: folder='...', enabled=...`
   - `[UpdateDataPresenter] toggle_file_discovery: ...`
   - `FileDiscoveryManager.set_discovery(...)`
4. Re-open the same folder context; checkbox reflects persisted state.

## Log Signatures Cheat Sheet
- Widget: `[DebugCheckBox] clicked/toggled ...`, `[LabeledCheckBox] stateChanged -> emit ...`
- Guide pane: `[GuidePaneV2] show_source_context_options: ...`, `set_enable_checked(programmatic)`, `user toggle: folder=...`, `toggle ignored: no current_folder`
- View: `[UD_VIEW] enable_file_discovery_toggled: folder='...', enabled=...`
- Presenter: `[UpdateDataPresenter] toggle_file_discovery: ...`, `FDM.set_discovery done`, `syncing state to view ...`
- Manager: `[FileDiscoveryManager] Persisted discovery ...`, `get_discovery ...`

## Contact / Ownership
- Update Data module – Guide Pane and File Discovery: owned by Update Data team.
- Presenter-only logic; Qt-free policy enforced in presenters.
