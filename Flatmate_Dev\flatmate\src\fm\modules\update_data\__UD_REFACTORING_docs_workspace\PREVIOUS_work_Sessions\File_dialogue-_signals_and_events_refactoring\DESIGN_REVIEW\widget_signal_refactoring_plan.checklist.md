# Widget Signal Refactoring – Strict Compliance Checklist

## 1. Signal Location & Ownership
- [ ] All user-facing signals (file selection, file list change, etc.) are defined and emitted only in the view (e.g., `UDFileView`), not in any panel/layout manager.
- [ ] No legacy signal definitions or re-emissions remain in any `(x)_panel_layout.py` or manager file.

## 2. Signal Connections
- [ ] All presenters/controllers connect directly to the view’s signals, not to signals on panel/layout managers.
- [ ] All code that previously referenced manager signals is updated to reference the view’s signals.

## 3. Panel/Layout Manager Scope
- [ ] Panel/layout manager files contain only widget creation, placement, and layout logic—no event propagation, signal emission, or business logic.
- [ ] No business logic or event handling remains in any layout manager.

## 4. Imports & References
- [ ] All imports and references in the codebase use the new view-based signals.
- [ ] No code references legacy manager signals for any event.

## 5. Testing
- [ ] All tests that check UI events or signal handling are updated to use the view signals, not manager signals.
- [ ] No test references legacy manager signals.

## 6. Documentation
- [ ] The new signal flow and ownership is clearly documented in the design documents.
- [ ] Any diagrams or flowcharts are updated to show signals originating from the view.

## 7. Legacy Code Removal
- [ ] All legacy signal code, duplicated event handling, or shadow signals are fully removed from the codebase.

## 8. Folder Structure
- [ ] Panel/layout manager files (`(x)_panel_layout.py`) are located within their respective component folders unless their scope justifies otherwise.
- [ ] The `ui/` folder structure matches the plan, and presenters remain outside `ui/`.

## 9. Final Review
- [ ] A final code review confirms all above points, with evidence for each item.
