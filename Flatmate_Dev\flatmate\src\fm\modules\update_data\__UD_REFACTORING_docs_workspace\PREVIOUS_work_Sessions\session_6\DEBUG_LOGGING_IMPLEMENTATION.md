# Debug Logging Implementation - File Display Tracing
**Date**: 2025-07-31  
**Context**: Adding comprehensive debug logging to trace file display process  
**Status**: ✅ **IMPLEMENTED** - Debug logging throughout file display chain  

## 🎯 **Objective**

Add detailed debug logging throughout the file display process to identify why files are not appearing in the file pane, with module-level debug configuration.

## 🔧 **Implementation Details**

### **1. Module-Level Debug Configuration**

#### **Added Debug Logging Key**
**Files Modified**:
- `config/ud_keys.py` - Added `DEBUG_LEVEL` key
- `config/defaults.yaml` - Added `debug_level: true`

```python
# ud_keys.py
class Logging(str, Enum):
    DEBUG_LEVEL = 'update_data.logging.debug_level'

# defaults.yaml
logging:
  debug_level: true
```

#### **Debug Logging Setup**
**File**: `config/ud_config.py`  
**Added**: `_setup_debug_logging()` method

```python
def _setup_debug_logging(self):
    """Set up debug logging for Update Data module if enabled."""
    import logging
    
    debug_enabled = self.get_value(UpdateDataKeys.Logging.DEBUG_LEVEL, default=False)
    if debug_enabled:
        # Set debug level for update_data module loggers
        update_data_logger = logging.getLogger('fm.modules.update_data')
        update_data_logger.setLevel(logging.DEBUG)
        
        # Also set for specific components
        for component in ['_presenter', '_view', 'services', 'config']:
            component_logger = logging.getLogger(f'fm.modules.update_data.{component}')
            component_logger.setLevel(logging.DEBUG)
```

### **2. File Display Chain Debug Logging**

#### **FileManager Debug Logging**
**File**: `_presenter/file_manager.py`  
**Methods**: `_select_files()`, `_select_folder()`

```python
# File Selection Logging
log.debug(f"[FILE_MANAGER] Opening file dialog with last_dir: {last_dir}")
log.debug(f"[FILE_MANAGER] Dialog returned file_paths: {file_paths}")
log.debug(f"[FILE_MANAGER] Processing {len(file_paths)} selected files")
log.debug(f"[FILE_MANAGER] Emitting FILE_DISPLAY_UPDATED event: files={len(file_paths)}")

# Folder Selection Logging  
log.debug(f"[FILE_MANAGER] Discovering files in folder...")
log.debug(f"[FILE_MANAGER] Discovered {len(discovered_files)} files: {discovered_files}")
log.debug(f"[FILE_MANAGER] Emitting FILE_DISPLAY_UPDATED event: files={len(discovered_files)}")
```

#### **UpdateDataView Debug Logging**
**File**: `ud_view.py`  
**Method**: `update_files_display()`

```python
log.debug(f"[UD_VIEW] Received FILE_DISPLAY_UPDATED event: {type(files_data)}")
log.debug(f"[UD_VIEW] Processing dataclass format - files: {len(files_data.files)}")
log.debug(f"[UD_VIEW] Called center_display.set_files() with dataclass data")
```

#### **CenterPanelManager Debug Logging**
**File**: `_view/center_panel.py`  
**Method**: `set_files()`

```python
log.debug(f"[CENTER_PANEL] Received set_files call - files: {len(files)}, source_dir: {source_dir}")
log.debug(f"[CENTER_PANEL] Files list: {files}")
log.debug(f"[CENTER_PANEL] Calling file_pane.set_files()")
log.debug(f"[CENTER_PANEL] file_pane.set_files() completed")
```

#### **FilePane Debug Logging**
**File**: `_view/center_panel_components/file_pane.py`  
**Method**: `set_files()`

```python
log.debug(f"[FILE_PANE] Received set_files call - files: {len(files)}, source_dir: {source_dir}")
log.debug(f"[FILE_PANE] Files list: {files}")
log.debug(f"[FILE_PANE] Calling file_browser.set_files()")
log.debug(f"[FILE_PANE] file_browser.set_files() completed")
```

#### **FileBrowser Debug Logging**
**File**: `_view/center_panel_components/widgets/file_browser.py`  
**Method**: `set_files()`

```python
log.debug(f"[FILE_BROWSER] Received set_files call - files: {len(files)}, source_dir: {source_dir}")
log.debug(f"[FILE_BROWSER] Files list: {files}")
log.debug(f"[FILE_BROWSER] Processing files to full paths")
log.debug(f"[FILE_BROWSER] Calling file_display.set_files() with {len(full_paths)} full paths")
log.debug(f"[FILE_BROWSER] file_display.set_files() completed")
```

## 🔍 **Debug Trace Flow**

### **Expected Log Sequence for File Selection**
```
[FILE_MANAGER] Opening file dialog with last_dir: /path/to/last/dir
[FILE_MANAGER] Dialog returned file_paths: ['/path/to/file1.csv', '/path/to/file2.csv']
[FILE_MANAGER] Processing 2 selected files
[FILE_MANAGER] Emitting FILE_DISPLAY_UPDATED event: files=2, source_path=/path/to
[FILE_MANAGER] Event emitted successfully

[UD_VIEW] Received FILE_DISPLAY_UPDATED event: <class 'FileDisplayUpdateEvent'>
[UD_VIEW] Processing dataclass format - files: 2, source_path: /path/to
[UD_VIEW] Called center_display.set_files() with dataclass data

[CENTER_PANEL] Received set_files call - files: 2, source_dir: /path/to
[CENTER_PANEL] Files list: ['/path/to/file1.csv', '/path/to/file2.csv']
[CENTER_PANEL] Calling file_pane.set_files()
[CENTER_PANEL] file_pane.set_files() completed

[FILE_PANE] Received set_files call - files: 2, source_dir: /path/to
[FILE_PANE] Files list: ['/path/to/file1.csv', '/path/to/file2.csv']
[FILE_PANE] Calling file_browser.set_files()
[FILE_PANE] file_browser.set_files() completed

[FILE_BROWSER] Received set_files call - files: 2, source_dir: /path/to
[FILE_BROWSER] Files list: ['/path/to/file1.csv', '/path/to/file2.csv']
[FILE_BROWSER] Processing files to full paths
[FILE_BROWSER] Calling file_display.set_files() with 2 full paths
[FILE_BROWSER] file_display.set_files() completed
```

### **Expected Log Sequence for Folder Selection**
```
[FILE_MANAGER] Opening folder dialog with last_dir: /path/to/last/dir
[FILE_MANAGER] Dialog returned folder_path: /path/to/csv/folder
[FILE_MANAGER] Processing selected folder: /path/to/csv/folder
[FILE_MANAGER] Discovering files in folder...
[FILE_MANAGER] Discovered 3 files: ['/path/to/csv/folder/file1.csv', ...]
[FILE_MANAGER] Emitting FILE_DISPLAY_UPDATED event: files=3, source_path=/path/to/csv/folder
[FILE_MANAGER] FILE_DISPLAY_UPDATED event emitted successfully

[Similar chain as file selection continues...]
```

## 🧪 **Testing Instructions**

### **1. Enable Debug Logging**
Debug logging is enabled by default in `defaults.yaml`:
```yaml
logging:
  debug_level: true
```

### **2. Test File Selection**
1. Run the Update Data module
2. Click "Select individual files..."
3. Choose CSV files
4. Monitor console for debug messages

### **3. Test Folder Selection**
1. Click "Select entire folder..."
2. Choose folder containing CSV files
3. Monitor console for debug messages

### **4. Identify Break Points**
Look for where the debug trace stops:
- **Event emission failure**: No `[FILE_MANAGER] Event emitted successfully`
- **Event reception failure**: No `[UD_VIEW] Received FILE_DISPLAY_UPDATED event`
- **Data processing failure**: Event received but no subsequent calls
- **UI update failure**: All calls made but no visual update

## 🔧 **Debugging Scenarios**

### **Scenario 1: Event Not Emitted**
**Symptoms**: No `[FILE_MANAGER]` logs after file selection  
**Possible Causes**: Dialog returns empty, exception in file processing

### **Scenario 2: Event Not Received**
**Symptoms**: `[FILE_MANAGER]` logs but no `[UD_VIEW]` logs  
**Possible Causes**: Event bus connection issue, event name mismatch

### **Scenario 3: Event Received But Not Processed**
**Symptoms**: `[UD_VIEW]` logs but no `[CENTER_PANEL]` logs  
**Possible Causes**: Data format issue, center_display not available

### **Scenario 4: Processing Chain Breaks**
**Symptoms**: Logs stop at specific component  
**Possible Causes**: Method not found, parameter mismatch, exception

### **Scenario 5: UI Not Updating**
**Symptoms**: All logs present but no visual update  
**Possible Causes**: Widget not visible, tree not refreshing, data format issue

## 💡 **Key Debug Points**

### **Data Format Verification**
- **File paths**: Should be absolute paths (strings)
- **Event data**: Should be FileDisplayUpdateEvent dataclass
- **Source path**: Should be directory path (string)

### **Component Availability**
- **center_display**: Should exist on UpdateDataView
- **file_pane**: Should exist on CenterPanelManager
- **file_browser**: Should exist on FilePane
- **file_display**: Should exist on FileBrowser

### **Event Flow Verification**
- **Event emission**: `local_bus.emit()` called successfully
- **Event reception**: Handler method called
- **Data transformation**: Correct format at each step

---

>> **Status**: Comprehensive debug logging implemented throughout the file display chain. Run the module and monitor console output to identify exactly where the file display process is failing.
