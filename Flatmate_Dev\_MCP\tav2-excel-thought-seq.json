{"mcpServers": {"TAV2": {"command": "npx", "args": ["-y", "tavily-mcp@0.1.3"], "env": {"TAVILY_API_KEY": "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"}}, "think": {"command": "mcp-think-tool", "args": [], "type": "stdio", "pollingInterval": 30000, "startupTimeout": 30000, "restartOnFailure": true}, "excel": {"command": "uvx", "args": ["excel-mcp-server", "stdio"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "env": {}, "disabled": true}}}