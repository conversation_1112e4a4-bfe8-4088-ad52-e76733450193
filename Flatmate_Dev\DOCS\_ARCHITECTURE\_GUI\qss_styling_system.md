# Flatmate GUI Styling System (Qt QSS) — Architecture & Behavior

Status: Authoritative reference for how styles are applied in the app today and how to make them deterministic.

## Executive Summary
- The application applies a global QSS at startup from the main QApplication init.
- QPushButton style matching relies on dynamic properties (e.g., setProperty("type", "...")) and QSS attribute selectors (e.g., QPushButton[type="secondary"]).
- CSS custom properties var(--...) are NOT supported by stock Qt’s stylesheet engine (QSS). Any QSS variables must be resolved to literals before application.
- “Select” buttons are styled as secondary buttons. Their colors come from secondary palette rules that are actually present with literal color values and a selector that matches the button’s dynamic type.

---

## System Overview (High-Level)

```mermaid
flowchart TD
    A["App Startup (QApplication)"]
    B["Load QSS (theme/palette/combined)"]
    C["Apply Global Stylesheet on QApplication"]
    D["Widget Creation: QPushButton, QLabel, etc."]
    E["Widgets set dynamic properties (e.g. type='select_btn')"]
    F["QSS Selector Matching"]
    G["Visual Styling Rendered (colors, borders, radii)"]

    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
```

Key: Styles apply top-down. Widgets inherit the global QSS unless locally overridden.

---

## Selector Mechanics

```mermaid
sequenceDiagram
    participant App as "QApplication"
    participant Root as "Root Window"
    participant BTN as "QPushButton('Select')"

    Note over App: setStyleSheet(global_qss)
    Root->>BTN: create child button
    BTN->>BTN: setProperty("type", "select_btn")
    App-->>BTN: QSS evaluates selectors
    Note over BTN: "QPushButton[type='select_btn'] matches"
    BTN-->>BTN: "Apply background, color, border, radius"
```

Selector truth table (QSS):
- QWidget#objectName — matches by objectName
- QPushButton[type="secondary"] — matches if dynamic property type == "secondary"
- QPushButton:hover / :pressed — pseudo states supported

Important:
- Dynamic property must be set BEFORE stylesheet application for immediate effect; or call style().polish(widget) / reset stylesheet after setting property.

---

## Palette and Variables

```mermaid
flowchart LR
    P["Palette Source"] -->|Python dict or .qss literals| R["Resolved QSS"]
    V["var(--color-...)"] -. "UNSUPPORTED" .-x R
```

- QSS does not implement CSS custom properties var(--...).
- If the stylesheet contains var(--...), those declarations won’t resolve. They either get ignored or fail to set a value.
- To use “variables”, introduce a preprocessing step (Python) that replaces tokens with literals before calling setStyleSheet.

---

## Why “Select” Appears Secondary

```mermaid
flowchart TD
    S["Select Button"] -->|setProperty('type','select_btn')| M["Matching Selector Exists"]
    M -->|"QPushButton[type='select_btn'] or mapped to secondary"| L["Literals (#hex)"]
    L --> VZ["Visible Secondary Colors"]
```

- In code: [flatmate/src/fm/gui/_shared_components/widgets/option_menus.py](flatmate/src/fm/gui/_shared_components/widgets/option_menus.py:115)
  - self.button.setProperty("type", "select_btn")
- In active stylesheet: there is a rule that either:
  - Directly targets type="select_btn" with literal colors for the secondary scheme; or
  - Maps select_btn to secondary variant via another layer you’re applying at runtime.
- Result: “Select” renders with the secondary palette.

---

## Current Mismatches Noted

- theme.qss shows selectors for primary/secondary using var(--...) (e.g., QPushButton[type="secondary"] with var(--color-secondary)).
- Buttons in code set type="action_btn", "select_btn", "exit_btn".
- Therefore, if theme.qss alone were applied, these selectors wouldn’t match those types AND the var(--...) would not resolve. Colors seen in the app are coming from either:
  - Another stylesheet or a pre-resolved form with literals, and/or
  - Additional QSS rules that do match “select_btn” and specify literal colors.

---

## Minimal Working Standard

1) Single Source of Truth for Palette
   - Define a Python dict:
     - e.g., PALETTE = {"color_secondary": "#3B7443", ...}

2) Preprocess QSS (if you want to keep QSS files)
   - Read theme.qss as text.
   - Replace var(--color-secondary) with literal "#3B7443" using a regex/tokenizer driven by PALETTE.
   - Apply the resolved string via QApplication.setStyleSheet(resolved_qss).

3) Align Selectors with Code
   - Either change code to:
     - setProperty("type","secondary") and “primary” where appropriate
   - OR change stylesheet to:
     - QPushButton[type="select_btn"] { background-color: #3B7443; ... }
     - QPushButton[type="action_btn"] { ... }
     - QPushButton[type="exit_btn"] { ... }

4) Application Order
   - Ensure properties are set before final stylesheet application to avoid timing mismatch.
   - If properties change at runtime, re-polish the widget or re-apply relevant stylesheet scope.

---

## Reference Snippet: Python-Side Resolver (Example)

```python
# pseudo-code integrated at app init
from pathlib import Path
import re
from PySide6.QtWidgets import QApplication

PALETTE = {
    "color_secondary": "#3B7443",
    "color_secondary_hover": "#4D8C55",
    "color_secondary_pressed": "#35673C",
    "color_text_primary": "#FFFFFF",
    # ... add more
}

VAR_RE = re.compile(r"var\(--([A-Za-z0-9_-]+)\)")

def resolve_qss_vars(qss_text: str) -> str:
    def repl(m):
        key = m.group(1)
        # map --color-secondary => PALETTE["color_secondary"]
        return PALETTE.get(key, "")
    return VAR_RE.sub(repl, qss_text)

qss_path = Path("flatmate/src/fm/gui/styles/theme.qss")
qss_text = qss_path.read_text(encoding="utf-8")
resolved = resolve_qss_vars(qss_text)

app = QApplication.instance() or QApplication([])
app.setStyleSheet(resolved)
```

---

## Verification Protocol

```mermaid
flowchart TD
    T1["Unit Harness (tests/ui/test_qss_vars.py)"] --> T2["Confirm selectors: primary/secondary vs select_btn/action_btn"]
    T2 --> T3["Confirm hover/pressed states"]
    T3 --> T4["Confirm no var(--...) remains in applied QSS"]
```

Expected:
- Literal-color rules apply.
- Select button shows secondary colors; hover/pressed states work.

---

## Migration Checklist

- [ ] Decide: keep “primary/secondary” or keep “action_btn/select_btn/exit_btn”
- [ ] Update stylesheet selectors accordingly
- [ ] Implement a QSS preprocessor or convert QSS vars to literals
- [ ] Apply stylesheet at app startup after properties are known (or re-polish)
- [ ] Commit tests/ui/test_qss_vars.py as an ongoing guard

---

## Conclusion

- The system works because dynamic properties + matching selectors + literal color rules are in place in the runtime-applied stylesheet.
- QSS var(--...) is unsupported; resolve to literals ahead of time.
- Align naming between widget properties and QSS selectors to keep styling deterministic and maintainable.