# Guide Pane Presenter Integration — Change Log

Date: 2025-08-09
Time: 17:45 NZT
Status: Applied

## Summary
- Integrated `GuidePanePresenter` into the Update Data module to centralise guide pane messaging and UI state.
- Refactored `StateManager` to be presenter-driven and Qt-free for guide updates.
- Added explicit state setters to ensure clear, controlled mutation and derived state recomputation.

## Files Modified
- `src/fm/modules/update_data/_ui/_presenter/state_coordinator.py`
  - Injected `guide_presenter` into `StateManager`.
  - Removed direct widget access to guide pane.
  - Added setters: `set_source_files(...)`, `set_source_folder(...)`, `set_destination(...)`, `set_update_database(...)`, `set_processing(...)`.
  - Guide updates now go via presenter methods: `on_files_listed(...)`, `on_discovery_toggled(...)`.
- `src/fm/modules/update_data/ud_presenter.py`
  - Passed `self.view.guide_presenter` into `StateManager` on setup.

## Rationale (MVP & Interfaces)
- Presenter-view communication uses interface methods; avoid event bus duplication for synchronous UI updates.
- `StateManager` owns state; `GuidePanePresenter` owns all guide copy/state. Zero Qt coupling in both.

## Behavioural Impact
- Guide pane messages and toggles are consistent and sourced from a single presenter.
- Process enablement and button text computed via `StateManager.update_can_process()`.
- Direct Qt widget manipulation for guide pane removed from state layer.

## Verification Steps
1. Launch Update Data module.
2. Select files → guide pane shows count and source type via presenter.
3. Select a folder → guide pane shows folder summary and auto-queue toggle state.
4. Toggle update database / save option → guide archive summary remains consistent.
5. Start processing → controls disabled and presenter receives `on_processing_started`.
6. Complete processing → controls re-enabled; presenter receives `on_processing_completed`.

## Next Actions
- Ensure processing lifecycle callers also invoke:
  - `state_manager.set_processing(True/False)` and `state_manager.sync_state_to_view()`
  - Presenter calls: `on_processing_started(...)` / `on_processing_completed(...)`
- Add tests for `StateManager` setters and presenter-driven updates.

## References
- `Guide_Pane_Presenter_Design.md`
- `src/fm/modules/update_data/_ui/_presenter/guide_pane_presenter.py`
- `src/fm/modules/update_data/_ui/interface/i_guide_pane_view.py`

