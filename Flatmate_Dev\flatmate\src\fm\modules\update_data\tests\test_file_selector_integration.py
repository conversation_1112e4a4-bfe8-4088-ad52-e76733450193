"""
Tests for file selector integration in Update Data module.

This test suite verifies:
1. FileConfigManager correctly uses shared get_file_paths() function
2. UDFileView correctly uses shared get_file_paths() function  
3. Both components handle file selection consistently
4. Error handling and edge cases work properly
"""

import os
import unittest
from unittest.mock import MagicMock, patch, Mock
from pathlib import Path

from fm.core.services.logger import log
from fm.modules.update_data._ui._presenter.file_config_manager import FileConfigManager
from fm.modules.update_data._ui._view.center_panel_components.ud_file_view import UDFileView


class TestFileConfigManagerIntegration(unittest.TestCase):
    """Test FileConfigManager integration with shared file selector."""

    def setUp(self):
        """Set up test environment."""
        # Mock dependencies
        self.mock_view = MagicMock()
        self.mock_view.get_main_window.return_value = MagicMock()
        
        self.mock_state_manager = MagicMock()
        self.mock_state = MagicMock()
        self.mock_state_manager.state = self.mock_state
        
        self.mock_file_list_manager = MagicMock()
        self.mock_local_bus = MagicMock()
        self.mock_info_bar_service = MagicMock()
        
        # Create FileConfigManager instance
        self.file_manager = FileConfigManager(
            view=self.mock_view,
            state_manager=self.mock_state_manager,
            file_list_manager=self.mock_file_list_manager,
            local_bus=self.mock_local_bus
        )

    @patch('fm.modules.update_data._ui._presenter.file_management.FileSelector.get_file_paths')
    @patch('fm.modules.update_data._ui._presenter.file_management.ud_config')
    def test_select_files_success(self, mock_config, mock_get_file_paths):
        """Test successful file selection."""
        # Setup
        test_files = ['/path/to/file1.csv', '/path/to/file2.csv']
        mock_get_file_paths.return_value = test_files
        mock_config.get_value.return_value = '/home/<USER>'
        
        # Mock file enrichment
        enriched_files = [{'path': f, 'size': 1000} for f in test_files]
        self.file_manager.enrich_file_info = MagicMock(return_value=enriched_files)
        
        # Execute
        self.file_manager._select_files()
        
        # Verify get_file_paths was called correctly
        mock_get_file_paths.assert_called_once_with(
            method='select_files',
            title="Select CSV Files to Process",
            parent=self.mock_view.get_main_window(),
            start_dir='/home/<USER>'
        )
        
        # Verify state updates
        self.assertEqual(self.file_manager.selected_source, test_files)
        self.assertEqual(self.mock_state.source_type, 'files')
        self.assertEqual(self.mock_state.selected_files, test_files)
        
        # Verify file list manager was called
        self.mock_file_list_manager.set_files.assert_called_once_with(test_files)
        
        # Verify view was updated
        self.mock_view.display_enriched_file_info.assert_called_once_with(enriched_files)
        
        # Verify event was emitted
        self.mock_local_bus.emit.assert_called_once()

    @patch('fm.modules.update_data._ui._presenter.file_management.FileSelector.get_file_paths')
    @patch('fm.modules.update_data._ui._presenter.file_management.ud_config')
    def test_select_files_empty_selection(self, mock_config, mock_get_file_paths):
        """Test file selection with empty result."""
        # Setup
        mock_get_file_paths.return_value = []
        mock_config.get_value.return_value = '/home/<USER>'
        
        # Execute
        self.file_manager._select_files()
        
        # Verify no state changes occurred
        self.mock_file_list_manager.set_files.assert_not_called()
        self.mock_view.display_enriched_file_info.assert_not_called()
        self.mock_local_bus.emit.assert_not_called()

    @patch('fm.modules.update_data._ui._presenter.file_management.FileSelector.get_file_paths')
    @patch('fm.modules.update_data._ui._presenter.file_management.ud_config')
    def test_select_folder_success(self, mock_config, mock_get_file_paths):
        """Test successful folder selection."""
        # Setup
        test_files = ['/path/to/folder/file1.csv', '/path/to/folder/file2.csv']
        mock_get_file_paths.return_value = test_files
        mock_config.get_value.return_value = '/home/<USER>'
        
        # Mock file enrichment
        enriched_files = [{'path': f, 'size': 1000} for f in test_files]
        self.file_manager.enrich_file_info = MagicMock(return_value=enriched_files)
        
        # Execute
        self.file_manager._select_folder()
        
        # Verify get_file_paths was called correctly
        mock_get_file_paths.assert_called_once_with(
            method='select_folder',
            title="Select Folder Containing CSV Files",
            parent=self.mock_view.get_main_window(),
            start_dir='/home/<USER>'
        )
        
        # Verify state updates
        expected_folder = '/path/to/folder'
        self.assertEqual(self.file_manager.selected_source, expected_folder)
        self.assertEqual(self.mock_state.source_type, 'folder')
        self.assertEqual(self.mock_state.selected_folder, expected_folder)
        
        # Verify file list manager was called
        self.mock_file_list_manager.set_files.assert_called_once_with(test_files, expected_folder)

    @patch('fm.modules.update_data._ui._presenter.file_management.FileSelector.get_file_paths')
    def test_select_files_error_handling(self, mock_get_file_paths):
        """Test error handling in file selection."""
        # Setup
        mock_get_file_paths.side_effect = Exception("Dialog error")
        
        # Execute
        self.file_manager._select_files()
        
        # Verify error was handled
        self.mock_info_bar_service.show_error.assert_called_once()


class TestUDFileViewIntegration(unittest.TestCase):
    """Test UDFileView integration with shared file selector."""

    def setUp(self):
        """Set up test environment."""
        # Mock the parent widget and config
        self.mock_parent = MagicMock()
        self.mock_config = MagicMock()
        self.mock_config.allowed_file_types = ['*.csv', '*.txt']
        
        # Create a minimal UDFileView instance for testing
        with patch('fm.modules.update_data._ui._view.center_panel_components.file_pane_v2.ud_file_view.FileConfig'):
            with patch('fm.modules.update_data._ui._view.center_panel_components.file_pane_v2.ud_file_view.FileViewModel'):
                self.file_view = UDFileView(parent=self.mock_parent)
                self.file_view._config = self.mock_config

    @patch('fm.modules.update_data._ui._view.center_panel_components.file_pane_v2.ud_file_view.FileSelector.get_file_paths')
    def test_add_files_success(self, mock_get_file_paths):
        """Test successful file addition."""
        # Setup
        test_files = ['/path/to/file1.csv', '/path/to/file2.csv']
        mock_get_file_paths.return_value = test_files
        
        # Mock the add_file method
        self.file_view.add_file = MagicMock(return_value=self.file_view)
        
        # Execute
        self.file_view._on_add_files_requested()
        
        # Verify get_file_paths was called correctly
        mock_get_file_paths.assert_called_once_with(
            method='select_files',
            title="Select Files to Add",
            parent=self.file_view,
            start_dir=None
        )
        
        # Verify add_file was called for each file
        self.assertEqual(self.file_view.add_file.call_count, 2)
        self.file_view.add_file.assert_any_call('/path/to/file1.csv')
        self.file_view.add_file.assert_any_call('/path/to/file2.csv')

    @patch('fm.modules.update_data._ui._view.center_panel_components.file_pane_v2.ud_file_view.FileSelector.get_file_paths')
    def test_add_files_empty_selection(self, mock_get_file_paths):
        """Test file addition with empty selection."""
        # Setup
        mock_get_file_paths.return_value = []
        
        # Mock the add_file method
        self.file_view.add_file = MagicMock()
        
        # Execute
        self.file_view._on_add_files_requested()
        
        # Verify no files were added
        self.file_view.add_file.assert_not_called()

    @patch('fm.modules.update_data._ui._view.center_panel_components.file_pane_v2.ud_file_view.FileSelector.get_file_paths')
    @patch('fm.modules.update_data._ui._view.center_panel_components.file_pane_v2.ud_file_view.log')
    def test_add_files_error_handling(self, mock_log, mock_get_file_paths):
        """Test error handling in file addition."""
        # Setup
        mock_get_file_paths.side_effect = Exception("Dialog error")
        
        # Execute
        self.file_view._on_add_files_requested()
        
        # Verify error was logged
        mock_log.error.assert_called_once()


if __name__ == '__main__':
    unittest.main()
