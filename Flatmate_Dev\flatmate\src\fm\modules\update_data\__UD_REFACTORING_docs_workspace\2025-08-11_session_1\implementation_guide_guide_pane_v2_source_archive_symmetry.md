---
description: Implementation guide – Guide Pane V2 symmetry (Source/Archive), per‑folder options bound to path slot
---

# Implementation Guide: Guide Pane V2 – symmetric Source/Archive with per‑folder option binding

This guide implements the agreed changes: Source gets a dedicated path slot (like Archive), info lines are for non‑path text only, and the per‑folder option (Enable file discovery) is strictly bound to the Source path slot.

## Files to edit
- `src/fm/modules/update_data/_ui/_view/center_panel_components/guide_pane_components/source_section.py`
- `src/fm/modules/update_data/_ui/_view/center_panel_components/guide_pane_v2.py`
- Optional: `src/fm/modules/update_data/_ui/_view/center_panel_components/guide_pane_components/base_components/folder_path_row.py`

## 1) Add a Source path slot (symmetry)
In `source_section.py`:
- Add a single‑line path row, mirroring `ArchiveSection`:
  - Fields:
    - `self._path_label = QLabel("")` with `setWordWrap(False)`
    - `self._path_row = SlotRow()`; call `self._path_row.set_content(self._path_label)`
    - `self._path_row.set_horizontal_padding(8)`; start hidden: `self._path_row.set_visible(False)`
    - `self.add_to_body(self._path_row)`
  - Methods:
    - `def set_path_html(self, html: str, tooltip: str = "") -> None:`
      - set label text + tooltip
      - `self._path_row.set_visible(bool(html))`
    - `def clear_path(self) -> None:`
      - clear text + tooltip
      - hide row
    - `def show_path(self, visible: bool) -> None:`
      - passthrough to `self._path_row.set_visible(visible)`
- Keep existing slots/APIs: `message` row, `options` row (checkbox), `show_enable_option(...)`, `set_enable_checked(...)`, `connect_enable_toggled(...)`.

## 2) Tighten root spacing
In `guide_pane_v2.py` → `_build_ui()`:
- Change `root.setSpacing(6)` to `root.setSpacing(2)` (or `0` if you want absolutely flush).

## 3) Source path rendering rules
In `guide_pane_v2.py` → `set_section_info(section_id: str, text: str, level: str = "info")`:
- When `section_id == "source"`:
  - Extract candidate: `candidate = self._extract_path_candidate(text)`
  - If `_looks_like_path(candidate)`:
    - `self._source_path = candidate`
    - `html, tip = self._format_path_for_label(candidate)`
    - `self.section_source.set_path_html(html, tip)`
    - `self.section_source.clear_info()`
    - `self.section_source.show_message(False)`
    - Show options row: `self.section_source.show_enable_option(True)`
    - Update binding context: `self._current_folder = self._source_path`
    - If Archive is “Same as Source”, update archive path slot accordingly (reuse existing logic)
  - Else (non‑path text):
    - `self._source_path = None`; `self._current_folder = None`
    - `self.section_source.clear_path()`
    - If `text`:
      - `self.section_source.set_info(text)`; `self.section_source.show_message(False)`; `self.section_source.show_enable_option(False)`
    - Else (empty):
      - `self.section_source.clear_info()`; `self.section_source.show_message(True)`; `self.section_source.show_enable_option(False)`
- Nudge geometry if needed, but do not swallow errors (remove broad try/except).

## 4) Archive remains path‑slot first
- Confirm `set_archive_summary(text)` routes:
  - Path‑like → `ArchiveSection.set_path_html(html, tip)` and clears `info`
  - "Same as Source" with known source → mirror Source path to archive path slot, info blank
  - Non‑path text → `ArchiveSection.clear_path()` and `set_summary(text)`

## 5) Bind per‑folder option strictly to Source path slot
In `guide_pane_v2.py` → `show_source_context_options(monitor_enabled=False, auto_queue_enabled=False, folder: Optional[str] = None)`:
- Ignore external `folder` parameter; derive binding from `self._source_path` only.
- If `self._source_path`:
  - `self._current_folder = self._source_path`
  - `self.section_source.show_enable_option(True)`
  - Block signals, set checkbox state: `self.section_source.set_enable_checked(auto_queue_enabled)`
- Else:
  - `self.section_source.show_enable_option(False)`; leave checkbox state unchanged; no emissions possible.

In `_on_src_enable_toggled(self, checked: bool)`:
- If `self._current_folder`: emit `publish_toggle_auto_queue_requested(self._current_folder, bool(checked))`
- Else: no‑op. Do not wrap with broad try/except.

## 6) Optional: adopt FolderPathRow
If you prefer a standardised row component:
- Replace each section’s `_path_label` + `SlotRow` with a `FolderPathRow` instance.
- Inject formatter from the pane: `folder_row.set_formatter(self._format_path_for_label)` (or pass into section via constructor if you want zero back‑refs).
- Use `folder_row.set_path(path)` instead of `set_path_html(...)`.

## 7) Presenter contract (unchanged)
- Presenter remains Qt‑free and calls the same methods:
  - `set_section_info("source", text)`
  - `set_archive_summary(text)`
  - `show_source_context_options(monitor_enabled, auto_queue_enabled, folder=None)` (folder is ignored by view; binding derives from current source path)
- Do not read UI labels for state. Treat the view as authoritative for rendering.

## 8) Acceptance criteria
- Empty Source → placeholder visible; info collapsed; options hidden; zero layout gaps.
- Source path set → Source path slot visible (formatted with `~`, tooltip absolute path); info blank; placeholder hidden; options visible and emits with that path.
- Non‑path text set for Source → info shows text; path slot hidden; placeholder hidden; options hidden.
- Archive mirrors Source when "Same as Source"; otherwise follows its own path/summary rules.
- Inter‑section spacing is tight (0/2); no stray vertical gaps.
- No broad try/except in layout code.

## 9) Manual test checklist
- Toggle Source between: empty → path → non‑path text; verify slot/info/placeholder/option visibility each time.
- With Source path present, toggle the checkbox and confirm emitted `(folder, enabled)` matches the current Source path.
- Switch Source to a different folder; verify binding updates and emissions carry the new path.
- Archive set to "Same as Source" mirrors the current Source path; switching Source updates Archive accordingly.
- Context menu “Open in Explorer” still works for both Source and Archive path labels.

## Notes
- Logging: use `from fm.core.services.logger import log` for targeted diagnostics only; otherwise fail fast.
- UK spelling; avoid speculative clutter; keep code explicit and readable.

## Task Checklist (checked against current codebase)

- [DONE] SourceSection: add a dedicated path slot
  - Implemented: `_path_row` + `_path_label` with `set_path_html()/clear_path()/show_path()`.
- [DONE] GuidePaneV2: route Source paths to SourceSection.path slot
  - Implemented in `set_section_info("source", ...)`: uses `section_source.set_path_html(...)`; info line used only for non‑path text.
- [DONE] GuidePaneV2: fix API usage to call section methods (not raw widget attrs)
  - Implemented: removed `src_enable_container/src_enable_checkbox` references; now use `section_source.show_enable_option()` and `section_source.set_enable_checked()`.
- [DONE] Bind per‑folder option to active Source path
  - Implemented: `show_source_context_options(...)` derives from `self._source_path` and hides row when absent; `_current_folder` bound to source path.
- [DONE] Tighten inter‑section spacing
  - Implemented: `_build_ui()` now `root.setSpacing(2)`.
- [PARTIAL] Fail‑fast: remove broad try/except around geometry nudges
  - Implemented in `set_section_info(...)` and `show_source_context_options(...)` (direct calls to `adjustSize()/updateGeometry()`). Remaining: `set_archive_mode(...)` still wraps geometry in try/except.
- [DONE] Archive path slot semantics present
  - `archive_section.py` has `_path_row` + `_path_label` with `set_path_html()`/`clear_path()`.
- [DONE] Context menu on Source/Archive info labels
  - Implemented in `guide_pane_v2.py`.

Re‑run the Manual test checklist above.
