from __future__ import annotations

from dataclasses import replace
from datetime import datetime
from pathlib import Path
from typing import Dict, Iterable, Optional

from fm.core.directory.models.directory_info import DirectoryInfo


class DirectoryInfoService:
    """In-memory registry for DirectoryInfo with simple persistence hooks.

    MVP: keep it simple, explicit, and readable. No hidden IO.
    """

    def __init__(self) -> None:
        self._by_path: Dict[Path, DirectoryInfo] = {}

    # Interface / API methods (discoverable, extendable)
    def upsert(self, info: DirectoryInfo) -> DirectoryInfo:
        info_n = info.normalised()
        self._by_path[info_n.path] = info_n
        return info_n

    def get(self, path: Path) -> Optional[DirectoryInfo]:
        p = path.resolve()
        return self._by_path.get(p)

    def all(self) -> Iterable[DirectoryInfo]:
        return list(self._by_path.values())

    def enable_discovery(self, path: Path, archive_dest: Optional[Path] = None) -> DirectoryInfo:
        existing = self.get(path)
        if existing is None:
            if archive_dest is None:
                raise ValueError("archive_dest required when enabling discovery for new directory")
            info = DirectoryInfo(path=path, role="source", discovery_enabled=True, archive_dest=archive_dest)
            return self.upsert(info)
        updated = replace(existing, discovery_enabled=True, archive_dest=archive_dest or existing.archive_dest)
        return self.upsert(updated)

    def disable_discovery(self, path: Path) -> Optional[DirectoryInfo]:
        existing = self.get(path)
        if existing is None:
            return None
        updated = replace(existing, discovery_enabled=False)
        return self.upsert(updated)

    def mark_scanned_now(self, path: Path) -> Optional[DirectoryInfo]:
        existing = self.get(path)
        if existing is None:
            return None
        updated = replace(existing, last_scan=datetime.now())
        return self.upsert(updated)

    # Persistence hooks (no-ops for MVP; add concrete backing later)
    def load_from_config(self) -> None:
        pass

    def save_to_config(self) -> None:
        pass
