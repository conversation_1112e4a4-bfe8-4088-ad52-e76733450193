# Guide Pane Refactor — Findings, Answers, and Proposals (2025-08-10)

Author: Cascade

This document summarises the code-first review of the Update Data Guide Pane refactor and responds to your questions directly. It follows our fail-fast policy, UK spelling, and no speculative clutter.

---

## 1) Current Status Snapshot (code references)

- __Startup fix__: `_connect_center_pane_intents()` now exists and is called in `UpdateDataView.setup_ui()`.
  - File: `update_data/_ui/ud_view.py`
  - Symbols: `UpdateDataView.setup_ui()` → `self._connect_center_pane_intents()`; method at lines ~153–162
- __Centre layout__: `GuidePaneWidget` sits above `UDFileView` in a vertical splitter. File view expands.
  - File: `update_data/_ui/_view/center_panel_layout.py`
  - Symbols: `CenterPanelManager._create_panes()` creates `guide_pane.GuidePaneWidget()` then `UDFileView()`
- __Guide Pane (current)__: State-driven messages, archive summary line, single “auto‑queue” toggle with folder-aware payload.
  - File: `update_data/_ui/_view/center_panel_components/guide_pane.py`
  - Signals: `publish_toggle_auto_queue_requested(str, bool)`
- __Guide Pane V2 (sandbox)__: Minimal, review-only widget. Always shows Source + Archive sections, optional info lines, default Source empty message.
  - File: `update_data/_ui/_view/center_panel_components/guide_pane_v2/guide_pane_v2.py`
  - Note: Not currently wired into runtime.
- __Enums unified__: Left-panel option payloads converted to `SourceOptions` / `SaveOptions` before emitting.
  - File: `update_data/models/config.py` (enum definitions)
  - File: `update_data/_ui/ud_view.py` (`_to_enum_or_label()`, `_to_save_enum_or_label()`)
- __Event wiring (left panel)__: `ud_view.py` translates left panel Qt signals → Local Event Bus events with canonical payloads. No broad try/except.

---

## 2) Direct Answers to Your Questions

- __Q1. Keep GuidePaneV2 temporarily with styling; add a switch?__
  - Yes. We’ll keep `GuidePaneV2` and add a small, runtime switch `test_guidepane_v2: bool` to select between `GuidePaneWidget` and `GuidePaneV2` in the centre panel. Styling parity will be preserved (see Proposal A).

- __Q2. “Interface‑first” for left panel — what is it and why mention it?__
  - Meaning: Presenter talks to the view via a clean interface (`IUpdateDataView`) and never directly to Qt signals. The view maps Qt signals to those interface methods internally. This keeps the presenter Qt‑free.
  - Scope: You’re right — we’re NOT changing left panel now. I mentioned it only to confirm the target architecture, not to imply immediate edits. Current code emits Local Event Bus events from the view; that’s acceptable for MVP.
  - “Event duplication”: The risk is having both interface methods and events carry the same user intent in parallel (two pathways). To be clear, in our current code we’re not duplicating that intent — we use events. If/when we migrate to interface methods for single‑listener flows, we’ll retire the equivalent events to avoid duplication.

- __Q3. Placeholder for empty state message?__
  - Agreed. `UDFileView` should show an italic/light “No files selected…” placeholder when empty. `GuidePaneV2` already displays a default Source message, but we should confirm/add the placeholder inside `UDFileView` itself (see Proposal B).

- __Q4. What is the startup blocker?__
  - Previous state: `UpdateDataView.setup_ui()` called a missing method `_connect_center_pane_intents()` → `AttributeError` at startup.
  - Current state: Fixed. `_connect_center_pane_intents()` exists and connects `UDFileView.add_files_requested` → `ViewEvents.ADD_FILES_REQUESTED`. Startup proceeds.
  - Files: `update_data/_ui/ud_view.py` (method and call present).

---

## 3) Observations (short)

- __Layout and behaviour__ meet MVP: Guide above File View, fit‑to‑content guide area, file view fills remaining space.
- __Messaging__: Guide Pane supports pane‑level messages, persistent archive summary, and an auto‑queue context option emitting `(folder, enabled)`.
- __Enums__: Left panel options normalised to enums, preventing drift from hard‑coded labels.
- __Sandbox__: `GuidePaneV2` is isolated — safe to keep short term; not in runtime path.

---

## 4) Proposals (minimal, concrete)

- __A. Runtime switch for Guide Pane V2 (styling preserved)__
  - Add a single boolean flag: `config/ud_config.py` → `TEST_GUIDEPANE_V2: bool = False`.
  - In `CenterPanelManager._create_panes()`, instantiate `GuidePaneV2` iff the flag is True; otherwise use `GuidePaneWidget`.
  - Styling parity: apply the same subtle green border styling to `GuidePaneV2` (copy of `GuidePaneWidget` frame stylesheet) OR wrap `GuidePaneV2` in a styled `QFrame` with identical stylesheet. No other visual changes.
  - Zero presenter impact: The view continues to interact only with `GuidePaneWidget` APIs today; with V2 active, this is strictly a visual review path (no presenter wiring).

- __B. File browser empty‑state placeholder__
  - Add a `QLabel` inside `UDFileView` (e.g., `empty_hint`) with italic, subtle styling: “No files selected…”.
  - Show when the internal list is empty; hide when files are present. No behavioural changes.

- __C. Documentation/hygiene__
  - Add a short README in `center_panel_components/guide_pane_v2/` stating: sandbox for design review; not wired; styling matches current; removal timeline post‑review.
  - Mark `publish_toggle_folder_monitoring_requested` (deprecated) for removal after MVP to avoid confusion; keep `publish_toggle_auto_queue_requested` as the single path.

- __D. Interface vs Events (clarification only — no change now)__
  - Keep current Local Event Bus emissions from `ud_view.py` for left panel intents for MVP.
  - If/when we migrate to interface‑first, we’ll replace those emissions with direct presenter callbacks via `IUpdateDataView` and delete the overlapping events to avoid duplication. This will not touch the left panel layout; only the view’s mapping layer.

---

## 5) Acceptance Criteria (for this phase)

- __Switch__: `test_guidepane_v2` toggles the top pane between `GuidePaneWidget` and `GuidePaneV2` with styling parity.
- __Empty state__: `UDFileView` shows a visible placeholder only when no files are present.
- __No coupling changes__: Presenter remains Qt‑free; no left panel refactor in this phase.
- __No regressions__: Startup remains stable; existing guide behaviour intact when V2 is off.

---

## 6) Implementation Plan (once approved)

1) Add `TEST_GUIDEPANE_V2` flag to `config/ud_config.py` (default False).
2) Update `CenterPanelManager._create_panes()` to select `GuidePaneWidget` vs `GuidePaneV2` based on the flag. Wrap V2 with the same border style or apply identical stylesheet to its root frame.
3) Add `empty_hint` label to `UDFileView` with italic styling and toggle it based on file count.
4) Add a one‑paragraph README in `guide_pane_v2/` explaining scope and styling parity.

---

## 7) Open Questions for You

- __Switch location__: Do you prefer the flag in `config/ud_config.py` or an env‑read at startup (e.g., `FM_TEST_GUIDEPANE_V2`)? Default will remain False.
- __Styling parity__: OK to apply the same stylesheet used by `GuidePaneWidget` to V2, or would you prefer wrapping V2 in a styled `QFrame` so we don’t touch V2 internals?
- __Empty‑state copy__: Confirm the exact copy for the file list placeholder (proposed: “No files selected…”). UK spelling/ellipsis?

---

## 8) Summary

- MVP structure is correct and stable; startup blocker was fixed and is no longer present.
- I propose a minimal runtime switch for `GuidePaneV2` with styling parity, and a lightweight empty‑state label in `UDFileView`.
- No left panel refactor now. “Interface‑first” was clarification of target architecture; not an immediate change.

Please add your inline comments and I’ll implement exactly what you approve — nothing else.
