# File Selection Dialogs Refactor & Architectural Report

**Date:** 2025-08-03
**Author:** <PERSON> (AI)

## Context

During the ongoing refactor of the Update Data module, critical UI bugs were discovered in the file and folder selection dialog logic. These issues were blocking the use of the update_data UI and were identified as P0 (critical) in triage. The root causes and the architectural implications of the fixes are documented here.

## Issues Encountered

### 1. Presenter Bypassing View Interface
- The presenter (`file_management.py`) called `self.view.get_main_window()` to obtain a parent window for dialogs.
- Neither the `IUpdateDataView` interface nor the `UpdateDataView` implementation provided this method or attribute.
- This approach violated the MVP pattern and tightly coupled the presenter to the Qt widget hierarchy.

### 2. Unused Interface Dialog Methods
- The `IUpdateDataView` interface correctly defined `show_folder_dialog()` and `show_files_dialog()` methods for dialog presentation.
- The `UpdateDataView` implemented these methods using `QFileDialog` as intended.
- However, the presenter did not use these interface methods, instead constructing dialogs itself and passing a non-existent main window reference.

### 3. File Discovery and Folder Path Handling
- The original presenter logic attempted to discover files in a selected folder by relying on the dialog's return value and path manipulation.
- The refactor required ensuring that the folder dialog returns a path and file discovery is performed via a utility method (`FileUtils.discover_files_in_folder`).

## Changes Made

1. **Presenter Refactor**
    - `_select_files` and `_select_folder` now call the view's `show_files_dialog` and `show_folder_dialog` methods, respectively.
    - All direct usage of `get_main_window()` was removed.
    - The folder dialog now saves the selected folder to config and discovers files using `FileUtils`.
    - Added missing `FileUtils` import to the presenter.

2. **Interface Compliance**
    - The presenter now fully respects the `IUpdateDataView` contract for dialog operations.
    - No direct Qt widget or window references are required in the presenter.
    - This enforces strict MVP separation and reduces future coupling issues.

3. **Error and Edge Case Handling**
    - If no supported files are found in the selected folder, a warning is shown to the user (using the info bar service).
    - Logging is performed using the custom `log` singleton as per project standards.

## Open Questions & Risks

- **Main Window Reference**: Is there any legitimate use case for the presenter to know about the main window (e.g., for advanced dialog parenting or modality)? If so, this should be handled via the view interface only.
- **Dialog Consistency**: Are there other places in the codebase where dialogs are invoked directly from presenters or non-view classes? If so, these should be migrated to the interface pattern for consistency.
- **Testing**: End-to-end UI testing is needed to confirm that dialog parenting, modality, and focus are correct across platforms.
- **Legacy Code**: Are there other legacy usages of `get_main_window()` or similar anti-patterns elsewhere in the update_data module or other modules?

## Summary Table

| Area                | Before                             | After (This Refactor)           |
|---------------------|------------------------------------|---------------------------------|
| Dialog Invocation   | Presenter called Qt methods directly| Presenter calls view interface  |
| MVP Separation      | Broken                             | Enforced                        |
| Coupling            | High (Qt in presenter)              | Low (no Qt in presenter)        |
| Config Handling     | Ad-hoc, sometimes missing           | Centralised, explicit           |
| Error Handling      | Minimal, sometimes silent           | Explicit, user-visible          |

## Next Steps
- Review all dialog and UI interaction points for interface compliance
- Confirm with team if any main window reference is required in the presenter
- Proceed with widget signal refactor and continue enforcing MVP boundaries

---

**This document should be updated if further architectural or implementation changes are made to dialog handling or presenter/view separation in the Update Data module.**
