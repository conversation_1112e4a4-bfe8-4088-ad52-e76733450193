---
description: Guide Pane v2 – IUpdateDataView guide_* API to widget mapping
---

# Guide Pane v2 – API Mapping (MVP, no switching)

This document defines the canonical mapping between the presenter-facing `IUpdateDataView` guide_* methods and the concrete Guide Pane v2 widget API. We are committing to v2, so all switching/legacy adapter logic is removed.

- Interface: `fm/modules/update_data/_ui/interface/i_view_interface.py` (`IUpdateDataView`)
- View implementation: `fm/modules/update_data/_ui/ud_view.py` (`UpdateDataView`)
- Presenter: `fm/modules/update_data/_ui/_presenter/guide_pane_presenter.py` (`GuidePanePresenter`)
- Concrete widget (v2): `UpdateDataView.guide_pane` (expects the methods below)

## Principles
- Presenter calls `IUpdateDataView` guide_* methods only (Qt-free, fail-fast guards in view).
- View directly routes to `guide_pane` v2 methods. No adapters. No event duplication.
- Left panel ownership is unchanged; guide methods do not manipulate left panel options.

## API Map (1:1)

- __guide_display(text: str)__
  - Maps to: `guide_pane.display(text)`
  - Purpose: Set the main guide copy/message line.

- __guide_set_status(status: str)__
  - Maps to: `guide_pane.set_status(status)`
  - Expected values: `info | processing | warning | success | error`
  - Purpose: Visual state/badge to reflect current status.

- __guide_show_source_context_options(auto_queue_enabled: bool = False, folder: str | None = None)__
  - Maps to: `guide_pane.show_source_context_options(monitor_enabled=False, auto_queue_enabled=auto_queue_enabled, folder=folder)`
  - Purpose: Show contextual actions for the current source folder (v2 owns its toggles/labels).
  - Notes: `monitor_enabled` is reserved/not used; always `False` in v2.

- __guide_set_archive_summary(text: str)__
  - Maps to: `guide_pane.set_archive_summary(text)`
  - Purpose: Display archive/save destination summary text.
  - Notes: Parsing or presenter notifications happen in presenter/managers, not here.

- __guide_set_actions_enabled(process_enabled: bool, reset_enabled: bool)__
  - Maps to: `guide_pane.set_actions_enabled(process_enabled=process_enabled, reset_enabled=reset_enabled)`
  - Purpose: Enable/disable contextual actions rendered inside the guide pane.
  - Notes: Left panel buttons are managed elsewhere; this is guide-context only.

- __guide_set_discovery_badge(active: bool)__
  - Maps to: `guide_pane.set_discovery_badge(active)`
  - Purpose: Show a per-folder discovery/auto-queue badge indicator in the guide pane.

## Lifecycle examples

- __Files listed__
  - `GuidePanePresenter.on_files_listed(...)` →
    - `view.guide_display("Listed N file(s) ...")`
    - `view.guide_set_status('info')`
    - `view.guide_set_actions_enabled(process_enabled=(N>0), reset_enabled=True)`
    - `view.guide_set_discovery_badge(active_if_same_folder)`
    - `view.guide_show_source_context_options(auto_queue_enabled=discovery_enabled, folder=folder)` (if folder)

- __Discovery toggled__
  - `GuidePanePresenter.on_discovery_toggled(...)` →
    - `view.guide_set_discovery_badge(active_if_same_folder)`
    - `view.guide_show_source_context_options(auto_queue_enabled=enabled, folder=folder)` (if folder)

- __Save option changed__
  - `GuidePanePresenter.on_save_option_changed(...)` →
    - `view.guide_set_archive_summary("Archive: ...")`

## Fail-fast and boundaries

- Each view method raises if `guide_pane` is not initialised. This is intentional (fail fast).
- No guide API method reads or mutates left panel options; that remains under `UpdateDataView.set_source_options()` / `set_archive_options()`.
- No events re-emit functionality covered by these interface methods.

## Removal of switching/legacy paths

- `_GuidePaneViewAdapter` removed; presenter calls interface methods directly.
- Any legacy calls like `set_message`, `set_status`, `show_source_context_options` without the `guide_` prefix are considered deprecated and should be migrated to the `guide_*` API.

## Checklist for implementers (v2 only)

- Ensure `guide_pane` exposes the v2 methods exactly:
  - `display(text)`
  - `set_status(status)`
  - `show_source_context_options(monitor_enabled: bool, auto_queue_enabled: bool, folder: Optional[str])`
  - `set_archive_summary(text)`
  - `set_actions_enabled(process_enabled: bool, reset_enabled: bool)`
  - `set_discovery_badge(active: bool)`
- Verify no adapter imports remain in `ud_view.py`.
- Verify `UpdateDataPresenter` constructs `GuidePanePresenter(self.view)`.
- Verify `StateManager` receives `guide_presenter`.
