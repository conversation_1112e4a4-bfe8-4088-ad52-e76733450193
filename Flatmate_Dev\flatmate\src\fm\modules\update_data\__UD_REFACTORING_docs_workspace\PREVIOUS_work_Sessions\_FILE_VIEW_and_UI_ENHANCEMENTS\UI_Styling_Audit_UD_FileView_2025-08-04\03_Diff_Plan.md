# 03 — Diff Plan (Surgical, Low-Risk)

Goal
Apply minimal changes to align UD File View with theme.qss while avoiding app-wide impact. All changes are scoped and reversible.

Plan Summary
1) UD File View: Wrap the Add/Remove button row in a QFrame named TableViewToolbar (matches theme selector).
2) theme.qss: Reinstate minimal toolbar affordances within QFrame#TableViewToolbar scope.
3) Documentation: Add Styling Contract entries to PRD and Deep-Dive.

A) Code Diffs — UD File View

Target: [ud_file_view.py](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/ud_file_view.py)

Context: In _setup_ui(), we currently create a QWidget named "FileTreeButtonBar". Replace with QFrame named "TableViewToolbar".

Patch (illustrative snippet):

```python
# Before (lines ~93-106)
from PySide6.QtWidgets import QWidget, QHBoxLayout
btn_container = QWidget(self)
btn_container.setObjectName("FileTreeButtonBar")
h = QHBoxLayout(btn_container)
h.setContentsMargins(0, 2, 0, 0)
h.setSpacing(0)
from PySide6.QtWidgets import QWidget as _QW
spacer = _QW(btn_container)
spacer.setSizePolicy(spacer.sizePolicy().Expanding, spacer.sizePolicy().Preferred)
h.addWidget(spacer)
h.addWidget(self._add_remove_btns)
self._layout.addWidget(btn_container)
```

```python
# After
from PySide6.QtWidgets import QFrame, QHBoxLayout, QWidget as _QW
btn_container = QFrame(self)
btn_container.setObjectName("TableViewToolbar")  # Match theme.qss
h = QHBoxLayout(btn_container)
h.setContentsMargins(0, 2, 0, 0)  # legacy spacing preserved
h.setSpacing(0)
spacer = _QW(btn_container)
spacer.setSizePolicy(spacer.sizePolicy().Expanding, spacer.sizePolicy().Preferred)
h.addWidget(spacer)
h.addWidget(self._add_remove_btns)
self._layout.addWidget(btn_container)
```

Property enforcement (already present — keep):
```python
add_btn.setProperty("type", "primary")
rm_btn.setProperty("type", "secondary")
add_btn.style().unpolish(add_btn); add_btn.style().polish(add_btn)
rm_btn.style().unpolish(rm_btn); rm_btn.style().polish(rm_btn)
```

B) QSS Diffs — theme.qss

Target: [theme.qss](flatmate/src/fm/gui/styles/theme.qss)

1. Keep the subtle toolbar frame affordance (already present at lines ~110–114):
```css
QFrame#TableViewToolbar {
    border: 1px solid #2A5A3A;  /* Dark, muted green */
    border-radius: 4px;
}
```

2. Reinstate minimal toolbar button visibility within the toolbar scope (uncomment or add a minimal block):
```css
QFrame#TableViewToolbar QPushButton {
    background-color: var(--color-bg-dark);
    border: 1px solid var(--color-border);
    border-radius: 3px;
    padding: 4px 8px;
    color: var(--color-text-primary);
    min-height: 20px;
}

QFrame#TableViewToolbar QPushButton:hover {
    background-color: var(--color-secondary);
    border: 1px solid var(--color-primary);
    color: var(--color-text-primary);
}

QFrame#TableViewToolbar QPushButton:pressed {
    background-color: var(--color-secondary-pressed);
    border: 1px solid var(--color-primary);
    color: var(--color-text-primary);
}
```

Optional: Keep input styles commented for now to avoid scope creep.

C) Stylesheet Precedence (No code change here; document policy)
- Policy: theme.qss represents the source of truth for refactored modules (like Update Data). Ensure it is loaded after style.qss or that its selectors have higher specificity. Avoid setStyleSheet() calls on these widgets in code.

D) Documentation Edits

Targets:
- PRD: [PRD_table_view_and_ui_enhancements.md](../PRD_table_view_and_ui_enhancements.md)
- Guide: [Update-Data-UI-Deep-Dive.md](../../../../../DOCS/_GUIDES/Update-Data-UI-Deep-Dive.md)

Add “Styling Contract” subsection with:
- Object names:
  - File tree container: #file_tree
  - Toolbar frame: QFrame#TableViewToolbar
- Button properties:
  - QPushButton[type="primary"|"secondary"]
- Precedence policy:
  - theme.qss applies after style.qss for refactored modules
  - Inline setStyleSheet discouraged for these components

E) Rollback Plan
- If any visual regression occurs, revert:
  - ud_file_view.py QFrame back to QWidget with old objectName
  - theme.qss toolbar button blocks to commented state
- These operations are single-file, localized reversions.

F) Verification Steps (see 04_Verification_Checklist.md)
- Confirm toolbar border/shape is visible
- Confirm primary/secondary button styles appear as expected
- Confirm file tree rows/headers adopt theme (#file_tree selectors)
- Confirm no unintended changes in other modules (scoped selectors)