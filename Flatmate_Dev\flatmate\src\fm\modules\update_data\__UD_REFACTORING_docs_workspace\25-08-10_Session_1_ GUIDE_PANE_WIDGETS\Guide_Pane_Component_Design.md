# Guide Pane Component Design (Phase 1 MVP)

## Goals
- Provide a minimal, clear Guide Pane using existing shared widgets where possible.
- Zero Qt coupling in presenter; all interactions via `IUpdateDataView`.
- Fit-to-content sections; browser area fills remaining space and hosts pre-built FilePaneV2.

## Scope (Phase 1)
- Sections: Source, Archive (Master hidden).
- Optional per-section info line; optional main message (pane-level, top by default).
- Empty-state message in browser host when no files.
- No collapsible behaviour yet.

## Container and Layout
- Root: `GuidePane` (QFrame)
  - frameShape=NoFrame; background=transparent; contentsMargins=0
  - layout: QVBoxLayout(spacing=6; margins=0)
- Children (order):
  1) `MainMessage` (SharedLabel.Info) [optional]
  2) `SectionSource` (QWidget → QVBoxLayout)
  3) `SectionArchive` (QWidget → QVBoxLayout)
  4) `BrowserAreaHost` (QWidget → QVBoxLayout) sizePolicy=Expanding,Expanding

## Widget Schematic (with types)

**GuidePane container**
```text
GuidePane (QFrame, NoFrame, transparent, margins=0)
  VBoxLayout(spacing=6, margins=0)
  MainMessage (SharedLabel.Info, wordWrap=true) [optional]
```

**Section: Source**
```text
QWidget
  VBox(spacing=4, margins=0)
  Header (SharedLabel.H2: "Source")
  SectionInfo (SharedLabel.Info) [optional]
  SlotList (QVBoxLayout)
    Slot 1 (QWidget)
      HBox(spacing=6, margins=0)
      Label (SharedLabel.Body: "No files or folders selected…")
      [optional] SharedCheckbox (discovery toggle, future)
```

**Section: Archive**
```text
QWidget
  VBox(spacing=4, margins=0)
  Header (SharedLabel.H2: "Archive")
  SectionInfo (SharedLabel.Info) [optional]
  SummaryLine (SharedLabel.Body: "Same as source folder (default)")
  OptionsRow (QHBoxLayout) [visible iff ArchiveMode==SelectFolder]
    Label (SharedLabel.Body: "Folder:")
    PathChooser (SharedPathPicker or QPushButton + QFileDialog)
```

**BrowserAreaHost**
```text
QWidget
  VBox(margins=0), sizePolicy=Expanding,Expanding
  - hosts pre-built FilePaneV2; shows an italic SharedLabel when empty
```

## Identifiers

| Item | Values | Notes |
|---|---|---|
| Section IDs | `source`, `archive` | Stable identifiers used by API |
| InfoLevel | `info`, `warning`, `success`, `error` | For main/section messages |
| ArchiveMode | `SameAsSource`, `SelectFolder` | Controls archive UI options |
| MainMessagePosition | `Top`, `Bottom` | Default: `Top` |

## View Interface (Presenter-facing)

Add these methods to `IUpdateDataView` (concise, discoverable, no Qt types):

### Pane-level
| Method | Purpose |
|---|---|
| `set_main_message(text, level, position='Top')` | Set the main guide-pane message |
| `clear_main_message()` | Clear the main message |

### Section visibility
| Method | Purpose |
|---|---|
| `show_source_section(visible)` | Show/hide Source section |
| `show_archive_section(visible)` | Show/hide Archive section |

### Section info (per section)
| Method | Purpose |
|---|---|
| `set_section_info(section_id, text, level)` | Set section-level info line |
| `clear_section_info(section_id)` | Clear section-level info line |

### Source content
| Method | Purpose |
|---|---|
| `set_source_slot_message(text)` | Set Slot 1 text (e.g., "No files or folders selected…") |

### Archive content
| Method | Purpose |
|---|---|
| `set_archive_mode(mode)` | Switch between `SameAsSource`/`SelectFolder` |
| `set_archive_summary(text)` | Set summary line text |
| `set_archive_path(path)` | Set chosen folder path (optional) |

### Browser host
| Method | Purpose |
|---|---|
| `set_browser_files(files)` | Provide files to FilePaneV2 |
| `show_browser_empty_state(text)` | Show italic empty-state when no files |

Notes:
- Presenter never touches Qt classes; view maps to shared widgets internally.
- Use `from fm.core.services.logger import log` inside the view for logging.

## Internal GuidePane View (non-public)

Used within `ud_view.py` for rendering; not exposed to the presenter:
- `_render_source_section()` / `_render_archive_section()`
- `_update_section_info(section_id, text, level)`
- `_update_main_message(text, level, position)`
- `_update_browser_visibility(files)`

## Initial State (render contract)
- Main message (Top): "No files selected." (info)
- Source
  - Section info: "No files selected." (info)
  - Slot 1: "No files or folders selected…"
- Archive
  - Summary: "Same as source folder (default)"
  - Section info (only when SelectFolder & no path): "Awaiting archive location…" (warning)
- Browser host
  - Italic empty-state: "Files selected for processing will appear here…"

## Presenter Flow (Phase 1)
1) On view init:
   - `show_source_section(True)`, `show_archive_section(True)`
   - `set_main_message("No files selected.", info, Top)`
   - `set_section_info('source', "No files selected.", info)`
   - `set_source_slot_message("No files or folders selected…")`
   - `set_archive_mode(SameAsSource)`; `set_archive_summary("Same as source folder (default)")`
   - `set_browser_files([])`; `show_browser_empty_state("Files selected for processing will appear here…")`
2) On archive mode -> SelectFolder:
   - Show `OptionsRow`; if no `archive_path`, set `set_section_info('archive', "Awaiting archive location…", warning)`
3) On files change (from Source/Discovery):
   - If files empty: keep empty-state + info lines
   - Else: populate FilePaneV2, hide empty-state

## Reuse and Styling
- Prefer `fm/gui/_shared_components` for labels, checkboxes, and any path picker if available.
- Keep margins=0 on container widgets for tight layout; use subdued styles for info text.

## Out of Scope (Phase 1)
- Collapsible sections (planned)
- Slot-level info lines
- Persistence of UI state

## Risks & Mitigations
- Over-abstracting primitives → keep local, promote later if reused.
- Presenter coupling to Qt → only call interface methods above.
- Styling mismatch → start with shared components; minimal custom styling.

## Acceptance Criteria
- Initial state renders exactly as specified.
- Presenter can set per-section info and main message.
- Browser host integrates FilePaneV2 and shows empty-state when no files.
- No Qt types referenced by presenter; logs via `log` in view.
