---
# Guide Pane: User Options Components (to implement)

Purpose: capture the minimal, clean design for presenter-insertable option widgets inside the Guide Pane, aligned with MVP and layout-only panel managers.
---

## Goals
- Provide small, prebuilt option components (e.g., Enable File Discovery) insertable into the Guide Pane at a given row.
- Keep presenter Qt-free; keep panel/layout managers layout-only.
- Fail-fast, explicit API. No signal chains, no fallback logic.

## Non-Goals
- No runtime wiring inside panel managers.
- No duplicate pathways (interface method and event for the same action).

## Design Overview
- Folder: `update_data/_ui/_view/guide_pane_components/`
- Components: tiny, self-contained widgets with explicit public APIs and a single domain signal each.
- View interface (`IGuidePaneView`) exposes methods to insert/remove/address components by key.
- Registry creates components from a key + props.

## Contracts
- Presenter → View (interface methods only):
  - `insert_option(row: int, key: str, props: dict) -> None`
  - `remove_option(key: str) -> None`
  - Optional convenience: `set_option_state(key: str, value: Any)`, `get_option_state(key: str) -> Any`
- View → Components (Qt wiring internal to view):
  - Creates the widget via registry and inserts at requested row.
  - Connects component domain signal(s) to either:
    - a view-level callback to the presenter (preferred), or
    - a local-bus event only when multiple listeners are required.
- Panel managers: strictly placement; no signals.

## Component Shape (example)
- `EnableFileDiscoveryOption(QWidget)`
  - Props: `{ dir_path: str, text?: str, icon?: QIcon }`
  - Public API: `set_path(str)`, `get_enabled() -> bool`, `set_enabled(bool)`
  - Signal: `toggled(bool)`

## Registry
- `guide_pane_components/registry.py`
  - `create(key: str, props: dict) -> QWidget`
  - Known keys (initial set):
    - `enable_file_discovery`
    - `auto_queue_folder`
    - `warn_on_large_batch`
    - `show_source_hint`

## View API Sketch
- `GuidePaneWidget.insert(row: int, widget: QWidget, key: str) -> None`
- `GuidePaneWidget.remove(key: str) -> None`
- Storage: ordered dict of key → widget; rows kept consistent by layout operations.

## Event vs Interface Guidance
- Use interface methods by default (presenter ↔ view).
- Events only when:
  - multiple listeners need notifying, or
  - genuinely async flows (e.g., dialog results).

## Error Handling & Logging
- Follow fail-fast policy. Minimal, explicit logging via `core.services.logger`.
- No broad try/except that obscures the source of errors.

## Open Questions
- Do we want view-to-presenter callbacks for component signals or standardise via local events for multi-module listeners?
- Do components need persistence of their state via UI prefs?

## Minimal Next Steps (scaffold only)
1. Create folder `guide_pane_components/` and `registry.py` with `create()` stub.
2. Extend `IGuidePaneView` with `insert_option/remove_option` signatures (no wiring yet).
3. Document initial component keys and expected props.

## Notes
- Keep UK spelling where possible.
- Keep APIs explicit and discoverable. Avoid speculative clutter.
