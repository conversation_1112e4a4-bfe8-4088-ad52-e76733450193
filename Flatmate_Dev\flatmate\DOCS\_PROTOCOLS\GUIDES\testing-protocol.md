# Testing-Protocol (Flatmate UI/GUI)

Environment
- Python venv: flatmate/.venv_fm313
- Toolkit: PySide6 for GUI, pytest for tests, pytest-qt or qasync optional later
- Scope: Update Data module GUI verification + unit tests

Activate-Virtualenv
- Git Bash
  - source flatmate/.venv_fm313/Scripts/activate
  - python -m pytest -q tests/gui/update_data -k "smoke or harden or subscription" --maxfail=1 --disable-warnings
- Windows CMD/PowerShell
  - flatmate\\.venv_fm313\\Scripts\\activate
  - .venv_fm313\\Scripts\\python.exe -m pytest -q tests\\gui\\update_data -k "smoke or harden or subscription" --maxfail=1 --disable-warnings

Install/Sync Dev Deps (only if needed)
- pip install -U pip setuptools wheel
- pip install -U pytest pytest-qt

Test-Taxonomy
- Smoke-tests (fast, happy-path wiring)
  - Goal: verify system “turns on” without catching fire
  - Run first locally and in CI
  - Guide: [testing-smoke-tests.md](flatmate/DOCS/_PROTOCOLS/GUIDES/testing-smoke-tests.md)
  - Examples:
    - [tests/gui/update_data/test_smoke.py](tests/gui/update_data/test_smoke.py)
    - [tests/gui/update_data/test_harden_contracts.py](tests/gui/update_data/test_harden_contracts.py)
- Contract-and-unit-tests (APIs, event payloads, decision logic)
- Integration-and-GUI-tests (pytest-qt, event-driven interactions, offscreen when needed)

Run-Tests (repo-root)
- Git Bash (root): ./.venv_fm313/Scripts/python.exe -m pytest -q tests/gui/update_data -k "smoke or harden or subscription" --maxfail=1 --disable-warnings
- CMD/PowerShell (root): .venv_fm313\\Scripts\\python.exe -m pytest -q tests\\gui\\update_data -k "smoke or harden or subscription" --maxfail=1 --disable-warnings
- Optional headless: add --offscreen (see conftest)

GUI-Test-Locations
- tests/gui/update_data/  → Update Data-specific GUI tests
- tests/gui/shared/       → Common GUI helpers
- Harness-scaffold: tests/gui/update_data/conftest.py (adds --offscreen and repo path)

Minimum-Contracts-Covered-in-Phase-1
- Distinct-channels: SOURCE_SELECT_REQUESTED vs add_files_requested
- Dialog-policy: no direct QMessageBox from presenters/managers; only dialog-request events emitted and handled by the View
- File-list-rendering-path: only via FileListUpdatedEvent to View.update_files_display

Troubleshooting
- If Qt platform errors:
  - Set QT_QPA_PLATFORM=offscreen for headless (CI) or ensure desktop session for local
- If import errors:
  - Confirm virtualenv activated and PYTHONPATH includes project root if needed
- If tests hang:
  - Use -vv to see last test, isolate with -k, and check event bus handlers for dangling slots

Next Steps (Phase 2/3)
- Add qasync/pytest-qt driven interaction tests
- Add end-to-end path for processing lifecycle events
- Add a dedicated smoke test file (e.g., tests/gui/update_data/test_smoke.py) that asserts happy-path event flow without deep UI assertions