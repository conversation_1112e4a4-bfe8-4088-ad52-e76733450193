# The Flatmate Project Bible

This is a living document outlining the core vision, architecture, and rules for the Flatmate project. It serves as the single source of truth to ensure consistent, high-quality development.

---

## 1. The "Why" (The Elevator Pitch)

### The Vision: Flatmate

Flatmate is a comprehensive household management application designed to eliminate the friction of shared living. It automates financial tracking, bill payments, and communication for tenants in a shared rental. The full vision includes a subscription-based portal and mobile app where tenants can view their financial status, receive payment reminders, and resolve disputes with clarity, saving the head tenant from uncomfortable conversations and ensuring everyone knows exactly where they stand.

### The MVP: Unify

Unify is the core of the Flatmate vision, delivered as a powerful, self-hosted personal finance tool. Its primary goal is to automate the painful process of consolidating financial data. Unify monitors statement files from major NZ banks, intelligently parses them, and brings them into a single, unified database on the user's local machine. Users can then instantly search, categorise, tag, and export all their transaction data from one place. It's designed to be lightweight, fast, private, and so simple your elderly mum can use it, while providing a solid foundation for the future of Flatmate.

---

## 2. Key Modules & Responsibilities

*This section will provide a high-level breakdown of the codebase structure, outlining the purpose and responsibility of each key module.*

*(Content to be added)*

---

## 3. "Golden Path" User Flows

*This section will describe the step-by-step process for the most critical user journeys in the application, providing a clear reference for how the app is intended to be used.*

*(Content to be added)*

---

## 4. Critical Rules & Patterns

This section contains the hard rules, architectural principles, and established patterns that **must** be followed.

1.  **Model-View-Presenter (MVP)**: The application follows a strict MVP pattern.
2.  **Zero Qt Coupling in Presenter**: Presenters must *never* directly access or know about Qt widgets. All view interaction happens through a clean interface (`IUpdateDataView`, etc.).
3.  **Events for Async Only**: The event system is only for asynchronous communication (like dialog results) or when multiple components need to be notified. Direct, synchronous actions should be simple interface method calls.
4.  **Markdown-Driven Process**: All reports, analyses, and plans must be created as `.md` files.
5.  **Shared Component Architecture**: Reusable UI components are built from base classes in `gui._shared.components` and styled via QSS files. The native theme is dark.
6.  **Configuration Hierarchy**: The app uses a layered configuration system. The order of precedence is: **1) User Preferences (`~/.flatmate/config`)** -> **2) Module-specific defaults** -> **3) Core application defaults (`core.config`)**. New configuration logic must be compatible with this hierarchy.
7.  **Use the Custom Logger**: All logging must use the singleton `log` object from `fm.core.services.logger`.
8.  **Handlers Read Their Own Files**: Statement handlers are responsible for their own file I/O and processing via the `process_file` method.
9.  **UK Spelling**: Use UK spelling (e.g., "categorise," "colour") where possible.
10. **No Speculative Code**: Do not add features or code that isn't directly related to the task at hand. Keep it clean and focused.

---

## 5. Testing Protocol

To ensure consistency and prevent regressions, all tests must be run using the following protocol.

### Running Tests

- **Working Directory**: All test commands must be run from the project root directory (`Flatmate_Dev/`).
- **Environment**: It is not necessary to activate the virtual environment. The command calls the venv's Python interpreter directly.

### Confirmed Working Command (Git Bash)

```bash
# From the project root (e.g., c:/Users/<USER>/_DEV/__PROJECTS/Flatmate_Dev/)
./flatmate/.venv_fm313/Scripts/python.exe -m pytest [path/to/test_file.py]
```

To run all tests, omit the specific file path:

```bash
./flatmate/.venv_fm313/Scripts/python.exe -m pytest tests/
```

---

---

## 6. Development Workflows

This section outlines the official, repeatable processes for development tasks.

-   **/feature-protocol**: The primary workflow for implementing any new feature or significant refactoring. It enforces a structured, documentation-driven approach. All new work must follow this protocol.

---

## 7. Known Technical Debt

This section tracks known architectural issues and areas of the codebase that require refactoring or improvement.

### 1. File Dialogue System

-   **Problem**: The current file dialogue system contains MVP violations, with the presenter layer directly accessing Qt widgets (`self.view.get_main_window()`).
-   **Required Fix**: The system must be refactored to use the clean view interface pattern, removing all Qt coupling from the presenter. The original plan to rename `get_files.py` to `file_selector.py` should also be completed.
-   **Reference**: `FOLDER_DIALOGUE_ARCHITECTURE_DOCS/`

### 2. File Pane v2 Outstanding Tasks

-   **Problem**: While the `file_pane_v2` implementation is complete and functional, several follow-up tasks remain.
-   **Required Tasks**:
    -   Full end-to-end testing with real file operations.
    -   Rename panel layout manager files to `*_layout_manager.py`.
    -   Review and potentially rename `events_data.py` to `event_models.py`.
    -   Decide when to make `file_pane_v2` the default implementation.
