# Folder Handling — Discussion (Session 2)

Last updated: 2025-08-08
Scope: Recent folders, monitored folders, MVP surfacing, and future configuration
Audience: Update Data engineers and AI agents

---

## 1) Context and Goal

- **Need**: A usable MVP to monitor folders now, without reworking the main window.
- **Decision**: Add a checkbox + label under the Source folder path in the Guide Pane to toggle per‑folder auto‑queue (monitoring) on/off.
- **Longer‑term**: A configuration pane is desirable, but deferred.

References:
- Architecture overview: `Auto-Queue_Toggle_Architecture_Overview.md`
- Guide: `flatmate/DOCS/_GUIDES/Update-Data-UI-Deep-Dive.md` (§5.x)

---

## 2) Current Assets (what we already have)

- **FileInfoManager** (`_ui/_presenter/file_info_manager.py`)
  - Owns enriched file list; unrelated to folder preferences.

- **Recent Folders List**
  - Managed by `fm.core.services.recent_folders_service.RecentFoldersService`.
  - Persisted as MRU (source and archive), rendered as compact labels.

- **Monitored Folders (Enabled) List**
  - Effective source of truth: `ud_config.set/is_auto_queue_enabled(folder)` (per‑folder preference).
  - Runtime reconciliation: `FolderMonitorService.set_folder_monitored(folder, enabled)`.
  - UI must always render from config; monitoring follows config, not vice versa.

---

## 3) MVP Surfacing (what shows in the UI now)

- **Where**: Guide Pane, directly under the Source folder path.
- **What**: A single checkbox labelled “Auto‑queue new files in this folder”.
- **Behaviour**:
  - Checked state bound to `ud_config.is_auto_queue_enabled(current_folder)`.
  - Toggle emits `ViewEvents.AUTO_QUEUE_TOGGLED(enabled)`.
  - Presenter resolves folder → delegates to `AutoQueueManager.set_auto_queue(folder, enabled)`.
  - Presenter triggers state sync; View re‑renders checkbox from config.
- **Copy**: Keep concise. Tooltip (optional): “Starts monitoring this folder and queues new files automatically.”

Notes:
- No extra panes or lists required for the MVP.
- No reliance on monitoring runtime state for rendering.

---

## 4) How to Handle the Two Lists (now vs later)

- **Recent Folders (MRU)** — user history
  - Storage: `RecentFoldersService` (core), persisted to user config.
  - Use: Provides quick access and initial directory for dialogs.
  - UI: Shown as compact labels with disambiguation; tooltips show full paths.

- **Monitored Folders (Enabled)** — user preference applied to system monitoring
  - Storage of preference: `ud_config` per‑folder flag.
  - Application: Presenter/`AutoQueueManager` reconciles `FolderMonitorService` to match preference.
  - UI today: Only the single checkbox for the current source.
  - UI later: A Configuration pane could list all enabled folders for review/removal.

- **Keep services separate**
  - Do NOT merge MRU and monitoring into one service. Different lifecycles and concerns.

---

## 5) Should We Formalise Folder Handling?

- **Yes, minimally**, without over‑engineering:
  - Define a small **Folder Preferences API** (thin layer over `ud_config`) used by presenter/managers:
    - `is_auto_queue_enabled(folder) -> bool`
    - `set_auto_queue_enabled(folder, enabled) -> None`
  - Encapsulate reconciliation in **AutoQueueManager** so business logic lives in one place.
  - Keep MRU in `RecentFoldersService` untouched.

- **Defer** a full “Folder Registry” or configuration pane until needed. Avoid speculative clutter.

---

## 6) Minimal Implementation Steps (MVP)

1. **View (Guide Pane)**
   - Add checkbox + label under Source path.
   - Emit `AUTO_QUEUE_TOGGLED(enabled)` when toggled.
2. **Presenter (`ud_presenter.py`)**
   - Subscribe to intent (already wired) and delegate to `AutoQueueManager.set_auto_queue(folder, enabled)`.
   - Use `_resolve_current_folder()` or `FileConfigManager.get_current_folder()`.
3. **Manager (`_ui/_presenter/auto_queue_manager.py`)**
   - Implement `set_auto_queue` (persist + reconcile) and `get_auto_queue` (read).
4. **Rendering (`state_coordinator.py`)**
   - Always render checkbox from `ud_config.is_auto_queue_enabled(current_folder)`.

This yields a working checkbox with correct persistence and monitoring behaviour, without new panes.

---

## 7) Future: Configuration Pane (outline only)

- **Purpose**: Central place to manage monitored folders, MRU, defaults (e.g., quick‑select MRU).
- **Contents**:
  - Table of enabled (monitored) folders with remove/disable.
  - MRU lists with clear/edit controls.
  - Preferences (e.g., `quick_select_recent_source`).
- **Routing**: Likely a new panel or dialog; will require main window layout changes.

---

## 8) Risks and Mitigations

- **Risk**: Presenter bloat if it keeps logic inline → **Mitigation**: use `AutoQueueManager`.
- **Risk**: UI reads monitoring runtime state → **Mitigation**: render from config only.
- **Risk**: Conflating MRU and monitoring → **Mitigation**: keep services separate.

---

## 9) Decisions (MVP)

- Add checkbox + label under Source path in Guide Pane (per‑folder auto‑queue).
- Render from `ud_config`; reconcile `FolderMonitorService` via `AutoQueueManager`.
- No new configuration pane for now.

End of discussion.
