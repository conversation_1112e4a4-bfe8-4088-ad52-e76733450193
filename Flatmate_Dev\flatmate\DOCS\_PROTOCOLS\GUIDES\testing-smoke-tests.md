# Smoke Tests (Concept and Usage)

Purpose
- A smoke test is a fast, lightweight check that verifies the system’s most critical paths basically work after changes.
- Think “turn it on and see if it catches fire.” If smoke tests fail, stop and fix before running deeper suites.

Key Characteristics
- Shallow, not deep: validates happy path wiring and basic behavior
- End-to-end or close to it: exercises main flow users rely on
- Fast feedback: seconds, not minutes
- Binary gate: if it fails, don’t proceed

Why Use Smoke Tests
- Catch obvious breakages (imports, wiring, missing deps) early
- Prevent wasting time on long suites when fundamentals are broken
- Baseline confidence after refactors

In Flatmate (Update Data)
- Event-first wiring must be intact:
  - SOURCE_SELECT_REQUESTED is emitted and handled
  - “add_files_requested” is separate and present
  - FILE_LIST_UPDATED can be observed with expected shape
  - ERROR_DIALOG_REQUESTED/SUCCESS_DIALOG_REQUESTED can be emitted and picked up by the View

How to Structure a Smoke Test
1) Arrange:
   - Ensure offscreen mode for headless (optional)
   - Subscribe to minimal event bus topics
2) Act:
   - Emit canonical intents/events
3) Assert:
   - Confirm handlers ran and event payloads have basic expected fields

Example (Already in Repo)
- File: tests/gui/update_data/test_event_contracts.py
  - Distinct channel emissions (SOURCE_SELECT_REQUESTED vs add_files_requested)
  - DialogRequestEvent structure sanity check
  - FileListUpdatedEvent payload shape check

Suggested Dedicated Smoke Test
- File: tests/gui/update_data/test_smoke.py
- Intent: one happy path to prove wiring:
  - Emit SOURCE_SELECT_REQUESTED ("SELECT_FILES")
  - Emit FILE_LIST_UPDATED with a couple of fake paths
  - Assert that both events appear in the bus log and payloads are minimally correct

Run Commands (in venv)
- pytest -q -k "smoke"
- pytest -q tests/gui/update_data --offscreen -k "smoke or contracts"

What Smoke Tests Are Not
- They are NOT exhaustive validations
- They are NOT a replacement for unit, contract, integration, or GUI interaction tests
- They should remain few and fast

Workflow Integration
1) Local dev: run smoke tests before pushing
2) CI pipeline: run smoke tests first; on pass, run the rest
3) After refactors: update smoke tests to reflect the new happy path

Next Step (Optional)
- Add tests/gui/update_data/test_smoke.py implementing the minimal happy-path event flow as described above.