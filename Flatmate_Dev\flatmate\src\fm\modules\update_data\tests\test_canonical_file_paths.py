"""
Tests for canonical file paths implementation in Update Data module.

This test suite verifies:
1. FileManager maintains canonical file_paths_list
2. FilePane correctly handles file paths
3. ProcessingManager uses FilePane as source of truth
4. Edge cases like empty selections and type conversions
"""

import os
import unittest
from unittest.mock import MagicMock, patch

from fm.core.services.logger import log
from fm.modules.update_data._presenter.file_manager import FileManager
from fm.modules.update_data._presenter.processing_manager import ProcessingManager


class TestCanonicalFilePaths(unittest.TestCase):
    """Test suite for canonical file paths implementation."""

    def setUp(self):
        """Set up test environment."""
        # Mock dependencies
        self.mock_view = MagicMock()
        self.mock_state_manager = MagicMock()
        self.mock_state = MagicMock()
        self.mock_state_manager.state = self.mock_state
        self.mock_folder_monitor_service = MagicMock()
        self.mock_local_bus = MagicMock()
        self.mock_info_bar_service = MagicMock()
        
        # Create file manager instance
        self.file_manager = FileManager(
            self.mock_view,
            self.mock_state_manager,
            self.mock_folder_monitor_service,
            self.mock_local_bus
        )
        
        # Create processing manager instance
        self.processing_manager = ProcessingManager(
            self.mock_view,
            self.mock_state,
            self.mock_info_bar_service,
            self.mock_local_bus
        )
        
        # Sample file paths for testing
        self.sample_files = [
            os.path.join("path", "to", "file1.csv"),
            os.path.join("path", "to", "file2.csv"),
            os.path.join("path", "to", "file3.csv")
        ]
        
        # Sample folder path for testing
        self.sample_folder = os.path.join("path", "to", "folder")

    def test_file_manager_initialization(self):
        """Test that FileManager initializes with empty file_paths_list."""
        self.assertIsInstance(self.file_manager.file_paths_list, list)
        self.assertEqual(len(self.file_manager.file_paths_list), 0)

    @patch("fm.modules.update_data.config.ud_config")
    def test_file_selection_updates_canonical_list(self, mock_config):
        """Test that file selection updates the canonical file_paths_list."""
        # Setup mock view to return sample files
        self.mock_view.show_files_dialog.return_value = self.sample_files
        
        # Call file selection method
        self.file_manager._select_files()
        
        # Verify file_paths_list is updated
        self.assertEqual(self.file_manager.file_paths_list, self.sample_files)
        
        # Verify files are passed to center panel
        if hasattr(self.mock_view, 'center_panel'):
            self.mock_view.center_panel.set_files.assert_called_with(self.sample_files, "")

    @patch("fm.modules.update_data.config.ud_config")
    def test_folder_selection_updates_canonical_list(self, mock_config):
        """Test that folder selection updates the canonical file_paths_list."""
        # Setup mock view to return sample folder
        self.mock_view.show_folder_dialog.return_value = self.sample_folder
        
        # Setup mock discover_files to return sample files
        self.file_manager._discover_files_in_folder = MagicMock(return_value=self.sample_files)
        
        # Call folder selection method
        self.file_manager._select_folder()
        
        # Verify file_paths_list is updated
        self.assertEqual(self.file_manager.file_paths_list, self.sample_files)
        
        # Verify files are passed to center panel
        if hasattr(self.mock_view, 'center_panel'):
            self.mock_view.center_panel.set_files.assert_called_with(self.sample_files, self.sample_folder)

    def test_processing_manager_gets_files_from_file_pane(self):
        """Test that ProcessingManager gets files from file_pane."""
        # Setup mock file_pane
        self.mock_view.file_pane = MagicMock()
        self.mock_view.file_pane.get_files.return_value = self.sample_files
        
        # Call get_files_for_processing
        files = self.processing_manager.get_files_for_processing()
        
        # Verify files are retrieved from file_pane
        self.assertEqual(files, self.sample_files)
        self.mock_view.file_pane.get_files.assert_called_once()

    def test_processing_manager_fallback_to_state(self):
        """Test that ProcessingManager falls back to state if file_pane not available."""
        # Setup mock state with sample files
        self.mock_state.selected_files = self.sample_files
        
        # Remove file_pane from view
        if hasattr(self.mock_view, 'file_pane'):
            delattr(self.mock_view, 'file_pane')
        
        # Call get_files_for_processing
        files = self.processing_manager.get_files_for_processing()
        
        # Verify files are retrieved from state
        self.assertEqual(files, self.sample_files)

    def test_processing_manager_empty_list_on_error(self):
        """Test that ProcessingManager returns empty list on error."""
        # Setup mock file_pane to raise exception
        self.mock_view.file_pane = MagicMock()
        self.mock_view.file_pane.get_files.side_effect = Exception("Test error")
        
        # Call get_files_for_processing
        files = self.processing_manager.get_files_for_processing()
        
        # Verify empty list is returned
        self.assertEqual(files, [])

    def test_handle_process_uses_file_pane_files(self):
        """Test that handle_process uses files from file_pane."""
        # Setup mock file_pane
        self.mock_view.file_pane = MagicMock()
        self.mock_view.file_pane.get_files.return_value = self.sample_files
        
        # Setup save location
        self.processing_manager.save_location = "save/location"
        
        # Setup mock get_update_database
        self.mock_view.get_update_database.return_value = True
        
        # Call handle_process
        with patch.object(self.processing_manager, 'get_files_for_processing', wraps=self.processing_manager.get_files_for_processing) as mock_get_files:
            self.processing_manager.handle_process()
            
            # Verify get_files_for_processing is called
            mock_get_files.assert_called_once()
            
            # Verify job sheet uses files from file_pane
            self.assertEqual(self.processing_manager.job_sheet_dict["filepaths"], self.sample_files)


if __name__ == "__main__":
    unittest.main()
