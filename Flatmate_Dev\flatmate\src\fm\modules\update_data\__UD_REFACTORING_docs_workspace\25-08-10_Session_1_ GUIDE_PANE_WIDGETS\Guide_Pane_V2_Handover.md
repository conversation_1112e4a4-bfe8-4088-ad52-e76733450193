# Guide Pane V2 – Handover

Date: 2025-08-10
Owner: Cascade (handover)

## Summary
- V2 widget implemented and styled (green border) and is the default in the centre panel.
- Strict constraints: the centre panel manager composes only. It does NOT manage the widget and does NOT relay signals.
- Presenter remains Qt-free. Wiring to services is outside the centre panel. No speculative abstractions.
- Discovery option exists as a presentational row only; visibility/state controlled via V2 public API.

## Locations
- Legacy pane (kept):
  - `src/fm/modules/update_data/_ui/_view/center_panel_components/guide_pane.py`
- Guide Pane V2 (default):
  - `src/fm/modules/update_data/_ui/_view/center_panel_components/guide_pane_v2/guide_pane_v2.py`
- Shared subcomponents:
  - `src/fm/modules/update_data/_ui/_view/center_panel_components/guide_pane_components/`
- Layout (composition only):
  - `src/fm/modules/update_data/_ui/_view/center_panel_layout.py`

## Tech baseline
- Qt binding: PySide6 (confirmed)
- Style: reuse shared labels (`fm/gui/_shared_components/widgets/labels.py`)
- Architecture: presenter remains Qt‑free; view implements mapping (do not wire interface yet per instruction)
- Logging: `fm.core.services.logger.log` when needed (not added to stub yet to keep it minimal)

## Current state (GuidePane V2)
- Green border and spacing restored to match legacy visual cues.
- Sections always visible:
  - Source: placeholder “No files or folders selected…” until a concrete source is set.
  - Archive: summary line; optional “Folder:” key/value shown when mode is SelectFolder.
- Enable discovery row (checkbox + info) under Source; hidden by default.
- Placeholder auto-hides when `set_section_info("source", <text>)` is called.
- No file view embedded; placed ABOVE the file view in the centre panel stack.

### Public API (concise)
- `set_main_message(text, level="info", position="Top")`
- `clear_main_message()`
- `set_section_info(section_id, text, level="info")`  // "source" | "archive"
- `clear_section_info(section_id)`
- `set_source_slot_message(text)`
- `show_source_enable_option(visible: bool)`
- `set_source_enable_checked(checked: bool)`
- `set_archive_mode(mode)`  // "SameAsSource" | "SelectFolder"
- `set_archive_summary(text)`
- `set_archive_path(path)`

## Integration constraints (non‑negotiable)
- Centre panel manager composes only. It does not set texts, does not manage state, and does not relay signals.
- Presenter remains Qt‑free; call the view’s methods directly via the interface layer where applicable.
- Discovery toggle is a view concern; persistence/service calls belong in the presenter and services.

## Action items (immediate)
1) Verify centre panel has zero guide mutations (no calls into the widget from `center_panel_layout.py`).
2) Presenter wiring for discovery (no events needed for MVP):
   - Show/hide: `show_source_enable_option(...)`
   - State: `set_source_enable_checked(...)`
3) Ensure placeholder logic: never re‑show the placeholder after setting concrete Source info.

## Run
- From project root: `flatmate/.venv_fm313/Scripts/python.exe -m fm.main` (or per your alias: `venv` then `fm` in `flatmate/`).

## Tests
- From project root: `flatmate/.venv_fm313/Scripts/python.exe tests/test_real_csvs.py`

## Known issue (separate from V2)
- Folder monitor callback signature mismatch:
  - Error: `FileInfoManager._on_files_discovered() takes 2 positional arguments but 3 were given`.
  - `folder_monitor_service` calls `(file_path, source_folder)` — update handler signature or wrap accordingly.

## Rollback
- If needed, toggle back to legacy pane at runtime or disable V2 in layout instantiation (composition only; no logic moves).

## Status
- V2 widget implemented, styled, default; presenter wiring intentionally minimal; centre panel remains composition-only.

End of handover.
