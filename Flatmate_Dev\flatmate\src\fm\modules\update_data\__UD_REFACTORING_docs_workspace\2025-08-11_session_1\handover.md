# Update Data – Guide Pane and Path Formatting (Session 2025-08-11)

## Summary
- **Goal**: Finalise Guide Pane path display and layout; ensure Archive mirrors Source formatting; make pane fit contents; remove gaps by collapsing empty rows; keep presenter Qt‑free and formatting in the view.
- **Outcome**: Consistent path formatting for Source/Archive (with `~`, tooltip, actual casing). Guide Pane now uses uniform slot containers that collapse to zero height when empty. Context menu supports “Open in Explorer”. Presenter uses `DirectoryAliasService` for label↔path mapping before updating the view.

## Developer Quickstart
- __Run__: `cd {workspace}/flatmate` → `venv` alias → `fm`
- __Where to look__:
  - View: `.../_ui/_view/center_panel_components/guide_pane_v2.py`
  - Components: `.../guide_pane_components/{section.py, slot_row.py, key_value_row.py}`
  - Presenter: `.../_ui/_presenter/file_config_manager.py`
  - Service: `fm/core/directory/services/directory_alias_service.py`
- __Smoke checks__:
  - Source/Archive labels show correctly with `~` for home; tooltip is full path.
  - Right‑click → Open in Explorer works for Source/Archive.
  - Empty slots take zero space; switching Archive mode shows/hides its row immediately.

## Key Changes
- **Uniform slots**: Introduced `SlotRow` (borderless frame) used for all optional rows so behaviour is predictable and gap‑free.
- **Pane sizing**: `GuidePaneV2` sizes to contents vertically; removed stretch; tight spacing/margins.
- **Archive formatting**: Archive display and tooltip mirror Source rules; shows full path tooltip; home paths abbreviated with `~`.
- **Correct casing**: Restores filesystem casing for tooltips/segments on Windows.
- **Context menus**: Added “Open in Explorer” for Source and Archive labels.

## Files Touched
- `fm/modules/update_data/_ui/_view/center_panel_components/guide_pane_v2.py`
  - Formats paths (`_format_path_for_label()`), preserves drive letters, uses `~` for home.
  - Context menu for “Open in Explorer”.
  - Refactored to use `SlotRow` for:
    - Placeholder row: `src_slot_row`
    - Source enable option row: `src_enable_container`
    - Archive folder row: `arc_path_container`
  - Collapsing via `set_collapsed(True/False)` after updates; layout nudged with `adjustSize()/updateGeometry()`.
  - Fixed placeholder bug (no longer shows when Source path is set).

### Component Contracts (authoritative)
- __Section__ (`section.py`)
  - Structure: header (Subheading), `info: InfoLabel`, `body: QVBoxLayout`.
  - Margins/spacings: zero within body; header gap minimal.
  - API: `set_info(text)`, `clear_info()`, `add_to_body(widget)`.
- __SlotRow__ (`slot_row.py`)
  - Uniform borderless slot container.
  - API: `set_content(QWidget)`, `set_collapsed(bool)`.
  - Collapsed = zero height (`hide()`, fixed height 0, vertical policy Fixed).
  - Expanded = shown, minimal margins; top margin provides inter‑row gap.
- __KeyValueRow__ (`key_value_row.py`)
  - Used for labelled value rows (e.g., “Folder: <path>”). Keep margins zero; vertical policy `(Preferred, Minimum)`.


- `fm/modules/update_data/_ui/_view/center_panel_components/guide_pane_components/slot_row.py`
  - New component. Borderless frame with:
    - `set_content(widget)`
    - `set_collapsed(collapsed: bool)` — uses `hide()`, `setFixedHeight(0)`, min/max height 0, and `QSizePolicy.Fixed` to ensure zero layout footprint; restores when expanded with a small top margin for inter‑row gap.

- `fm/modules/update_data/_ui/_view/center_panel_components/guide_pane_components/section.py`
  - Section container with header, optional info, and a body layout. Tight margins/spacings to avoid extra gaps.

- `fm/modules/update_data/_ui/_presenter/file_config_manager.py`
  - Integrated `DirectoryAliasService` for stable label generation and robust label→path resolution.
  - Presenter resolves real paths before calling view methods to keep UI formatting focused in the view.

- `fm/core/directory/services/directory_alias_service.py`
  - Centralized bidirectional mapping of labels↔paths. Collision‑safe labels; case‑insensitive resolution; MRU hydration.

## Behavioural Rules Implemented
- **Display**
  - Parent path: dim monospace. Basename: bold.
  - Home directory paths: abbreviate with `~` in the label; full absolute path as tooltip.
  - Drive letter hidden in display when under home; tooltip preserves drive letter (normalised).
- **Separation of concerns**
  - Services/presenter: path/label logic only.
  - View: formatting and Qt.
- **MVP comms**
  - Prefer interface methods; reserve events for async/multi‑listener flows.

## View Usage Snippets
- __Set Source path summary__
```python
view.set_section_info("source", f"Source: {path}")
```
- __Set Archive summary__ (mirrors Source when text == "Same as Source")
```python
view.set_archive_summary(summary_text)
```
- __Show Archive folder row__
```python
view.set_archive_mode("SelectFolder")  # shows
view.set_archive_mode("SameAsSource")  # hides
```
- __Per‑folder discovery row__
```python
view.show_source_context_options(folder=folder_path, auto_queue_enabled=True)
```

## Do / Don’t
- __Do__
  - Add new rows as `SlotRow` only; place any custom widget inside via `set_content()`.
  - Collapse rows with `set_collapsed(True)`; avoid raw `setVisible(False)`.
  - Keep presenter Qt‑free; resolve labels to paths before calling view.
- __Don’t__
  - Don’t format paths in services/presenter.
  - Don’t split on `:` when extracting paths (preserve `C:\`).
  - Don’t re‑emit left panel signals; the view calls widget APIs directly (layout manager only).

## Changelog (high‑level)
- Guide Pane now sizes to content; removed layout stretch.
- Added `SlotRow` and refactored all optional rows to use it; fixed zero‑height collapse.
- Fixed Source placeholder visibility logic.
- Path formatting: `~` for home, bold basename, dim monospace parent, correct casing, tooltip full path.
- Context menus: “Open in Explorer” for Source/Archive labels.
- Presenter uses `DirectoryAliasService` for robust label→path mapping.


## How to Verify
1. Launch app (per workspace venv):
   - `cd {workspace}/flatmate` → `venv` alias → `fm`
2. In Update Data:
   - Select a Source folder with files.
   - Confirm Source label shows `~` when under home, with correct casing; tooltip shows full absolute path.
   - If Archive = “Same as Source”, Archive label mirrors Source formatting.
   - If Archive mode = “SelectFolder”, the Archive “Folder:” row appears; otherwise it is collapsed (zero gap).
   - Right‑click Source/Archive label → “Open in Explorer” opens the correct path.
   - Toggle “File discovery” row; when there’s no folder context, row collapses fully.
   - Ensure the placeholder “No files or folders selected…” shows only when there’s no Source path and disappears otherwise.

## Notes on Layout
- Guide Pane now uses: `Section` (header, info, body) → body contains uniform `SlotRow`s.
- `SlotRow` handles its own collapse/expand so empty rows take zero space.
- Inter‑row gap is controlled by SlotRow’s top margin when expanded; Section’s body has zero spacing/margins for precision.

## Known Follow‑ups
- Add unit tests for `DirectoryAliasService` (label collisions, round‑trip resolution, case‑insensitive behaviour).
- Review `KeyValueRow` internals to ensure zero margins and `(Preferred, Minimum)` vertical policy if any residual padding appears.
- Optional: expose a tiny helper in `Section` to add a labelled SlotRow in one call for common rows.
- Optional: add UI smoke tests for path display with edge cases (UNC paths, symlinks, long paths).

## Troubleshooting
- If a collapsed row still leaves a gap, check the inner widget’s margins/min‑height. Wrap all row content inside a `SlotRow` (as done) and ensure content widgets don’t impose fixed heights.
- If splitter doesn’t rebalance after content change, call `adjustSize()` (already done) or set splitter sizes from the view containing the pane.

## Session Log Highlights
- Fixed colon handling to preserve `C:\` drive letters when parsing labels like “Source: …”.
- Implemented `_restore_actual_casing()` for Windows.
- Fixed visibility bug for placeholder message.
- Introduced `SlotRow` and migrated all optional rows to it for consistent layout.
- Ensured Guide Pane sizes to content and removed layout stretch.

## Owner’s Notes
- All logging uses `fm.core.services.logger.log` (as per project rules). Avoid broad try/except; fail fast.
- Presenter remains Qt‑free; view handles all Qt logic.
- UK spelling followed in docs.
