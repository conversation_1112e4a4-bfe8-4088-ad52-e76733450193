# Update Data: MRU + Guide Pane – 2025-08-08

## Summary of recent changes
- Added `set_guide_archive_summary(text)` to view interface and implemented in view; presenter updates this when save option changes and after source selection with Same-as-Source.
- Source MRU labels are compact (base name only), duplicate names disambiguated with short parent hint (e.g., "Photos — 2024"). Internal mapping keeps full paths.
- Restored recent source folders helpers: `_update_recent_folders`, `get_recent_folders`, `clear_recent_folders`.
- Save-location selection reuses `FileSelector.get_paths('folder')` and updates guide summary.

## Observed UX issue
- Selecting a Recent Source folder from the Source options immediately queues files (no dialog). This may be surprising to non-technical users who expect a folder dialog to open at that location for confirmation.

## Options (trade-offs)
- Option A – Quick Select (current): Selecting an MRU source directly queues files from that folder.
  - Pros: Fast for power users.
  - Cons: Surprising; skips confirmation; users can’t inspect the folder first.

- Option B – Dialog at MRU Path (proposed default): Selecting an MRU source opens the folder dialog initialised at that path; user confirms, then we queue files.
  - Pros: Matches mental model; reduces surprise; supportive for non-technical users.
  - Cons: One extra click for power users.

- Option C – Preference Toggle: Add a runtime-configurable preference: "Quick select recent folders (skip dialog)".
  - Default: OFF (behaves like Option B). Power users can enable fast behaviour.
  - Minimal code change, no additional UI elements required.

## Recommendation
- Adopt Option B as default (open dialog at MRU path), and add Option C as a simple preference for power users.
- Rationale: Non-technical users are a key audience; confirmation-oriented flows are more intuitive and forgiving. Preference preserves efficiency for advanced users.

## Next steps
- Implement MRU behaviour change for Source:
  - On selecting an MRU label, open folder dialog at that path; queue on confirm.
  - Add preference `quick_select_recent_source` (default False) to allow bypassing the dialog.
- Implement Recent Archive Folders MRU similar to Source with compact labels.
- Guide pane: refine prompts linked to monitoring state and archive selection.
- Minor polish: tooltips with full path for MRU items (optional).
