# Account Contiguity and Duplicate Detection

Purpose
- Define a robust, format-agnostic identity and ingestion pattern that:
  - Ensures account timeline contiguity across repeated imports
  - Safely drops duplicates across multi-format sources (CSV, OFX, PDF)
  - Produces simple, auditable signals in the datastore and UI

Scope
- Transaction identity and de-duplication
- Ingestion overlap strategy
- Contiguity verification with minimal schema additions
- Observability and operator feedback

Core Principles
- Idempotent ingestion: Re-importing overlapping history must not create duplicates.
- Source diversity: Different export formats for the same bank data often vary in descriptors/headers; identity must not depend on textual descriptions.
- Conservative identity: Prefer bank-origin IDs when available; otherwise use financially-stable tuple identity.
- Overlap-by-design: Always ingest with a backward time window to guarantee seam coverage.

Identity and Duplicate Rules
1) Primary Identity (when present)
   - Key: (account_id, source_uid)
   - Description: Use bank-origin transaction identifier provided by the source handler (e.g., OFX FITID). Enforce uniqueness.

2) Secondary Identity (fallback for amount != 0)
   - Key: (account_id, txn_date, amount, balance)
   - Description: For typical bank data, date/amount/balance tuple is stable across CSV/OFX/PDF. Do not use textual description for identity. If multi-currency can appear for a single account, include currency.
   - Constraint: amount != 0. Zero-amount rows are excluded from this identity due to higher collision risk.

3) Zero-Amount Rows
   - Not included in tuple identity. Handle separately if a future use-case requires it (e.g., add a normalized descriptor hash as an auxiliary identity).

Normalization Rules
- Date: Normalize to bank calendar date (drop time or normalize to canonical bank timezone).
- Amount: Enforce consistent sign convention before identity evaluation.
- Balance: Use post-transaction ledger balance as exported by the bank; do not compute.
- Currency (optional): Include in identity if an account may hold multiple currencies.

Contiguity and Overlap Strategy
- Overlap Window: For each ingestion session, re-ingest a time window (e.g., last 60–90 days) to ensure seams are covered, catching late-arriving or corrected transactions.
- Idempotency: With identity rules + constraints, duplicates in the overlap are safely dropped.

Simple, Auditable Signals in the Datastore
- Add Columns:
  - dupe: bool (default false)
    - True when an incoming row conflicts on identity with an existing row during the same ingestion session (or when detected in pre-insert checks).
    - Rows marked dupe are not inserted into the primary transaction table if hard-constraints are used; or, if a soft-write path is used, they are captured into a side table. Pick one strategy (see “Write Strategies” below).
  - ingestion_ts: datetime (utc)
    - Timestamp when the row was processed/attempted.
  - ingestion_session_id: text/uuid
    - Correlates all rows from the same file/import event.

Write Strategies
- Preferred (Hard Constraints + Metrics):
  - Enforce unique constraints:
    - Unique(account_id, source_uid) when source_uid present
    - Unique(account_id, txn_date, amount, balance[, currency]) for amount != 0
  - Insert with ON CONFLICT DO NOTHING.
  - Record metrics per ingestion_session_id:
    - rows_in, net_new, duplicates_dropped, window_min_date, window_max_date, identity_rule_used
  - Optional: Maintain a per-session duplicates table to snapshot “attempted duplicate keys” for audit (contains the would-be row and reason).

- Alternative (Soft Insert with dupe flag):
  - Perform pre-insert existence check on identity.
  - If found, write an entry to a transactions_ingest_log table with dupe=true and the original identity target; do not write into the main ledger table.
  - This preserves an auditable trace of duplicates without relying on DB-level conflicts for reporting.

Contiguity Verification Heuristic
- Rationale: “Dupes at seams” proves that overlap ingestion is working and the account timeline remains contiguous.
- Display in UI per account and per session:
  - “Overlap seam evidence”: duplicates_dropped > 0 across recent N sessions.
  - Show ingestion_ts, file/source, rows_in, net_new, duplicates_dropped.
- Operational Rule of Thumb:
  - If duplicates_dropped stays at 0 for several consecutive imports while the overlap window is active, investigate: the overlap window might be too narrow or date normalization changed.

Handler and Pipeline Guidance
- Handlers:
  - Map any bank transaction identifier to source_uid when available (OFX often provides FITID).
  - Do minimal descriptor touching (trim only); do not use descriptors for identity.
- Pipeline:
  - Always compute identity after normalization.
  - Always ingest with overlap window.
  - Record ingestion_session_id, ingestion_ts.
  - Emit per-session metrics and store them.

Schema Sketch (relational flavor)
- transactions
  - id (pk)
  - account_id (fk)
  - txn_date date not null
  - amount numeric not null
  - balance numeric not null
  - currency text null  -- include in identity if accounts can vary currency
  - source_uid text null
  - description text null  -- not used for identity
  - ingestion_ts timestamptz not null default now()
  - ingestion_session_id text not null
  Indexes/Constraints:
    - unique (account_id, source_uid) where source_uid is not null
    - unique (account_id, txn_date, amount, balance) where amount != 0
    - or include currency in the unique if needed

- ingestion_sessions
  - ingestion_session_id (pk)
  - account_id
  - source_name
  - file_hash
  - rows_in int
  - net_new int
  - duplicates_dropped int
  - window_min_date date
  - window_max_date date
  - completed_ts timestamptz

- ingestion_duplicates (optional; for soft-logging or hard-constraint conflict snapshots)
  - id (pk)
  - ingestion_session_id
  - account_id
  - identity_rule_used enum('source_uid','date_amount_balance')
  - txn_date
  - amount
  - balance
  - currency
  - source_uid
  - reason text  -- e.g., "conflict on (account_id, date, amount, balance)"
  - recorded_ts timestamptz default now()

UI Surfacing
- For each ingestion session displayed in Update Data:
  - Show: rows_in, net_new, duplicates_dropped, window_min_date..window_max_date, rule used
  - Badge “Seam confirmed” when duplicates_dropped > 0
- For per-account health:
  - Show recent sessions with a small sparkline or counters of duplicates_dropped to confirm overlap is functioning.
- Keep descriptors visible but explicitly labeled as non-identity.

Operational Notes
- Time zones: Store ingestion_ts and compute in UTC. Normalize txn_date consistently to bank date before identity.
- Reversals/corrections: Identity is stable, but banks can backfill; overlap + idempotency will handle safely; metrics will show conflicts.
- Backfill/migration: Introduce constraints and ON CONFLICT semantics; run backfill with session metrics enabled to establish baseline.

Summary
- Use tiered identity (source_uid first; else date-amount-balance).
- Always ingest with overlap and record ingestion metrics.
- Add a simple dupe boolean signal alongside ingestion_ts/session metrics (either via conflict snapshots or a soft-log table).
- Surface “duplication at seams” in the UI to provide visible contiguity assurance.

— — —

Addendum: Minimal Required Fields + Zero-Amount Policy

Minimal field set (practical floor)
- Required to function with CSV-only pipelines where headers/descriptors are unreliable:
  - account_id (or resolvable account reference)
  - txn_date (bank calendar date)
  - amount (signed)
  - balance (post-transaction ledger balance)
- Optional but beneficial when present:
  - currency, source_uid, description/memo

Why these four suffice
- Identity fallback uses (account_id, txn_date, amount, balance) with amount != 0, which only requires these four fields.
- Contiguity/seam proof relies on duplicates_dropped and ingestion timestamps, independent of descriptors.

Zero-amount transactions (amount == 0)
- Policy: Soft delete or exclude from main ledger by default.
  - Rationale: Zero-amount rows carry no financial movement and commonly represent informational entries or internal adjustments that do not affect totals.
- Implementation options:
  - Ingest into a separate auxiliary table (transactions_zero_amount) for audit, or
  - Store in the main table with a soft_deleted bool = true and exclude from balances/analytics queries.
- Identity handling:
  - Zero-amount rows are excluded from tuple identity de-duplication to avoid false matches.
  - If kept, do not contribute to contiguity/dupe metrics.

Addendum: Source Hierarchy (Pragmatic)
- Trust order (when multiple sources exist for the same account):
  1) OFX (when well-formed): preferred for FITID and standardized fields.
  2) CSV: reliable and widely available; treated as bank-local standard.
  3) PDF: parser-dependent; advisory fallback.

Addendum: Contiguity Hit Table (Identity Coverage)
- identity_coverage
  - account_id
  - identity_key_hash
  - identity_rule_used
  - txn_date, amount, balance, currency, source_uid (for audit)
  - first_seen_ts, last_seen_ts
  - total_hits
  - by_source jsonb
- Purpose: Quantify cross-session and cross-source presence of each identity; helps visualize seams and corroboration without needing descriptors.

Implementation Notes
- Start with CSV-only minimal pipeline using the four-field floor and zero-amount soft-delete.
- Introduce OFX later with a standard parser; avoid bespoke work until a bank demonstrably requires it.
- Keep all normalization and identity logic centralized so CSV/OFX/PDF converge on the same behavior.

— — —

Addendum: Kiwibank Full CSV Preference and Counterparty Details

Preference
- Kiwibank “full CSV” includes the counterparty bank account number, which is operationally valuable (e.g., for payment reversals or reconciliation).

Data handling guidance
- In handler normalization:
  - Map counterparty_account_number to a dedicated field (e.g., counterparty_acct).
  - Preserve exactly as provided (no normalization beyond trimming); treat as PII and handle securely.
- Storage recommendations:
  - Store counterparty_acct in transactions table (nullable), with:
    - index if you need fast lookups (e.g., find all txns involving a given counterparty account)
    - encryption-at-rest and restricted access policies
- UI/UX:
  - Surface counterparty_acct only in detailed views with appropriate masking by default (e.g., show last 3–4 digits) and an “unmask” action gated by permissions.
- Reconciliation utilities:
  - Provide quick actions in the UI for “copy counterparty account number” and “initiate reversal workflow” (future).
- Identity impact:
  - Do not include counterparty_acct in the duplicate identity tuple; keep identity format-agnostic.
  - You may use it as an auxiliary signal for anomaly detection (e.g., large transfer to new counterparty).

Security and privacy notes
- Treat counterparty_acct as sensitive: avoid logging full values; mask in logs and analytics; control access.

— — —

Addendum: Extended vs Original Bank Columns (Storage Strategy)

Context
- You currently record all available columns for all formats, use your bank’s fields as the standard for extended types, and concatenate to build a standardized details column. Considering also storing original bank columns verbatim.

Trade-offs
- Pros (store original columns verbatim):
  - Lossless archival of source data; enables exact reconstruction/export in a format familiar to users.
  - Easier auditing and troubleshooting when parsers evolve.
  - Supports bank-specific features (like Kiwibank counterparty account) without forcing them into extended schema immediately.
- Cons:
  - Potentially very wide table with sparse, repetitive columns across banks.
  - Schema churn when adding new banks/columns; migration overhead.

Recommended pattern: Narrow core + JSONB “source_payload”
- transactions (narrow, analytics-friendly):
  - Core columns only: account_id, txn_date, amount, balance, currency?, source_uid?, description?, ingestion_ts, ingestion_session_id, source_name, dupe, soft_deleted
- transactions_payload (or a jsonb column on transactions):
  - source_payload jsonb: Original bank columns as-is (keys and values verbatim)
  - source_schema_version: semantic version of handler/profile used
- Benefits:
  - No explosion of physical columns; you retain full fidelity in JSONB.
  - You can reconstruct bank-native exports by selecting from core + projecting source_payload keys.
  - Allows per-bank evolution without schema migrations.
- Indexing strategy:
  - Create GIN index on source_payload for ad-hoc queries.
  - Promote frequently-used fields (e.g., counterparty_acct) to first-class columns when they become operationally important.
- ETL and projection:
  - Build views to present “standardized details” plus selected source fields:
    - vw_transactions_standardized with concatenated details
    - vw_transactions_kiwibank_export projecting Kiwibank’s native column layout from JSONB
- Governance:
  - Do not use source_payload fields for identity dedupe.
  - Keep payload write-once; any normalization happens in the core columns.

Outcome
- You keep the database lean and analytics-friendly while remaining lossless and export-capable.
- You avoid schema sprawl and can still reconstruct bank-native formats on demand.