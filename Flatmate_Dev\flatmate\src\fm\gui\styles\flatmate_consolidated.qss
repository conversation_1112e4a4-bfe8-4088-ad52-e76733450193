/* Base Theme Stylesheet (consolidated) */
/* Extracted from actual working debug_combined_output.qss */
/* Generated: 2025-08-05 - REAL WORKING COLORS */
/* Baseline file: base_theme.qss */

/* Global styles - CORRECTED FONT SYSTEM */
* {
    font-family: ".AppleSystemUIFont", "Helvetica Neue", Arial, sans-serif;
    font-size: {{FONT_SIZE}}px;  /* Dynamic font size replacement - RESTORED */
    /* Keep global minimal to avoid breaking widgets */
}

QWidget {
    background-color: #1E1E1E;  /* BACKGROUND_DARK - actual working color */
    color: #FFFFFF;
}

/* Main panels - CORRECTED COLORS */
#left_panel, #right_panel {
    background-color: #1a381f;  /* NAV_BACKGROUND - actual working color (not #1a382f) */
    min-width: 150px;
}

#right_side_bar {
    background-color: #1a381f;  /* NAV_BACKGROUND - actual working color */
}

#left_panel QLabel {
    background: transparent;
}

/* Labels - ACTUAL WORKING DEFINITIONS */
QLabel {
    color: white;
    font-size: 1em;  /* Same as base - actual working */
}

/* Inline info labels - minimal spacing */
QLabel[type="info_label"] {
    color: #CCCCCC;
    margin: 0px;
    padding: 0px;
}

/* Source slot label: ensure no extra padding even under #content_area */
QLabel#src_slot_message {
    margin: 0px;
    padding: 0px;
}

/* Headings - ACTUAL WORKING STYLING */
QLabel#heading {
    color: white;
    font-size: 1.7em;        /* 24px -> 1.7em - actual working */
    font-weight: bold;
    margin-bottom: 10px;
}

/* Subheadings - ACTUAL WORKING STYLING */
QLabel#lbl_panel_subheading,
QLabel#subheading {  /* Keep old name for backward compatibility */
    color: #CCCCCC;         /* Explicit calm-white color - actual working */
    font-size: 1.3em;       /* 18px -> 1.3em - actual working */
    font-weight: bold;
    margin-top: 15px;
    margin-bottom: 5px;
}

/* Guide Pane sections: tighter header spacing */
QWidget#gp2_section QLabel#lbl_panel_subheading {
    margin-top: 8px;
    margin-bottom: 0px; /* no gap under header */
}

/* Guide Pane sections: remove default content_area label padding */
QWidget#gp2_section QLabel {
    padding: 0px;
    margin: 0px;
}

/* Content Area Labels - ACTUAL WORKING STYLING */
#content_area QLabel {
    background: transparent;
}

#content_area {
    background-color: #1E1E1E;  /* BACKGROUND_DARK - actual working color */
    padding: 1.5em;            /* Consistent padding from working system */
}

/* Center Panel */
#center_panel_content {
    background-color: #1E1E1E;  /* BACKGROUND_DARK - actual working color */
    padding: 0;                 /* No padding from working system */
    border: none;
}

/* Action Buttons - ACTUAL WORKING STYLING */
QPushButton[type="action_btn"] {
    background-color: #3B8A45;  /* PRIMARY - actual working color */
    color: white;
    border: none;
    border-radius: 8px;       /* Larger radius - actual working */
    padding: 8px 16px;        /* More padding - actual working */
    height: 45px;             /* Taller - actual working */
    font-size: 1.15em;        /* 16px -> 1.15em - actual working */
    font-weight: bold;
}

QPushButton[type="action_btn"]:hover {
    background-color: #4BA357;  /* PRIMARY_HOVER - actual working color */
}

QPushButton[type="action_btn"]:pressed {
    background-color: #2E6E37;  /* PRIMARY_PRESSED - actual working color */
}

/* Navigation Buttons */
QPushButton[type="nav_btn"] {
    background-color: #3B7443;  /* Secondary - actual working color */
    color: white;
    border: none;
    border-radius: 6px;
    padding: 6px;
    height: 35px;
}

QPushButton[type="nav_btn"]:hover {
    background-color: #488E52;  /* Actual working hover color */
}

QPushButton[type="nav_btn"]:pressed {
    background-color: #2E5A35;  /* Actual working pressed color */
}

/* Select Buttons */
QPushButton[type="select_btn"] {
    background-color: #3B7443;  /* Same as secondary - actual working color */
    color: white;
    border: none;
    border-radius: 6px;
    padding: 6px;
    height: 35px;
}

QPushButton[type="select_btn"]:hover {
    background-color: #4B8453;  /* Actual working hover color */
}

QPushButton[type="select_btn"]:pressed {
    background-color: #2E6436;  /* Actual working pressed color */
}

/* Exit Buttons */
QPushButton[type="exit_btn"] {
    background-color: #2E5A35;  /* Darker base color - actual working */
    color: #E0E0E0;            /* Slightly dimmer text - actual working */
    border: none;
    border-radius: 6px;
    padding: 6px;
    height: 35px;
}

QPushButton[type="exit_btn"]:hover {
    background-color: #386B3F;  /* Darker hover - actual working */
}

QPushButton[type="exit_btn"]:pressed {
    background-color: #264B2C;  /* Darker pressed - actual working */
}

/* File Tree - ACTUAL WORKING COLORS */
#file_tree {
    border: 1px solid #333333;
    border-radius: 4px;
    background-color: #202020;  /* Dark background for file display - actual working */
    color: #FFFFFF;
}

#file_tree::item {
    padding: 4px;
    border-bottom: 1px solid #333333;
    background: transparent;
}

#file_tree::item:selected {
    background-color: #3B7443;  /* Green selection - actual working color */
    color: #FFFFFF;
}

#file_tree::item:alternate {
    background-color: #242424;  /* Subtle alternate for dark theme - actual working */
}

#file_tree::item:hover {
    background-color: #242424;  /* Actual working hover color */
}

#file_tree QHeaderView::section {
    background-color: #242424;  /* Actual working header color */
    color: #FFFFFF;
    padding: 4px;
    border: none;
    border-right: 1px solid #333333;
    border-bottom: 1px solid #333333;
}

#file_tree QHeaderView {
    background-color: #242424;  /* Actual working header background */
}

/* Table View Toolbar - ACTUAL WORKING STYLING */
QFrame#TableViewToolbar {
    border: 1px solid #2A5A3A;  /* Very dark, muted green - actual working */
    border-radius: 4px;         /* Match file tree container radius - actual working */
}

/* Text Display - ACTUAL WORKING COLORS */
QTextEdit {
    background-color: #1E1E1E;  /* Actual working background */
    color: #D4D4D4;            /* Actual working text color */
    border: none;
}

QTextEdit::selection {
    background-color: #264F78;  /* Actual working selection color */
}

/* Table Views - ACTUAL WORKING COLORS */
QTableView {
    border: none;
    background-color: #1E1E1E;  /* Same as panel - actual working */
    color: white;
}

QTableView::item:alternate {
    background-color: #2A2A2A;  /* Actual working alternate row color */
}

QTableView::item:selected {
    background-color: #3E3E3E;  /* Actual working selection color */
}

/* Tree Widgets - ACTUAL WORKING COLORS */
QTreeWidget {
    border: none;
    background-color: #1E1E1E;  /* Actual working background */
    color: white;
}

QTreeWidget::item:alternate {
    background-color: #2A2A2A;  /* Actual working alternate color */
}

QTreeWidget::item:selected {
    background-color: #3B7443;  /* Green selection to match app theme - actual working */
    color: white;
}

QTreeWidget::item:hover {
    background-color: #2A2A2A;  /* Actual working hover color */
}

QHeaderView::section {
    background-color: #333333;  /* Actual working header color */
    color: white;
    padding: 4px;
    border: none;
    border-right: 1px solid #555555;
    border-bottom: 1px solid #555555;
}

/* Input Fields */
QLineEdit {
    background-color: #202020;
    border: 1px solid #333333;
    border-radius: 3px;
    padding: 4px;
    color: #FFFFFF;
}

QLineEdit:focus {
    border: 1px solid #3B8A45;
}

/* Combo Boxes - ACTUAL WORKING STYLING */
QComboBox {
    background-color: #2C2C2C;  /* BACKGROUND - actual working color */
    color: white;
    border: 1px solid #3B8A45;  /* PRIMARY - actual working border */
    border-radius: 4px;         /* Actual working radius */
    padding: 3px;               /* Reduced padding for compact layout */
}

QComboBox:hover {
    background: rgba(255, 255, 255, 0.1);  /* Actual working hover effect */
}

QComboBox::drop-down {
    border: none;  /* Actual working - simplified */
}

QComboBox::down-arrow {
    border: none;  /* Actual working - simplified */
}

QComboBox QAbstractItemView {
    background-color: #1E1E1E;  /* Actual working dropdown background */
    color: white;
    border: 1px solid #333333;
    selection-background-color: #3B8A45;
}

QComboBox QAbstractItemView::item:selected {
    background-color: #3B8A45;  /* PRIMARY - actual working selection */
    color: white;
}

/* Checkboxes - ACTUAL WORKING STYLING */
QCheckBox {
    color: white;                    /* Actual working text color */
    background-color: transparent;   /* Actual working background */
    spacing: 5px;                   /* Actual working spacing */
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 1px solid #3B8A45;     /* PRIMARY - actual working border */
    border-radius: 3px;
    background-color: #2C2C2C;     /* BACKGROUND - actual working */
}

QCheckBox::indicator:hover {
    background-color: rgba(59, 138, 69, 0.2);  /* PRIMARY with transparency - actual working */
    border-color: #4BA357;         /* PRIMARY_HOVER - actual working */
}

QCheckBox::indicator:checked {
    background-color: #3B8A45;     /* PRIMARY - actual working */
    border-color: #3B8A45;
}

QCheckBox::indicator:checked:hover {
    background-color: #4BA357;     /* PRIMARY_HOVER - actual working */
    border-color: #4BA357;
}

QCheckBox::indicator:pressed {
    background-color: #2E6E37;     /* PRIMARY_PRESSED - actual working */
    border-color: #2E6E37;
}

/* Scrollbars - ORIGINAL SUBTLE STYLING */
QScrollBar:vertical {
    background-color: #1C2A3A;  /* Original subtle dark blue-gray */
    width: 10px;                /* Original width */
}

QScrollBar::handle:vertical {
    background-color: #2C3A4A;  /* Original subtle handle color */
    min-height: 20px;
    border-radius: 5px;         /* Original radius */
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;                /* Original - no arrow buttons */
}

QScrollBar:horizontal {
    background-color: #1C2A3A;  /* Original subtle dark blue-gray */
    height: 10px;               /* Original height */
}

QScrollBar::handle:horizontal {
    background-color: #2C3A4A;  /* Original subtle handle color */
    min-width: 20px;
    border-radius: 5px;         /* Original radius */
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
    width: 0px;                 /* Original - no arrow buttons */
}

/* Toolbar Buttons */
QToolButton {
    background-color: transparent;
    border: none;
    padding: 4px;
    margin: 2px;
    color: #FFFFFF;
    border-radius: 4px;
}

QToolButton:hover {
    background-color: #3B7443;
}

QToolButton:pressed {
    background-color: #2E5A35;
}

/* Status Bar */
QStatusBar {
    background-color: #242424;
    color: #FFFFFF;
    border-top: 1px solid #333333;
}

/* Menu Bar */
QMenuBar {
    background-color: #1E1E1E;
    color: #FFFFFF;
    border-bottom: 1px solid #333333;
}

QMenuBar::item {
    background-color: transparent;
    padding: 4px 8px;
}

QMenuBar::item:selected {
    background-color: #3B7443;
}

/* Menus */
QMenu {
    background-color: #242424;
    color: #FFFFFF;
    border: 1px solid #333333;
}

QMenu::item {
    padding: 4px 20px;
}

QMenu::item:selected {
    background-color: #3B7443;
}

/* Splitter */
QSplitter::handle {
    background-color: #333333;
}

QSplitter::handle:horizontal {
    width: 2px;
}

QSplitter::handle:vertical {
    height: 2px;
}

/* Progress Bar */
QProgressBar {
    background-color: #202020;
    border: 1px solid #333333;
    border-radius: 3px;
    text-align: center;
    color: #FFFFFF;
}

QProgressBar::chunk {
    background-color: #3B8A45;
    border-radius: 2px;
}

/* Tab Widget */
QTabWidget::pane {
    border: 1px solid #333333;
    background-color: #1E1E1E;
}

QTabBar::tab {
    background-color: #242424;
    color: #FFFFFF;
    padding: 6px 12px;
    border: 1px solid #333333;
    border-bottom: none;
}

QTabBar::tab:selected {
    background-color: #3B7443;
}

QTabBar::tab:hover {
    background-color: #488E52;
}
