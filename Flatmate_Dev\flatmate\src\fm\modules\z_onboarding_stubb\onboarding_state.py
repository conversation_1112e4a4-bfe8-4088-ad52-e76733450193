# PROPOSED - NEEDS REVIEW
# This is a proposed implementation of the onboarding state
# Currently disabled to prevent VS Code errors

from dataclasses import dataclass
from enum import Enum, auto
from typing import Optional, Dict, Any

class OnboardingStep(Enum):
    """Enum representing the different steps in the onboarding process."""
    WELCOME = auto()
    USER_TYPE = auto()
    BASIC_INFO = auto()
    COMPLETE = auto()

@dataclass
class OnboardingState:
    """Class to manage the state of the onboarding process."""
    
    current_step: OnboardingStep = OnboardingStep.WELCOME
    user_type: Optional[str] = None  # 'flatmate' or 'manager'
    data: Dict[str, Any] = None  # Storage for form data
    
    def __post_init__(self):
        if self.data is None:
            self.data = {}
    
    def next_step(self) -> OnboardingStep:
        """Move to the next step."""
        steps = [
            OnboardingStep.WELCOME,
            OnboardingStep.USER_TYPE,
            OnboardingStep.BASIC_INFO,
            OnboardingStep.COMPLETE
        ]
        current_index = steps.index(self.current_step)
        if current_index < len(steps) - 1:
            self.current_step = steps[current_index + 1]
        return self.current_step
    
    def previous_step(self) -> OnboardingStep:
        """Move to the previous step."""
        steps = [
            OnboardingStep.WELCOME,
            OnboardingStep.USER_TYPE,
            OnboardingStep.BASIC_INFO,
            OnboardingStep.COMPLETE
        ]
        current_index = steps.index(self.current_step)
        if current_index > 0:
            self.current_step = steps[current_index - 1]
        return self.current_step
    
    def set_user_type(self, user_type: str) -> None:
        """Set the user type."""
        if user_type not in ['flatmate', 'manager']:
            raise ValueError("User type must be either 'flatmate' or 'manager'")
        self.user_type = user_type
        self.data['user_type'] = user_type
    
    def store_data(self, key: str, value: Any) -> None:
        """Store form data."""
        self.data[key] = value
    
    def get_data(self, key: str) -> Any:
        """Retrieve form data."""
        return self.data.get(key)
