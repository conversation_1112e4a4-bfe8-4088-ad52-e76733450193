---
description: Audit matrix for events vs interface methods in Update Data UI system
---

# Events vs Interface – Audit Matrix

## Canonical Rule
- Interface: single-listener (Presenter↔View), synchronous, direct UI update or user intent.
- Event: multi-listener or genuinely asynchronous broadcast.

## Matrix

| Name | Emitter | Consumers | Nature | Use | Rationale | Action |
|---|---|---|---|---|---|---|
| ViewEvents.ENABLE_FILE_DISCOVERY_TOGGLED | `/_ui/ud_view.py` → `UpdateDataView.on_auto_queue_toggled()` | `/ud_presenter.py` → `UpdateDataPresenter._on_file_discovery_toggled()` | Sync, single-listener | Prefer Interface (ok as Event) | Presenter delegates only to `FileDiscoveryManager.set_discovery(folder, enabled)`; no duplication | Keep event for now; optional later: direct interface call |
| ViewEvents.FILE_LIST_UPDATED | `/_ui/_presenter/file_info_manager.py` → `FileInfoManager._emit_list_updated()` | `/_ui/ud_view.py` → `UpdateDataView.update_files_display()` | Multi-listener potential | Event | Enriched list broadcast; view renders; allows future observers | Keep as event |
| ViewEvents.FILES_DISCOVERED | Folder monitor callback bridged via `FileInfoManager._on_files_discovered()` | `/_ui/_presenter/file_info_manager.py` | Async, multi-source | Event | Discovery may originate outside presenter; manager gates by monitored folder | Keep as event |
| UpdateDataEvents.FILE_PROCESSING_COMPLETED (global) | Processing pipeline via `UpdateDataEventService.publish_processing_completed()` | Presenter/other modules | Async, multi-listener | Event | Cross-module notification | Keep as event |
| ViewEvents.ERROR_DIALOG_REQUESTED | Managers/services | `/_ui/ud_view.py` → `_on_dialog_requested()` | Async | Event | Uniform dialog handling; view-only consumer | Keep as event |
| ViewEvents.SUCCESS_DIALOG_REQUESTED | Managers/services | `/_ui/ud_view.py` → `_on_dialog_requested()` | Async | Event | Uniform dialog handling; view-only consumer | Keep as event |

## Interface Methods – Inventory

### View → Presenter (user intents; single-listener, sync)

| Method | Implemented In | Purpose | Recommended Use | Action |
|---|---|---|---|---|
| `on_auto_queue_toggled(folder: str, enabled: bool)` emits `ENABLE_FILE_DISCOVERY_TOGGLED` | `/_ui/ud_view.py` | User toggles discovery for a folder | Prefer direct interface; event acceptable | Optional: replace with `presenter.toggle_file_discovery(folder, enabled)` |
| Left panel option changes emit local intents (e.g., `SOURCE_SELECT_REQUESTED`) | `/_ui/ud_view.py` | Translate Qt signals to typed intents | Event (intra-module) | Keep as local bus intents |

### Presenter → View (UI updates; single-listener, sync)

| Method | Implemented In | Purpose | Recommended Use | Action |
|---|---|---|---|---|
| `set_source_options(options: list[str], selected: str | None)` | `/_ui/ud_view.py` (called by `FileConfigManager`) | Populate left panel source options incl. MRU | Interface | Keep; call post-view init |
| `set_archive_options(options: list[str], selected: str | None)` | `/_ui/ud_view.py` (called by `FileConfigManager`) | Populate archive destination options | Interface | Keep |
| `update_files_display(files_data)` | `/_ui/ud_view.py` | Render enriched files from `FILE_LIST_UPDATED` | Event→View render | Keep |
| `set_process_enabled(enabled: bool)` / `set_process_button_text(text: str)` | `/_ui/ud_view.py` | Control processing UI state | Interface | Keep |
| `set_save_select_enabled(enabled: bool)` / `set_archive_enabled(enabled: bool)` | `/_ui/ud_view.py` | Enable/disable archive controls | Interface | Keep |
| `get_save_option()` / `get_update_database()` | `/_ui/ud_view.py` | Reads used by presenter/managers | Interface | Keep |

### Presenter → Managers/Services (domain operations)

| Call | Target | Purpose | Recommended Use | Action |
|---|---|---|---|---|
| `set_discovery(folder: str, enabled: bool)` | `/_ui/_presenter/file_discovery_manager.py` → `FileDiscoveryManager` | Persist and reconcile folder monitoring | Interface | Presenter must only delegate |
| `set_files(file_paths: list[str], source_path: str)` | `/_ui/_presenter/file_info_manager.py` → `FileInfoManager` | Enrich files and emit `FILE_LIST_UPDATED` | Interface | Keep |
| `add_files(file_paths: list[str])` | `/_ui/_presenter/file_info_manager.py` → `FileInfoManager` | Add files to canonical list | Interface | Keep |
| MRU updates (`_update_recent_folders`) | `/_ui/_presenter/file_config_manager.py` | Maintain/persist recent folders | Interface | Keep |
| Directory info (`DirectoryInfoService`) | `fm/core/directory/services/directory_info_service.py` | Read/write per-folder discovery/archive info | Interface | Keep |

## Notes
- Prefer interface methods for all single-listener, synchronous flows between View and Presenter.
- Use events only when multiple components must react (e.g., processing completed) or for async dialog results with multiple consumers.
  

## Notes & Next Steps
- Inventory remaining events and interface calls across:
  - `services/events.py`, `_ui/ui_events.py`, `services/local_event_bus.py`
  - `ud_presenter.py`, `_ui/_presenter/**/*.py`, `_ui/_view/**/*.py`
- For each, fill a row with actual locations and final decision.
- Minimal refactors (proposed):
  - `ud_presenter._on_file_discovery_toggled`: resolve folder, delegate `FileDiscoveryManager.set_monitored(folder, enabled)`; no extra logic.
  - If no other listeners for discovery toggle: remove subscription and wire `view -> presenter.toggle_file_discovery(enabled)`.
  - Ensure view subscribes to `FILE_LIST_UPDATED` only if truly multi-listener; otherwise presenter drives view updates via interface.

## Acceptance
- No presenter duplication of manager responsibilities.
- Interface/events usage matches the canonical rule.
- Documented decisions are reflected by small, targeted patches.
