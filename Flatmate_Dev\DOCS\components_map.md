# Components Map

Scope: Full project. Hybrid style with targeted snippets and inline references for critical components.

## Top-Level Structure
- Core services and utilities → flatmate/src/fm/core/
- GUI framework and shared components → flatmate/src/fm/gui/
- Feature modules (domain functionality) → flatmate/src/fm/modules/
- Data services, standards, database → flatmate/src/fm/core/data_services/, flatmate/src/fm/core/database/
- Protocols and documentation → flatmate/DOCS/, DOCS/

## Core Components
- Configuration
  - Path: flatmate/src/fm/core/config/config.py
  - Role: global configuration manager (defaults, preferences, keys/enums)
- Logging
  - Path: flatmate/src/fm/core/services/logger.py (or equivalent)
  - Role: unified logging interface with configurable verbosity
- Event Bus
  - Path: flatmate/src/fm/core/services/event_bus.py
  - Role: pub/sub messaging for decoupled communication
- Query Processing
  - Path: flatmate/src/fm/core/services/query_processing/
  - Role: structured query orchestration/patterns

## Data Layer
- Standards (columns, schemas)
  - Path: flatmate/src/fm/core/data_services/standards/
  - Role: canonical column definitions, naming conventions
- Database
  - Path: flatmate/src/fm/core/database/
  - Subdirs: migrations/, sql_repository/
  - Role: persistence and SQL abstractions

## GUI Layer
- Main Window
  - Path: flatmate/src/fm/gui/main_window.py (or components entry)
  - Role: top-level composition, panel management
- Shared Components
  - Path: flatmate/src/fm/gui/_shared_components/
  - Role: reusable widgets, table view v2, toolbars, utils
- Toolbars and Search/Filter
  - Paths described in flatmate/DOCS/_ARCHITECTURE/_TABLE_VIEW_SYSTEM/
  - Role: table interactions, query controls

## Feature Modules
- Update Data
  - Path: flatmate/src/fm/modules/update_data/
  - Role: statement ingestion, transformation, metadata, DB updates, UI for operations
- Other Modules
  - Home, Categorize, etc., organized under flatmate/src/fm/modules/

## Coordination
- Module Coordinator
  - Path: flatmate/src/fm/module_coordinator.py
  - Role: transitions, module lifecycle management

## Targeted Snippets (indicative)
- Event publication/subscribe patterns centralized via event_bus
- Config access patterns (ensure defaults, user prefs retrieval)
- Module transition hooks through coordinator (moving toward event-based)