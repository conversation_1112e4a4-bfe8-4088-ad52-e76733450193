"""CenterPanelLayout: pure layout/container for Update Data module.

Scope:
- Composite container owning the vertical splitter with GuidePane (top) and UDFileView (bottom).
- No domain/user-intent signal emission or translation. The UpdateDataView is the switchboard and bridges
  widget Qt signals to the Local Event Bus.
- Provides a minimal interface for View/Presenter to target UI updates: set_files, get_files,
  display_enriched_file_info, display_welcome, show_error, show_success.
"""


from fm.core.services.event_bus import Events, global_event_bus
from PySide6.QtWidgets import QVBoxLayout, QSplitter
from PySide6.QtCore import Qt, QSettings
import pandas as pd

from fm.gui._shared_components.base.base_panel_component import BasePanelComponent

from .center_panel_components import UDFileView, GuidePaneV2

from fm.core.services.logger import log
from fm.core.config.paths import AppPaths


class CenterPanelLayout(BasePanelComponent):
    """Main center panel layout for the Update Data module.

    This class uses the Composite pattern to manage different panes
    that can be shown in the center area of the Update Data module.
    """

    # Signals removed: domain/user-intent propagation is handled by UpdateDataView via Local Event Bus

    def __init__(self, parent=None):
        """Initialize the center panel layout."""
        super().__init__(parent)
        self.info_bar = None
        self.event_bus = global_event_bus
        # Source metadata (for display only; MUST NOT filter file list)
        self._current_source_path: str = ""
        # Integration switch: use GuidePaneV2 by default
        self._use_guide_pane_v2: bool = True
        self._init_ui()
        # Purposely no signal wiring here; UpdateDataView is the switchboard
        # UI prefs: prepare settings handle (widgets-only, isolated from domain config)
        try:
            AppPaths.ensure_dir_exists(AppPaths.CONFIG_DIR)
            self._ui_prefs = QSettings(str(AppPaths.CONFIG_DIR / "ui_prefs.ini"), QSettings.Format.IniFormat)
        except Exception as e:
            self._ui_prefs = None
            log.debug(f"[CenterPanelLayout] UI prefs disabled (cannot init QSettings): {e}")

    def _init_ui(self):
        """Initialize the UI components with resizable split layout."""
        # Main layout
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)

        # Create vertical splitter for guide pane (top) and file display (bottom)
        self.splitter = QSplitter(Qt.Vertical, self)
        self.main_layout.addWidget(self.splitter)

        self._create_panes()

        # Layout behavior:
        # - Guide pane area stays tight; readable padding is around its content only
        # - File view expands to take remaining space
        try:
            from PySide6.QtWidgets import QSizePolicy
            self.splitter.setStretchFactor(0, 0)  # Guide container: minimal stretch
            self.splitter.setStretchFactor(1, 1)  # File view: expands
            # Allow the guide pane to collapse to content height
            self.splitter.setCollapsible(0, True)
        except Exception:
            pass

        # Bias sizes to favor file view; guide area should match content height
        # Try restore from UI prefs; fallback to sizeHint-based height for guide pane
        # Always start with guide height = its sizeHint, to avoid blank space
        guide_h = max(1, int(self.guide_pane.sizeHint().height()))
        self.splitter.setSizes([guide_h, max(400, 1200 - guide_h)])

    def _create_panes(self):
        """Create panes and add them to the splitter."""
        # Guide pane (top half) - contextual messages and instructions
        # Ensure readable padding on the guide pane without affecting the file view expansion
        from PySide6.QtWidgets import QVBoxLayout, QWidget, QVBoxLayout as _QVBL, QSizePolicy
        self._guide_wrapper = QWidget(self.splitter)
        self._guide_wrapper_layout = QVBoxLayout(self._guide_wrapper)
        self._guide_wrapper_layout.setContentsMargins(0, 0, 0, 0)  # guide pane owns its own external padding
        self._guide_wrapper_layout.setSpacing(0)
        # Make wrapper size-to-contents vertically
        self._guide_wrapper.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Minimum)
        self._guide_wrapper_layout.setSizeConstraint(_QVBL.SizeConstraint.SetMinimumSize)
        # Create guide pane (V2 only)
        self.guide_pane = self._create_guide_pane_widget()
        self._guide_wrapper_layout.addWidget(self.guide_pane)
        self.splitter.addWidget(self._guide_wrapper)

        # File display pane (bottom half) - file list and management
        self.ud_file_view = UDFileView()
        self.splitter.addWidget(self.ud_file_view)

        # Ensure file pane expands
        try:
            from PySide6.QtWidgets import QSizePolicy
            self.ud_file_view.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        except Exception:
            pass

    def _create_guide_pane_widget(self):
        """Factory for guide pane widget honoring the integration switch."""
        # V2 is the only supported guide pane in this refactor
        return GuidePaneV2()

    def use_guide_pane_v2(self, enabled: bool = True) -> None:
        """Switch between legacy GuidePane and GuidePaneV2 at runtime."""
        if enabled == getattr(self, "_use_guide_pane_v2", False):
            return
        self._use_guide_pane_v2 = enabled
        try:
            if hasattr(self, "_guide_wrapper_layout") and self._guide_wrapper_layout is not None:
                # Remove existing widget
                if hasattr(self, "guide_pane") and self.guide_pane is not None:
                    try:
                        self.guide_pane.setParent(None)
                    except Exception:
                        pass
                # Create and insert new widget
                self.guide_pane = self._create_guide_pane_widget()
                self._guide_wrapper_layout.addWidget(self.guide_pane)
        except Exception as e:
            log.debug(f"[CenterPanelLayout] Non-critical: failed to switch guide pane: {e}")

    def closeEvent(self, event):
        """Persist splitter sizes to UI prefs on close."""
        try:
            if self._ui_prefs and hasattr(self, "splitter") and self.splitter is not None:
                sizes = self.splitter.sizes()
                # Ensure list of ints
                if isinstance(sizes, list) and all(isinstance(x, int) for x in sizes):
                    self._ui_prefs.setValue("update_data/center_splitter", sizes)
        except Exception as e:
            log.debug(f"[CenterPanelLayout] Failed to save UI splitter sizes: {e}")
        # Call parent to continue normal close handling
        try:
            super().closeEvent(event)
        except Exception:
            pass

    # === Public interface used by UpdateDataView ===
    def set_files(self, file_paths: list[str], source_path: str = "") -> None:
        """
        Set files into the UDFileView. Source path is metadata only and MUST NOT be used to filter.
        This method is invoked via UDView.update_files_display on FILE_LIST_UPDATED events.
        """
        try:
            self._current_source_path = source_path or ""
            if hasattr(self, "ud_file_view") and self.ud_file_view is not None:
                # Forward the entire list as-is
                self.ud_file_view.set_files(file_paths)
                log.debug(f"[CENTER_PANEL] set_files -> forwarded {len(file_paths)} files (source='{self._current_source_path}')")
            else:
                log.warning("[CENTER_PANEL] ud_file_view not available to set files")
        except Exception as e:
            log.error(f"[CENTER_PANEL] Error in set_files: {e}")

    def get_files(self) -> list[str]:
        """Get current files from the UDFileView."""
        try:
            if hasattr(self, "ud_file_view") and self.ud_file_view is not None:
                return self.ud_file_view.get_files()
        except Exception as e:
            log.error(f"[CENTER_PANEL] Error getting files: {e}")
        return []

    def set_source_path(self, source_path: str) -> None:
        """Optionally display the source path in the guide pane; never filter files."""
        try:
            self._current_source_path = source_path or ""
            if hasattr(self, "guide_pane") and self.guide_pane is not None and self._current_source_path:
                # Non-intrusive: show hint message; avoid overwriting other guide content if not empty
                try:
                    if hasattr(self.guide_pane, "set_section_info"):
                        self.guide_pane.set_section_info("source", f"Source: {self._current_source_path}")
                    elif hasattr(self.guide_pane, "display"):
                        self.guide_pane.display(f"Source: {self._current_source_path}")
                except Exception:
                    pass
        except Exception as e:
            log.debug(f"[CenterPanelLayout] Non-critical: set_source_path failed: {e}")

    def display_enriched_file_info(self, file_info_list) -> None:
        """Forward enriched file info to the UDFileView."""
        try:
            if hasattr(self, "ud_file_view") and self.ud_file_view is not None:
                self.ud_file_view.display_enriched_file_info(file_info_list)
        except Exception as e:
            log.error(f"[CENTER_PANEL] Error displaying enriched file info: {e}")
