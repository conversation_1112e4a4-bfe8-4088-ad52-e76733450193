# Task 1 

# Get a report on the qt system [x]
~~consider converting to qml~~
done - qml is bs, stuck with qapplication and qss 
refactored qss system... one sheet [x]


# TODO: 
## Implement remove button enablement (local, simple) []
  - Caller (View) computes has_files = (file_count &gt; 0) and calls [add_remove_btns.py.set_remove_enabled()](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/add_remove_btns.py:63) with has_files
  - Widget method signature:
    - def set_remove_enabled(self, has_files: bool) -&gt; None
  - Rationale: trivial rule, no event-bus plumbing

## Add typed UI events for publishing file-list snapshot (code) []
  - Define in [ui_events.py](flatmate/src/fm/modules/update_data/_ui/ui_events.py):
    - FileListSnapshot (files, file_count, total_size_bytes, extensions, has_files, last_updated_ts)
    - FileListUpdatedEvent(snapshot: FileListSnapshot)
  - Emit from Presenter/FileListManager after list mutations
  - Consumers (widgets/views) subscribe and use snapshot.has_files, file_count, etc.
  - Spec reference: [SPEC_FileListUpdatedEvent.md](flatmate/src/fm/modules/update_data/__UD_REFACTORING_docs_workspace/_FILE_LIST_UPDATED_EVENT/SPEC_FileListUpdatedEvent.md)

## Title bar handling (architecture follow-ups) 
  - Add “Toggle Full Screen” action to main window (cross-platform: showFullScreen/showNormal)
  - Optional Windows-only: enable immersive dark title bar via DwmSetWindowAttribute (no frameless)
  - Doc reference: [Window_TitleBar_Options.md](flatmate/DOCS/_ARCHITECTURE/_GUI/Window_TitleBar_Options.md)

- Create local PYSIDE6_QT_LIBRARY and scripted extractor
  - Path: flatmate/DOCS/_ARCHITECTURE/_GUI/PYSIDE6_QT_LIBRARY/
    - sources.json with URLs:
      - https://www.pythonguis.com/tutorials/custom-title-bar-pyqt6/
      - https://doc.qt.io/qtforpython-6/PySide6/QtGui/QPalette.html#PySide6.QtGui.QPalette.ColorGroup
    - tools/extract_to_markdown.py (requests + readability-lxml + markdownify)
    - extracted/custom-title-bar-pyqt6.md, extracted/qpalette-colorgroup.md (generated)
  - Behavior: re-runnable script to refresh content; include front-matter with source and fetched_at
  - Reference in architecture docs after generation

Useful Links and Docs
- Update-Data UI Deep-Dive: [Update-Data-UI-Deep-Dive.md](flatmate/DOCS/_GUIDES/Update-Data-UI-Deep-Dive.md)
- Typed UI events module: [ui_events.py](flatmate/src/fm/modules/update_data/_ui/ui_events.py)
- File list manager (emitter site): [file_list_manager.py](flatmate/src/fm/modules/update_data/_ui/_presenter/file_list_manager.py)
- Remove button widget: [add_remove_btns.py](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/add_remove_btns.py)
- FileListUpdatedEvent Spec: [SPEC_FileListUpdatedEvent.md](flatmate/src/fm/modules/update_data/__UD_REFACTORING_docs_workspace/_FILE_LIST_UPDATED_EVENT/SPEC_FileListUpdatedEvent.md)
- Title bar options (architecture): [Window_TitleBar_Options.md](flatmate/DOCS/_ARCHITECTURE/_GUI/Window_TitleBar_Options.md)
- External Sources for PYSIDE6_QT_LIBRARY:
  - Custom Title Bar (PyQt6/PySide): https://www.pythonguis.com/tutorials/custom-title-bar-pyqt6/
  - QPalette ColorGroup (PySide6 docs): https://doc.qt.io/qtforpython-6/PySide6/QtGui/QPalette.html#PySide6.QtGui.QPalette.ColorGroup

