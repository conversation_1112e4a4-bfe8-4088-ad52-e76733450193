import os
import sys
import pytest

# Basic pytest-qt configuration scaffold (can run headless if QT_QPA_PLATFORM=offscreen)
# Ensures project root on sys.path for imports when running from repo root.

@pytest.fixture(scope="session", autouse=True)
def _ensure_project_root_in_path():
    repo_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..", ".."))
    if repo_root not in sys.path:
        sys.path.insert(0, repo_root)
    yield


def pytest_addoption(parser):
    parser.addoption(
        "--offscreen",
        action="store_true",
        default=False,
        help="Run Qt in offscreen platform (headless)"
    )


@pytest.fixture(scope="session", autouse=True)
def _qt_offscreen(request, monkeypatch):
    if request.config.getoption("--offscreen"):
        # Use offscreen for headless execution
        monkeypatch.setenv("QT_QPA_PLATFORM", "offscreen")
    yield