# Architecture Index

Destination: DOCS (hard-overwrite). Style: Hybrid with targeted snippets for critical components.

## Documents Produced
- architecture_overview.md — Overview of system layers and critical components
- components_map.md — Inventory of core, GUI, modules, and data components
- data_flow.md — End-to-end data processing lifecycle and event integration
- key_workflows.md — Startup, navigation, ingestion, querying, logging, configuration

## Quick Links (by area)

Core
- Config: flatmate/src/fm/core/config/
- Event Bus: flatmate/src/fm/core/services/event_bus.py
- Logger: flatmate/src/fm/core/services/logger.py (or equivalent)
- Query Processing: flatmate/src/fm/core/services/query_processing/

Data
- Standards: flatmate/src/fm/core/data_services/standards/
- Database: flatmate/src/fm/core/database/
  - migrations/
  - sql_repository/

GUI
- Main Window: flatmate/src/fm/gui/main_window.py
- Shared Components: flatmate/src/fm/gui/_shared_components/
- Table View v2: flatmate/src/fm/gui/_shared_components/table_view_v2/
- Toolbars & Search: see flatmate/DOCS/_ARCHITECTURE/_TABLE_VIEW_SYSTEM/

Modules
- Update Data: flatmate/src/fm/modules/update_data/
- Module Coordinator: flatmate/src/fm/module_coordinator.py

Protocols & Docs
- Project Docs (this folder): DOCS/
- Architecture/Guides/Reports: flatmate/DOCS/
- Protocols: flatmate/DOCS/_PROTOCOLS/
- Reports: flatmate/DOCS/_REPORTS/

## Critical Components (Targeted Snippet Focus)
Include snippets and inline path references in the above documents for:
- Core Config (config manager, defaults, user prefs)
- Event Bus (publish/subscribe patterns)
- Module Coordinator (transition orchestration)
- GUI Main Window (top-level composition)
- Update Data Module (file → standards → DB pipeline)

## How to Use This Index
1) New contributor onboarding: read architecture_overview.md, then components_map.md
2) Data pipeline changes: start with data_flow.md and referenced standards/database paths
3) Navigation/decoupling work: review key_workflows.md (Module Transition), event_bus, module_coordinator
4) GUI enhancements: consult components_map.md and table view/toolbars docs in flatmate/DOCS/_ARCHITECTURE

## Maintenance Policy
- This index reflects current-state documentation. Update links when moving components.
- Keep hybrid snippets minimal and focused on usage patterns rather than exhaustive code copies.
- For large refactors, regenerate overview, components map, and workflows to maintain alignment.