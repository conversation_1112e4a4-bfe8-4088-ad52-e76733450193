# File Discovery – Status, Fix Applied, and Next Steps (2025-08-11 Session 1)

## What I fixed (minimal, surgical)
- Symptom: The Guide Pane "File discovery" checkbox reported checked=True as False. Logs showed `state=2, checked=False`.
- Root cause: Both DebugCheckBox and LabeledCheckBox compared `state == Qt.CheckState.Checked` (enum vs int mismatch). In this environment, that equality returned False even when the box was checked.
- Fix: Convert state to boolean using the widget, not the int:
  - DebugCheckBox._on_state_changed: `checked = self.isChecked()`
  - LabeledCheckBox._on_checkbox_state_changed: `checked = self.checkbox.isChecked()`
- File changed: `src/fm/gui/_shared_components/widgets/checkboxes.py`

## Evidence of improvement (post-fix runtime log)
- Log: `C:\Users\<USER>\.flatmate\logs\flatmate-20250812_014904.log`
- Observed chain when toggling ON:
  - `[DebugCheckBox] stateChanged ... state=2, checked=True`
  - `[LabeledCheckBox] stateChanged -> emit ... checked=True`
  - `[UD_VIEW] enable_file_discovery_toggled ... enabled=True`
  - `[FileDiscoveryManager] Persisted discovery ... enabled=True`
  - `[FolderMonitorService] Started monitoring folder ... (monitored: True)`
- Observed chain when toggling OFF mirrors correctly (monitored: False).

## Current status
- Checkbox: functional and truthy booleans propagate correctly.
- Presenter ↔ view signal throughput: operating as designed per handover doc.
- FolderMonitorService: responds to toggles (start/stop monitoring logged correctly).
- However: End-to-end "system isn’t actually working yet" — i.e., no evidence (in this log) of discovery events (created/modified files) flowing into the file list and/or processing pipeline after monitoring starts.

## How the system is supposed to work (brief)
1. User selects a folder or file set; Guide Pane binds `_current_folder`.
2. User toggles "File discovery"; presenter persists per-folder state.
3. FileDiscoveryManager reconciles FolderMonitorService monitoring for that folder.
4. FolderMonitorService emits events (initial scan + live creates/modifies).
5. FileInfoManager receives discovered paths, enriches them, and updates UDFileView via events.

Refs: `DOCS/file_discovery_handover.md` and runtime logs confirm the wiring through step 3.

## Likely gaps preventing end-to-end behavior
- Missing initial scan: The monitor starts, but there’s no explicit initial folder enumeration feeding into FileInfoManager. If the design relies on a queued initial scan when enabled, that piece may not be executing.
- Callback pathway: We register `FileInfoManager._on_files_discovered`, but we don’t see any `[FILE_INFO_MANAGER] on_files_discovered`-style logs. Either the monitor isn’t emitting, or the translation from OS events → FileListUpdatedEvent is incomplete.
- Event filtering: The watcher may be ignoring the bank file patterns in your test folder, or path normalization (slashes/case) may cause a filter miss.

## High-signal next steps (minimal, explicit)
1) Instrument the discovery event path to confirm emission
   - In `folder_monitor_service.py`, log each event yielded to the callback with the resolved path, event type, and whether it passed filters.
   - Confirm that enabling discovery triggers either:
     - An initial scan (expected log burst), or
     - Only live events (then simulate a rename/copy-in to verify).

2) Verify FileInfoManager callback is invoked
   - Ensure `FileInfoManager._on_files_discovered` has a top-level debug entry (e.g., `on_files_discovered: n files`) and logs the first few paths.
   - If not invoked, check the subscription site where the callback is registered and that the same instance is wired at runtime.

3) Add/confirm initial scan on enable (if required by UX)
   - When toggled ON for a folder, enqueue an initial directory listing and send it through the same callback.
   - Guard with a debounce to avoid duplicate listings.

4) Path normalization and filtering
   - Normalize to a single Windows style (e.g., use `Path.resolve()` and lower-case comparisons) across monitor, manager, and UI.
   - Explicitly log which filters/patterns are applied and their result per file.

5) Quick manual verification
   - With monitoring ON, drop a new CSV into `C:\Users\<USER>\Downloads\_flatmete_file_moniter_test`.
   - Expect: monitor event -> FileInfoManager enrichment -> UDFileView updated; guide pane shows source context unchanged.

## Concrete work plan (small PR sized)
- Patch 1 (observability-only; safe):
  - Add debug logs in FolderMonitorService around event emission and initial-scan (if present).
  - Add an entry log to FileInfoManager._on_files_discovered with count + first path.
- Patch 2 (functional if needed):
  - Implement initial scan upon enabling discovery for a folder and push through the same callback API.
- Patch 3 (hardening):
  - Normalize paths pre-filter and pre-persist; unify slashes in logs.

## Files involved (for these next steps)
- `src/fm/core/services/folder_monitor_service.py`
- `src/fm/modules/update_data/_ui/_presenter/file_discovery_manager.py`
- `src/fm/modules/update_data/_ui/_presenter/file_info_manager.py`
- (If filters exist) `src/fm/modules/update_data/pipeline/...` for pattern rules

## Appendix – Key log signatures
- Monitor start/stop:
  - `FolderMonitorService] Started/Stopped monitoring folder: ... (monitored: True|False)`
- UI state sync (present):
  - `GuidePaneV2] set_section_info(source)`, `show_source_context_options` and `set_enable_checked(programmatic)`
- Desired but missing in current log:
  - `FolderMonitorService] emitting discovered files ...`
  - `FileInfoManager] on_files_discovered: count=..., first='...'`



---

## Addendum – Working system, batching, UI threading, and data model (2025-08-12)

### What now works end-to-end
- Discovery toggle reports correct boolean (checkbox fix).
- Enabling discovery starts FolderMonitorService and performs an initial scan.
- Initial scan is batched: existing files are delivered in one shot to avoid "tick over".
- Live new files are discovered and added to the Update Data file view (no moving; not auto-import).
- Presenter state now syncs from FILE_LIST_UPDATED so the Guide Pane shows the correct folder and file count.
- UI updates are marshalled to the Qt main thread to prevent cross-thread crashes.

Key file changes:
- `src/fm/gui/_shared_components/widgets/checkboxes.py`: use `isChecked()` for truth.
- `src/fm/modules/update_data/_ui/_presenter/file_info_manager.py`:
  - `is_folder_monitored()` delegates to FolderMonitorService.
  - `add_files(..., source_path=...)` emits `FileListUpdatedEvent` with folder context.
- `src/fm/core/services/folder_monitor_service.py`:
  - Initial scan now batches files to the callback (`{'files': [...], 'source_folder': path}`).
  - Log wording: "File already handled by monitor, skipping" (no implication of DB import).
- `src/fm/modules/update_data/_ui/ud_view.py`:
  - Queued signal `files_display_update_requested` to marshal UI updates.
  - `_apply_files_display_update(...)` updates the file view and source hint on the UI thread and sets a friendly guide summary.
- `src/fm/modules/update_data/ud_presenter.py`:
  - Subscribes to `FILE_LIST_UPDATED` and aligns presenter state (folder + file list), then schedules `sync_state_to_view()` on the UI thread.

### Are we doing a delta add?
Yes, discovery performs delta additions into the in-memory file list:
- `FileInfoManager.add_files([...])` deduplicates against the existing `file_paths_list` and only enriches/merges new entries.
- This avoids duplicate display entries when a file is re-notified by the monitor.

### Does the hydrated model persist between sessions?
No, by design the enriched in-memory model (the "hydrated" `FileInfoData` list) does not persist across app sessions. On restart:
- The list is empty until files are set/added again.
- If discovery is enabled for a folder, the initial scan will re-discover files and re-hydrate the model on startup, populating the view quickly. This is intentional to keep the runtime model ephemeral and consistent with the current on-disk state.
- Persistent state used at startup:
  - Per-folder discovery preferences (`ud_config` via `UpdateDataKeys.FolderMonitoring`)
  - Recent folders (RecentFoldersService)
  - Other UI/config preferences (various config keys)

### What gets persisted (and what does not)
- Persisted:
  - Discovery enabled/disabled per folder (so monitors re-enable on next run)
  - Recent folders (for quick access in source options)
  - Save option preferences and related UI config
- Not persisted:
  - The in-memory list of enriched files (`file_info_list`) and plain paths (`file_paths_list`)
  - Any notion that a discovered file is "processed" — processing refers strictly to database import or CSV merge when the user clicks Process

### Why did logs say "processed" before?
- The monitor worker used the phrase "File already processed" to mean "this file path has already been handled by the monitor in this run". We've changed this wording to "already handled by monitor" to avoid confusion with your domain term "processed" (database import / merged CSV).

### UX recap aligned to your spec
- Discovery is not auto-import. Files are only listed for user approval.
- Enabling discovery in the Guide Pane now:
  - Immediately shows any existing files (batched initial scan)
  - Streams new files as they arrive
- If the app is not running: on startup, discovery-enabled folders are re-attached and an initial scan repopulates the list (prompting to review can be added next).

### Open refinements (can be added later)
- Startup poll prompt: offer to jump to Update Data if new files found in monitored folders since last run.
- MRU Quick Discovery: one-click "Add all from <MRU>" in source options (same enrichment path, no moving).
- Optional debounce for bursty live events (200–300 ms) if needed.

### Developer notes
- Batching only changes how we publish discovered files; it does not change processing semantics.
- `FileInfoManager.add_files` dedupes by exact string path. If cross-platform normalization becomes relevant, we can normalize paths at insert time.
- All UI changes are now performed on the Qt main thread either by the view’s queued signal or presenter’s QTimer scheduling, preventing QObject parent/child threading errors.
