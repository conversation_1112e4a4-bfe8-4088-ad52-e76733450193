# Gap Analysis and Recommended Fixes

Purpose: Identify concrete violations, double-handling, and bypasses; prescribe targeted, low-blast-radius fixes aligned to the Target Contract and Architectural Rules.

Legend
- V = Violation detected
- R = Remedy (fix)
- Risk = Low/Med/High
- Effort = S/M/L

References
- Presenter: [ud_presenter.py](../../../../update_data/ud_presenter.py)
- View + Interface: [\_ui/ud_view.py](../../_ui/ud_view.py), [\_ui/interface/i_view_interface.py](../../_ui/interface/i_view_interface.py)
- Panels: [center_panel_layout.py](../../_ui/_view/center_panel_layout.py), [left_panel_layout.py](../../_ui/_view/left_panel_layout.py), [guide_pane.py](../../_ui/_view/center_panel_components/guide_pane.py)
- Managers: [file_management.py](../../_ui/_presenter/file_management.py), [file_list_manager.py](../../_ui/_presenter/file_list_manager.py), [processing_manager.py](../../_ui/_presenter/processing_manager.py)
- Events: [local_event_bus.py](../../services/local_event_bus.py), [ui_events.py](../../_ui/ui_events.py)

1) Layout managers are still signal hubs (must be layout-only)
V1: CenterPanelManager re-emits UDFileView signals and handles signal wiring
- Evidence: [`center_panel_layout.py`](../../_ui/_view/center_panel_layout.py:68-84) connects file_pane signals and re-emits publish_file_selected, handles file_list_changed, processing_requested internally.

R1:
- Remove publish_* signal definitions and all re-emissions from CenterPanelManager.
- Expose UDFileView (file_pane) as a child component only; any cross-component connections move to the View (`UpdateDataView._connect_signals()`).
- Add view-level connections to UDFileView signals and re-emit only via interface signals if needed.
Risk: Low; Effort: M

V2: LeftPanelManager defines the complete interface signal set and legacy publish_* signals
- Evidence: [`left_panel_layout.py`](../../_ui/_view/left_panel_layout.py:24-33,74-97).
- Duplicates interface signals that belong to View; introduces legacy publish_* signals that invite double handling.

R2:
- Keep LeftPanelManager emitting only low-level widget signals internally and move domain-level emission to View.
- Decommission legacy publish_* signals (file/data/exit selected). Replace with view-level consolidated signals.
Risk: Low-Med; Effort: M

2) Presenter bypasses the interface boundary (tight coupling)
V3: Presenter wires signals directly on view subcomponents (file_pane, guide_pane)
- Evidence: [`ud_presenter.py`](../../../../update_data/ud_presenter.py:157-195).
- Presenter connects file_pane.publish_* and guide_pane.publish_toggle_folder_monitoring_requested to managers.

R3:
- Relocate these connections to UpdateDataView:
  - View subscribes to UDFileView and GuidePaneWidget signals.
  - View translates to either interface signals (for Presenter input) or emits local bus events (for display/manager outputs) based on Target Rules.
- Presenter then connects only to IUpdateDataView signals.
Risk: Med; Effort: M

3) Dual pathways for file list display updates (risk of duplicates)
V4: File list UI gets updated via two different routes
- Route A: FileListManager emits FILE_LIST_UPDATED → Presenter subscribes → Presenter calls view.set_files.
- Route B: Managers emit FILE_DISPLAY_UPDATED → View subscribes and updates display.
- Evidence: [`ud_presenter.py`](../../../../update_data/ud_presenter.py:231-244) subscribes to FILE_LIST_UPDATED and `_on_file_list_updated()` calls `view.set_files`, while [`ud_view.py`](../../_ui/ud_view.py:85-93) separately handles FILE_DISPLAY_UPDATED.

R4 (pick one canonical path):
- Option A (Recommended): View subscribes to FILE_LIST_UPDATED and updates display. Remove Presenter's `_on_file_list_updated` and its subscription. Managers that emit FILE_DISPLAY_UPDATED either switch to FILE_LIST_UPDATED or View subscribes to both but normalizes through a single handler.
- Option B: Keep FILE_DISPLAY_UPDATED as the sole UI update event; FileListManager emits FILE_DISPLAY_UPDATED instead of FILE_LIST_UPDATED for UI. Presenter unsubscribes from FILE_LIST_UPDATED and stops calling view.set_files.
Risk: Med; Effort: S-M

4) Guide Pane monitoring toggle handled through Presenter reaching into View
V5: Presenter connects guide pane publish_toggle_folder_monitoring_requested directly to FileListManager
- Evidence: [`ud_presenter.py`](../../../../update_data/ud_presenter.py:189-195).

R5:
- Move this connection to UpdateDataView; View emits a high-level signal (e.g., folder_monitor_toggle(bool, path?)) or directly emits a typed local bus event to FileListManager.
- If additional data (folder_path) is required, include it via state or view query.
Risk: Low; Effort: S

5) Interface surface vs concrete view does not fully align with usage
V6: Presenter expects methods like `set_files` on the View that are not explicitly standardized in the single-path contract
- Evidence: [`ud_presenter.py`](../../../../update_data/ud_presenter.py:336-339) uses `view.set_files()`.
- Interface includes set_files in the protocol, but target rules prefer View to react to bus events rather than Presenter driving set_files.

R6:
- If adopting R4 Option A (View subscribes to FILE_LIST_UPDATED), remove Presenter-driven set_files.
- Ensure `IUpdateDataView` contains the display update methods that View uses internally, but Presenter should not call display updates directly where event subscription is canonical.
Risk: Low; Effort: S

6) Processing flow feedback potential duplication
V7: ProcessingManager emits local dialog events while Presenter subscribes to global UpdateDataEvents; risk of double-notification
- Evidence: [`processing_manager.py`](../../_ui/_presenter/processing_manager.py:100-190) emits dialog events; Presenter separately handles global FILE_PROCESSING_* events.

R7:
- Define the responsibility cut:
  - Managers emit processing lifecycle on local bus for intra-module coordination and UI feedback.
  - Presenter bridges only when cross-module communication is needed (keep bridges in local_event_bus.setup_global_event_bridges).
- Avoid Presenter duplicating user-facing notifications if View already reacts to local dialog events.
Risk: Med; Effort: M

7) Legacy compatibility signals
V8: LeftPanelManager legacy publish_* signals (publish_file_selected, publish_data_selected, publish_exit_selected)
- Evidence: [`left_panel_layout.py`](../../_ui/_view/left_panel_layout.py:33-38,93-96).

R8:
- Mark deprecated; create a tracking ticket to remove. Immediately stop wiring them to Presenter. Ensure View emits canonical interface signals instead.
Risk: Low; Effort: S

Remediation sequencing (minimal blast radius)
Phase 1: Wiring and ownership (no behavior change intended)
- Move Presenter’s subcomponent signal connections into UpdateDataView (_connect_signals).
- Remove Presenter subscription to FILE_LIST_UPDATED and its `view.set_files` call; subscribe View to FILE_LIST_UPDATED instead (R4 Option A).
- Keep local_event_bus bridges as-is.

Phase 2: De-duplicate panel signals and remove middlemen
- Remove re-emissions from CenterPanelManager; ensure View connects UDFileView signals and re-emits at interface level as needed.
- Remove LeftPanelManager domain-level signals and legacy publish_*; maintain only widget-local signals; View maps them to interface signals.

Phase 3: Normalize event schema and dialog flows
- Ensure View reacts to typed dataclass events only; eliminate dict-based new emissions.
- Confirm single source for user-facing dialogs (prefer View listening to local bus dialog events; avoid Presenter triggering UI directly).

Acceptance criteria
- Presenter connects only to IUpdateDataView signals and methods; no direct file_pane/guide_pane access.
- Panels contain widgets and layout only; no domain signal definitions or re-emissions.
- Exactly one path updates file list display (documented in code).
- Local bus payloads are dataclasses (ui_events); no new dict formats.
- Tests/logging confirm no duplicate processing triggers and stable UI updates.

Quick diff hints (non-executable)
- Remove blocks resembling:
  - In presenter: checks like `if hasattr(self.view, 'center_display') ...` wiring to `file_pane` and `guide_pane`.
  - In CenterPanelManager/LeftPanelManager: class-level Signal declarations duplicating interface signals; lambda re-emit patterns; publish_* legacy signals.

Risk notes
- Import path tangles are minimal since we are not moving files; we are moving connections. Behavior should remain stable if interface signals are already present.
- Ensure order of event subscriptions is preserved (View subscriptions established before managers emit initial events).