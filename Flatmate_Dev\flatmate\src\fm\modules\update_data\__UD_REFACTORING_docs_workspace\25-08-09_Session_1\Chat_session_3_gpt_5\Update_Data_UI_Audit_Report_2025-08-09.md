# Update Data UI Audit Report — Session 1

Date: 2025-08-09
Author: <PERSON> (Architect persona)
Scope: Audit and refactor plan for Update Data UI system (focus on `ud_view.py`, left panel, event wiring, and architecture)

## Executive Summary
- The Update Data UI largely follows <PERSON> with a local event bus but retains legacy patterns.
- Left panel is now cleanly managed by `LeftPanelManager` with explicit Qt signals; no domain re-emission from the panel itself.
- Hard-coded option labels in widgets duplicate enum values in `models/config.py`—a drift risk.
- View-to-presenter intents are still routed via the local event bus; per the agreed architecture, they should move to interface methods where synchronous and single-listener.
- Broad try/except blocks in `ud_view.py` obscure errors and conflict with the “fail fast” policy.
- A runtime error prevents start-up: `UpdateDataView` is missing `_connect_center_pane_intents()` while `setup_ui()` calls it.

## System Map (Key Files)
- `.../_ui/ud_view.py` — Root view. Wires left/centre panes to local bus; hosts guide pane adapter/presenter; subscribes to render events.
- `.../_ui/_view/left_panel_layout.py` — `LeftPanelManager` with high-level Qt signals.
- `.../_ui/_view/left_panel_components/widgets/widgets.py` — Stateless widgets for left panel.
- `.../_ui/_view/center_panel_layout.py` — `CenterPanelManager`, exposes file pane(s) and guide pane.
- `.../services/local_event_bus.py` — `LocalEventBus` and `ViewEvents`.
- `.../models/config.py` — `SourceOptions`, `SaveOptions` enums (authoritative UI labels). 

## Findings
- **Left panel structure**: `LeftPanelManager` exposes UI-intent Qt signals (`source_select_requested`, `save_select_requested`, `process_clicked`, etc.). No domain logic or event re-emit—good separation.
- **Widget labels vs enums**: Widgets in `widgets.py` hard-code labels that match `SourceOptions` and `SaveOptions`. This is fragile; the authoritative source should be `models/config.py`.
- **Signal wiring in `ud_view.py`**:
  - `_connect_signals()` translates left panel Qt signals to `ViewEvents.*.value` on the local bus.
  - Source select normalisation attempts to map label text to `SourceOptions` values (enum).
  - Centre pane intent wiring emits `ViewEvents.ADD_FILES_REQUESTED.value` (distinct channel) — correct.
- **Event subscriptions**: View renders from `UI_STATE_CHANGED`, `STATUS_MESSAGE_CHANGED`, `FILE_DISPLAY_UPDATED`, and `FILE_LIST_UPDATED`.
- **Deprecated dialogue shims**: `_GuidePaneViewAdapter` includes legacy `show_error`, `show_success`, `show_files_dialog()` that re-route to event bus—removable once callers switch to dataclass events.
- **Error handling**: Numerous broad `try/except Exception` in `ud_view.py` around UI wiring/rendering—masks legitimate errors and contradicts the project rule.

## Issues and Risks
- **Startup failure (critical)**:
  - Error: `AttributeError: 'UpdateDataView' object has no attribute '_connect_center_pane_intents'`
  - Location: `ud_view.py: setup_ui()` calls `self._connect_center_pane_intents()` but the method is absent on `UpdateDataView`.
  - Likely cause: method moved/renamed or accidentally scoped into the adapter during refactors; call site not updated.
  - Impact: Application fails to load the Update Data module.
- **String drift risk**: Hard-coded widget labels (e.g., "Select entire folder...") vs authoritative enums in `models/config.py`.
- **Event/interface overlap**: Left panel user intents (single listener, synchronous) travel via the local bus, which adds complexity and splits contracts across interface and bus.
- **Obscured errors**: Broad try/except in the view hides real wiring mistakes and runtime regressions.

## Architecture Alignment (Actual vs Target)
- **Actual**: MVP-ish with local event bus for most view→presenter intents and presenter→view renders; presenter remains Qt-free via a view adapter for the guide pane.
- **Target (per agreed decisions)**:
  - Use interface methods for synchronous, single-listener interactions (e.g., left panel actions).
  - Reserve the event bus for multi-listener broadcasts and asynchronous flows (dialogue requests/results, state changes, file list updates).
  - Dataclass payloads for all bus messages; avoid stringly typed payloads.
  - Minimal, targeted exception handling; fail fast.

## Concrete Recommendations
1. **Fix startup regression (immediate)**
   - Define `UpdateDataView._connect_center_pane_intents()` or update `setup_ui()` to call the correct method.
   - Ensure the method lives on `UpdateDataView` (it depends on `self.center_display` and `self.local_bus`).
   - Keep event key: `ViewEvents.ADD_FILES_REQUESTED.value`.

2. **Unify labels with enums (low risk)**
   - In `widgets.py`, source option and archive option lists should derive from `models/config.SourceOptions` and `SaveOptions` `.value`s.
   - This removes the drift risk and centralises label control.

3. **Reduce broad exception handling in view (low risk)**
   - Remove/limit the `try/except Exception` in `ud_view.py` around UI wiring and rendering.
   - Keep logging only where catching is necessary; otherwise let errors surface.

4. **Interface-first for left panel intents (medium)**
   - Update the view interface and presenter to handle left panel user intents via direct interface methods (e.g., `on_source_select_requested(option)`, `on_save_select_requested()`, `on_process_clicked()`).
   - Remove the corresponding local bus emissions for these intents.
   - Retain local bus for multi-listener and async events (dialogues, file list updates, state broadcasts).

5. **Dataclass payloads for bus (medium)**
   - Standardise to dataclass events across remaining bus channels per `proposed_events.md/csv`.
   - Avoid mixed enum-or-string payload patterns; carry enums as fields in the dataclass when needed.

6. **Centre pane consolidation (optional, gated)**
   - If `file_pane_v2` is production-ready, make it the default and remove/deprecate the legacy `file_pane` wiring.

7. **Remove deprecated shims (cleanup)**
   - After callers are migrated, remove `_GuidePaneViewAdapter.show_error`, `.show_success`, `.show_files_dialog()`.

## Phased Plan
- **Phase 1 (today)**
  - Fix `_connect_center_pane_intents` regression so the app loads.
  - Derive left panel labels from enums in `models/config.py`.
  - Trim broad `try/except` in `ud_view.py` where they only log and continue.

- **Phase 2**
  - Implement interface methods for left panel user intents; refactor presenter to bind directly.
  - Remove duplicate local bus emissions for those intents.

- **Phase 3**
  - Enforce dataclass payloads for all bus messages (per `proposed_events.csv`).
  - Remove deprecated dialogue shims.
  - Decide and switch to `file_pane_v2` if ready; delete legacy paths.

## Testing Strategy
- Import smoke tests for `ud_view.py`, `ud_presenter.py` (already used, continue post-fix).
- UI interaction smoke tests: left panel clicks, source/save selections, process/cancel toggles.
- Event payload type checks (ensure dataclasses end-to-end).
- Regression test for `ADD_FILES_REQUESTED` channel wiring from centre pane.

## References (key paths)
- `src/fm/modules/update_data/_ui/ud_view.py`
- `src/fm/modules/update_data/_ui/_view/left_panel_layout.py`
- `src/fm/modules/update_data/_ui/_view/left_panel_components/widgets/widgets.py`
- `src/fm/modules/update_data/_ui/_view/center_panel_layout.py`
- `src/fm/modules/update_data/models/config.py`
- `src/fm/modules/update_data/services/local_event_bus.py`

## Immediate Action Items
- [ ] Implement `UpdateDataView._connect_center_pane_intents()` (or update call) to resolve startup error.
- [ ] Replace hard-coded widget option labels with enum `.value`s from `models/config.py`.
- [ ] Remove broad `try/except Exception` blocks that obscure errors in `ud_view.py`.
- [ ] Plan interface migration for left panel intents (align with `proposed_events.md/csv`).

---
If you want, I can apply Phase 1 now and re-run `fm` to validate the fix and confirm no further startup errors.
