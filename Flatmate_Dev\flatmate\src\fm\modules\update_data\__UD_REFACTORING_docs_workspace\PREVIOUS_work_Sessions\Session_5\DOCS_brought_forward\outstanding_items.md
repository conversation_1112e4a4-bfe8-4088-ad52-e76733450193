# Update Data Module - Outstanding Items

## High Priority
- [ ] **Fix guide pane UI updates**: Guide pane is not showing changes when files are discovered or enriched
- [ ] **Verify file handler recognition**: Ensure that file handler recognition is working correctly in the UI
- [ ] **Test folder monitoring integration**: Verify that folder monitoring correctly discovers and enriches new files

## Medium Priority
- [ ] **Add unit tests**: Create unit tests for folder monitoring integration and file enrichment
- [ ] **Improve error handling**: Add better error handling for file discovery and enrichment
- [ ] **Optimize file enrichment**: Look for opportunities to optimize file enrichment for large file sets

## Low Priority
- [ ] **Refactor event emissions**: Review event emissions to ensure they follow the architectural guidelines
- [ ] **Documentation**: Update documentation for folder monitoring integration
- [ ] **Code cleanup**: Remove any remaining commented or unused code

## Completed Items
- [x] Re-implement `_enrich_file_info` method
- [x] Re-implement `_handle_source_select` method
- [x] Fix `_handle_folder_monitor_file_discovered` method
- [x] Restore folder monitoring service integration

## Notes
- The guide pane UI update issue is the most critical item to fix in the next session
- File handler recognition may be working correctly but not displaying properly in the UI
- The folder monitoring service is properly initialized and callbacks are registered
