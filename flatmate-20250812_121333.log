2025-08-12 12:13:33 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-08-12 12:13:34 - [fm.core.services.folder_monitor_service] [INFO] - Started monitoring folder: C:\Users\<USER>\Downloads\_flatmete_file_moniter_test
2025-08-12 12:13:34 - [fm.core.services.folder_monitor_service] [INFO] - Initial scan complete for C:\Users\<USER>\Downloads\_flatmete_file_moniter_test: queued 14 file(s) (batched)
2025-08-12 12:13:34 - [fm.core.services.folder_monitor_service] [INFO] - FolderMonitorService initialized
2025-08-12 12:13:34 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-08-12 12:13:34 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-08-12 12:13:34 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-08-12 12:13:35 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded 0 component defaults for categorize from C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\categorize\config\defaults.yaml
2025-08-12 12:13:35 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 2 user preferences
2025-08-12 12:13:35 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-08-12 12:13:35 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-08-12 12:13:35 - [fm.main] [INFO] - Application starting...
2025-08-12 12:13:35 - [fm.gui.styles.applier] [INFO] - Styles: BASE_FONT_SIZE=14 [source=default]
2025-08-12 12:13:35 - [fm.gui.styles.loader] [INFO] - Styles: using consolidated stylesheet: flatmate_consolidated.qss  (path=C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\gui\styles\flatmate_consolidated.qss)
2025-08-12 12:13:35 - [fm.gui.styles.loader] [INFO] - Styles: BASE_FONT_SIZE=14  FONT_SIZE replacements=1
2025-08-12 12:13:35 - [fm.gui.styles.applier] [INFO] - Styles: applied stylesheet from: C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\gui\styles\flatmate_consolidated.qss
2025-08-12 12:13:38 - [fm.main] [INFO] - 
=== Initializing Database & Cache ===
2025-08-12 12:13:38 - [fm.core.data_services.db_io_service] [INFO] - Initializing DBIOService singleton...
2025-08-12 12:13:38 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-08-12 12:13:38 - [fm.core.database.sql_repository.cached_sqlite_repository] [DEBUG] - CachedSQLiteRepository initialized
2025-08-12 12:13:38 - [fm.core.database.sql_repository.cached_sqlite_repository] [INFO] - Warming transaction cache...
2025-08-12 12:13:39 - [fm.core.database.sql_repository.cached_sqlite_repository] [INFO] - Cache warmed successfully: 2166 transactions, 3 unique accounts in 0.72s
2025-08-12 12:13:39 - [fm.core.data_services.db_io_service] [INFO] - DBIOService initialized with cache: 2166 transactions
2025-08-12 12:13:39 - [fm.main] [INFO] - 
=== Initializing Auto-Import Manager ===
2025-08-12 12:13:39 - [fm.core.services.folder_monitor_service] [DEBUG] - Already monitoring folder: C:\Users\<USER>\Downloads\_flatmete_file_moniter_test
2025-08-12 12:13:39 - [fm.core.services.folder_monitor_service] [INFO] - Folder monitor worker thread started
2025-08-12 12:13:39 - [fm.core.services.folder_monitor_service] [INFO] - Folder monitor service started
2025-08-12 12:13:39 - [fm.main] [INFO] - 
=== Setting up Module Coordinator ===
2025-08-12 12:13:39 - [fm.module_coordinator] [INFO] - Initializing Module Coordinator
2025-08-12 12:13:39 - [fm.module_coordinator] [DEBUG] - Loaded recent modules: ['home', 'categorize', 'update_data']
2025-08-12 12:13:39 - [fm.module_coordinator] [INFO] - Creating all modules (eager loading)
2025-08-12 12:13:39 - [fm.modules.base.base_presenter] [DEBUG] - Initialized HomePresenter
2025-08-12 12:13:39 - [fm.modules.home.home_presenter] [DEBUG] - Home Presenter initialization complete
2025-08-12 12:13:39 - [fm.modules.base.base_presenter] [DEBUG] - Initialized UpdateDataPresenter
2025-08-12 12:13:39 - [fm.modules.update_data.ud_presenter] [DEBUG] - log level set to debug in ud_presenter.py
2025-08-12 12:13:39 - [fm.modules.update_data.ud_presenter] [DEBUG] - UPDATE_DATA: Debug logging enabled for console output
2025-08-12 12:13:39 - [fm.modules.base.base_presenter] [DEBUG] - Initialized CategorizePresenter
2025-08-12 12:13:39 - [fm.module_coordinator] [INFO] - Setting up home module
2025-08-12 12:13:39 - [fm.modules.base.base_presenter] [INFO] - Setting up HomePresenter
2025-08-12 12:13:39 - [fm.modules.home.home_presenter] [DEBUG] - Connecting Home View signals
2025-08-12 12:13:39 - [fm.modules.base.base_presenter] [DEBUG] - HomePresenter setup complete
2025-08-12 12:13:39 - [fm.module_coordinator] [INFO] - Setting up update_data module
2025-08-12 12:13:39 - [fm.modules.base.base_presenter] [INFO] - Setting up UpdateDataPresenter
2025-08-12 12:13:39 - [fm.gui._shared_components.widgets.checkboxes] [DEBUG] - [DebugCheckBox] setChecked: label='Update Database', to=True, was=False, signalsBlocked=False
2025-08-12 12:13:39 - [fm.gui._shared_components.widgets.checkboxes] [DEBUG] - [DebugCheckBox] stateChanged: label='Update Database', state=2, checked=True, signalsBlocked=False
2025-08-12 12:13:39 - [fm.gui._shared_components.widgets.checkboxes] [DEBUG] - [DebugCheckBox] toggled: label='Update Database', checked=True, signalsBlocked=False
2025-08-12 12:13:39 - [fm.gui._shared_components.widgets.checkboxes] [DEBUG] - [DebugCheckBox] setChecked: label='File discovery', to=False, was=False, signalsBlocked=False
2025-08-12 12:13:39 - [fm.modules.update_data._ui._view.center_panel_components.ud_file_view] [DEBUG] - UDFileView component initialized
2025-08-12 12:13:39 - [fm.modules.update_data._ui.ud_view] [DEBUG] - [UD_VIEW] Wired UDFileView.add_files_requested -> add_files_requested
2025-08-12 12:13:39 - [fm.modules.update_data._ui._presenter.guide_pane_presenter] [DEBUG] - [GuidePanePresenter] Initialised
2025-08-12 12:13:39 - [fm.modules.update_data._ui._presenter.file_info_manager] [DEBUG] - [FILE_INFO_MANAGER] Initialized with empty enriched file list
2025-08-12 12:13:39 - [fm.modules.update_data._ui._presenter.file_config_manager] [DEBUG] - [FILE_CONFIG_MANAGER] Initialized with recent folders support and restored option: _flatmete_file_moniter_test
2025-08-12 12:13:39 - [fm.modules.update_data._ui._presenter.file_config_manager] [DEBUG] - [FILE_CONFIG_MANAGER] Subscribed to ADD_FILES_REQUESTED events
2025-08-12 12:13:39 - [fm.core.services.folder_monitor_service] [DEBUG] - Registered file discovery callback: FileInfoManager._on_files_discovered
2025-08-12 12:13:39 - [fm.modules.update_data.ud_presenter] [DEBUG] - Local bus subscriptions set up; Presenter has zero Qt signal knowledge
2025-08-12 12:13:39 - [fm.modules.base.base_presenter] [DEBUG] - UpdateDataPresenter setup complete
2025-08-12 12:13:39 - [fm.module_coordinator] [INFO] - Setting up categorize module
2025-08-12 12:13:39 - [fm.modules.base.base_presenter] [INFO] - Setting up CategorizePresenter
2025-08-12 12:13:40 - [fm.core.database.sql_repository.cached_sqlite_repository] [DEBUG] - Cache hit: returning 3 unique accounts
2025-08-12 12:13:40 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Initializing TransactionViewPanel
2025-08-12 12:13:40 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: TransactionViewPanel._init_ui
2025-08-12 12:13:40 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Setting up TransactionViewPanel UI
2025-08-12 12:13:40 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Creating CustomTableView_v2 for transactions
2025-08-12 12:13:40 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - TransactionViewPanel UI setup complete
2025-08-12 12:13:40 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: TransactionViewPanel._init_ui took 0.072s
2025-08-12 12:13:40 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Connecting TransactionViewPanel signals
2025-08-12 12:13:40 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - TransactionViewPanel signals connected
2025-08-12 12:13:40 - [fm.modules.categorize.cat_presenter] [DEBUG] - About to call _load_data_during_setup()
2025-08-12 12:13:40 - [fm.modules.categorize.cat_presenter] [DEBUG] - Loading data during setup (eager loading)
2025-08-12 12:13:40 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.database.last_account = None (Source: cat_presenter.py:_load_data_during_setup)
2025-08-12 12:13:40 - [fm.modules.categorize.cat_presenter] [INFO] - Auto-loading ALL transactions from database...
2025-08-12 12:13:40 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: CategorizePresenter._handle_load_db
2025-08-12 12:13:40 - [fm.modules.categorize.cat_presenter] [INFO] - Loading transactions from database for categorisation…
2025-08-12 12:13:40 - [fm.modules.categorize.cat_presenter] [DEBUG] - Filters: None
2025-08-12 12:13:40 - [fm.modules.categorize.cat_presenter] [DEBUG] - Fetching transactions with filters: {}
2025-08-12 12:13:40 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: Data Retrieval from Cache
2025-08-12 12:13:40 - [fm.modules.categorize.cat_presenter] [INFO] - Retrieved 2166 transactions as DataFrame
2025-08-12 12:13:40 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: Data Retrieval from Cache took 0.057s
2025-08-12 12:13:40 - [fm.modules.categorize.cat_presenter] [DEBUG] - DataFrame shape: (2166, 32), empty: False
2025-08-12 12:13:40 - [fm.modules.categorize.cat_presenter] [DEBUG] - DataFrame columns: ['account', 'amount', 'balance', 'category', 'credit_amount', 'date', 'db_uid', 'debit_amount', 'details', 'empty', 'hash', 'notes', 'op_account', 'op_code', 'op_name', 'op_part', 'op_ref', 'payment_type', 'source_bank', 'source_filename', 'source_type', 'source_uid', 'statement_date', 'tags', 'tp_code', 'tp_part', 'tp_ref', 'unique_id', 'id', 'import_date', 'modified_date', 'is_deleted']
2025-08-12 12:13:40 - [fm.modules.categorize.cat_presenter] [DEBUG] - First few rows:
              account  amount  ...        modified_date is_deleted
0  38-9004-0646977-04  -30.44  ...  2025-07-15 00:02:35          0
1  38-9004-0646977-04   -9.40  ...  2025-07-15 00:02:35          0
2  38-9004-0646977-04  -18.99  ...  2025-07-15 00:02:35          0
3  38-9004-0646977-04  -20.50  ...  2025-07-15 00:02:35          0
4  38-9004-0646977-00 -100.00  ...  2025-07-15 00:02:35          0

[5 rows x 32 columns]
2025-08-12 12:13:40 - [fm.modules.categorize.cat_presenter] [DEBUG] - Using DataFrame with shape: (2166, 32)
2025-08-12 12:13:40 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: Transaction Categorization
2025-08-12 12:13:40 - [fm.modules.categorize.cat_presenter] [DEBUG] - Applying categorization to transactions...
2025-08-12 12:13:40 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: Transaction Categorization took 0.046s
2025-08-12 12:13:40 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: CategorizePresenter._apply_default_sorting
2025-08-12 12:13:40 - [fm.modules.categorize.cat_presenter] [DEBUG] - Applied default sorting: date (descending)
2025-08-12 12:13:40 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: CategorizePresenter._apply_default_sorting took 0.006s
2025-08-12 12:13:40 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: Table View Data Setting
2025-08-12 12:13:40 - [fm.modules.categorize.cat_presenter] [DEBUG] - Setting DataFrame with 2166 transactions to view
2025-08-12 12:13:40 - [fm.modules.categorize._view.cat_view] [DEBUG] - CatView setting dataframe: 2166 rows
2025-08-12 12:13:40 - [fm.modules.categorize._view.cat_view] [DEBUG] - DataFrame columns: ['account', 'amount', 'balance', 'category', 'credit_amount', 'date', 'db_uid', 'debit_amount', 'details', 'empty', 'hash', 'notes', 'op_account', 'op_code', 'op_name', 'op_part', 'op_ref', 'payment_type', 'source_bank', 'source_filename', 'source_type', 'source_uid', 'statement_date', 'tags', 'tp_code', 'tp_part', 'tp_ref', 'unique_id', 'id', 'import_date', 'modified_date', 'is_deleted']
2025-08-12 12:13:40 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Setting transactions: 2166 rows
2025-08-12 12:13:40 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Using ordered display columns: ['date', 'details', 'amount', 'account', 'balance', 'category', 'tags', 'notes']
2025-08-12 12:13:40 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Reordered DataFrame columns: ['date', 'details', 'amount', 'account', 'balance', 'category', 'tags', 'notes', 'credit_amount', 'db_uid', 'debit_amount', 'empty', 'hash', 'op_account', 'op_code', 'op_name', 'op_part', 'op_ref', 'payment_type', 'source_bank', 'source_filename', 'source_type', 'source_uid', 'statement_date', 'tp_code', 'tp_part', 'tp_ref', 'unique_id', 'id', 'import_date', 'modified_date', 'is_deleted']
2025-08-12 12:13:43 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Displaying columns: ['Date', 'Details', 'Amount', 'Account', 'Balance', 'Category', 'Tags', 'Notes']
2025-08-12 12:13:43 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: Table View Data Setting took 3.472s
2025-08-12 12:13:43 - [fm.modules.categorize.cat_presenter] [INFO] - Successfully loaded and displayed 2166 transactions in 3.7s (592.0 txns/s)
2025-08-12 12:13:43 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: CategorizePresenter._handle_load_db took 3.674s
2025-08-12 12:13:43 - [fm.modules.categorize.cat_presenter] [DEBUG] - Data loading during setup complete
2025-08-12 12:13:43 - [fm.modules.base.base_presenter] [DEBUG] - CategorizePresenter setup complete
2025-08-12 12:13:43 - [fm.module_coordinator] [INFO] - All modules created and configured
2025-08-12 12:13:43 - [fm.module_coordinator] [DEBUG] - Available modules: ['home', 'update_data', 'categorize']
2025-08-12 12:13:43 - [fm.module_coordinator] [INFO] - Starting Application
2025-08-12 12:13:43 - [fm.module_coordinator] [INFO] - Transitioning from None to home
2025-08-12 12:13:43 - [fm.module_coordinator] [DEBUG] - Showing home module
2025-08-12 12:13:43 - [fm.modules.base.base_presenter] [INFO] - Showing HomePresenter
2025-08-12 12:13:43 - [fm.modules.base.base_module_view] [INFO] - Setting up HomeView in Main Window
2025-08-12 12:13:43 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Left Panel
2025-08-12 12:13:43 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Center Panel
2025-08-12 12:13:43 - [fm.modules.base.base_module_view] [INFO] - HomeView setup complete
2025-08-12 12:13:43 - [fm.modules.home.home_presenter] [DEBUG] - Refreshing Home content
2025-08-12 12:13:43 - [fm.modules.home.home_presenter] [DEBUG] - Home content refresh complete
2025-08-12 12:13:43 - [fm.modules.base.base_presenter] [DEBUG] - HomePresenter is now visible
2025-08-12 12:13:43 - [fm.module_coordinator] [INFO] - Successfully transitioned to home
2025-08-12 12:13:43 - [fm.main] [INFO] - 
=== Checking Auto-Import Status ===
2025-08-12 12:13:44 - [fm.main] [INFO] - Auto-import startup check temporarily disabled - using new state management
2025-08-12 12:13:44 - [fm.main] [INFO] - 
=== Application Ready ===
2025-08-12 12:13:50 - [fm.module_coordinator] [INFO] - Transitioning from HomePresenter to update_data
2025-08-12 12:13:50 - [fm.module_coordinator] [DEBUG] - Hiding HomePresenter
2025-08-12 12:13:50 - [fm.modules.base.base_presenter] [INFO] - Hiding HomePresenter
2025-08-12 12:13:50 - [fm.modules.base.base_module_view] [DEBUG] - Cleaning up HomeView from main window
2025-08-12 12:13:50 - [fm.modules.base.base_module_view] [DEBUG] - Removed left panel from layout
2025-08-12 12:13:50 - [fm.modules.base.base_module_view] [DEBUG] - Removed center panel from layout
2025-08-12 12:13:50 - [fm.modules.base.base_presenter] [DEBUG] - HomePresenter is now hidden
2025-08-12 12:13:50 - [fm.module_coordinator] [DEBUG] - Showing update_data module
2025-08-12 12:13:50 - [fm.modules.base.base_presenter] [INFO] - Showing UpdateDataPresenter
2025-08-12 12:13:50 - [fm.modules.base.base_module_view] [INFO] - Setting up UpdateDataView in Main Window
2025-08-12 12:13:50 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Left Panel
2025-08-12 12:13:51 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Center Panel
2025-08-12 12:13:51 - [fm.modules.base.base_module_view] [INFO] - UpdateDataView setup complete
2025-08-12 12:13:51 - [fm.modules.update_data.ud_presenter] [DEBUG] - Refreshing UpdateData content
2025-08-12 12:13:51 - [fm.modules.update_data._ui.ud_view] [DEBUG] - [UD_VIEW] set_source_options: opts=4, selected=_flatmete_file_moniter_test, has_group=True
2025-08-12 12:13:51 - [fm.modules.update_data._ui._presenter.file_config_manager] [DEBUG] - [FILE_CONFIG_MANAGER] Source option changed: _flatmete_file_moniter_test
2025-08-12 12:13:51 - [fm.modules.update_data._ui._presenter.state_coordinator] [DEBUG] - [StateManager] sync_state_to_view: source_type=, folder='', files=0
2025-08-12 12:13:51 - [fm.modules.update_data._ui._presenter.file_config_manager] [DEBUG] - [FILE_CONFIG_MANAGER] Source option updated and saved: _flatmete_file_moniter_test
2025-08-12 12:13:51 - [fm.core.directory.services.directory_alias_service] [DEBUG] - [DirectoryAliasService] Built 1 archive labels
2025-08-12 12:13:51 - [fm.modules.update_data._ui._presenter.file_config_manager] [DEBUG] - [FILE_CONFIG_MANAGER] Save option changed: bank_statement_dwnlds_2025
2025-08-12 12:13:51 - [fm.core.services.recent_folders_service] [DEBUG] - [RecentFolders] Added to archive: c:\users\<USER>\onedrive\documents\accounts\bank_statement_dwnlds_2025
2025-08-12 12:13:51 - [fm.core.directory.services.directory_alias_service] [DEBUG] - [DirectoryAliasService] Built 1 archive labels
2025-08-12 12:13:51 - [fm.modules.update_data._ui._presenter.state_coordinator] [DEBUG] - [StateManager] sync_state_to_view: source_type=, folder='', files=0
2025-08-12 12:13:51 - [fm.modules.update_data._ui._presenter.state_coordinator] [DEBUG] - [StateManager] sync_state_to_view: source_type=, folder='', files=0
2025-08-12 12:13:51 - [fm.modules.update_data.ud_presenter] [DEBUG] - UpdateData content refresh complete
2025-08-12 12:13:51 - [fm.modules.base.base_presenter] [DEBUG] - UpdateDataPresenter is now visible
2025-08-12 12:13:51 - [fm.module_coordinator] [INFO] - Successfully transitioned to update_data
2025-08-12 12:17:08 - [fm.modules.update_data._ui._presenter.file_config_manager] [DEBUG] - [FILE_CONFIG_MANAGER] Source select requested: _flatmete_file_moniter_test
2025-08-12 12:17:08 - [fm.modules.update_data._ui._presenter.file_config_manager] [DEBUG] - [FILE_CONFIG_MANAGER] MRU source selected from options: c:\users\<USER>\downloads\_flatmete_file_moniter_test
2025-08-12 12:17:08 - [fm.modules.update_data._ui._view.shared_components.file_selector] [DEBUG] - File selection requested: title=Select Files to Process
2025-08-12 12:17:17 - [fm.modules.update_data._ui._view.shared_components.file_selector] [DEBUG] - Last used directory updated: C:/Users/<USER>/Downloads/_flatmete_file_moniter_test
2025-08-12 12:17:17 - [fm.modules.update_data._ui._view.shared_components.file_selector] [DEBUG] - Selected 12 file(s): C:/Users/<USER>/Downloads/_flatmete_file_moniter_test/38-9004-0646977-00_01Oct_kbank_basic.CSV and more
2025-08-12 12:17:17 - [fm.core.services.recent_folders_service] [DEBUG] - [RecentFolders] Added to source: c:\users\<USER>\downloads\_flatmete_file_moniter_test
2025-08-12 12:17:17 - [fm.modules.update_data._ui._presenter.file_config_manager] [DEBUG] - [FILE_CONFIG_MANAGER] Recent source folders updated via service: top=['c:\\users\\<USER>\\downloads\\_flatmete_file_moniter_test']
2025-08-12 12:17:17 - [fm.modules.update_data._ui.ud_view] [DEBUG] - [UD_VIEW] set_source_options: opts=4, selected=_flatmete_file_moniter_test, has_group=True
2025-08-12 12:17:17 - [fm.modules.update_data._ui._presenter.state_coordinator] [DEBUG] - [StateManager] sync_state_to_view: source_type=, folder='', files=0
2025-08-12 12:17:17 - [fm.modules.update_data._ui._presenter.file_config_manager] [DEBUG] - [FILE_CONFIG_MANAGER] Updated recent folders, now 2 folders
2025-08-12 12:17:17 - [fm.modules.update_data._ui._presenter.file_config_manager] [DEBUG] - [FILE_CONFIG_MANAGER] Handling selection of 12 file(s)
2025-08-12 12:17:17 - [fm.modules.update_data._ui._presenter.file_info_manager] [DEBUG] - [FILE_INFO_MANAGER] Setting 12 files with enrichment
2025-08-12 12:17:17 - [fm.modules.update_data._ui._presenter.file_info_manager] [DEBUG] - [FILE_INFO_MANAGER] Enriching 12 files...
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 38-9004-0646977-00_01Oct_kbank_basic.CSV
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-08-12 12:17:17 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '38-9004-0646977-00_01Oct_kbank_basic.CSV': 15 (Account: True, Cols: False, NumCols: True) -> Pass
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: KiwibankBasicCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 38-9004-0646977-00_06Aug.CSV
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-08-12 12:17:17 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '38-9004-0646977-00_06Aug.CSV': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for '38-9004-0646977-00_06Aug.CSV': 20 (Account + Columns)
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: KiwibankFullCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 38-9004-0646977-00_12Aug.CSV
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-08-12 12:17:17 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '38-9004-0646977-00_12Aug.CSV': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for '38-9004-0646977-00_12Aug.CSV': 20 (Account + Columns)
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: KiwibankFullCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 38-9004-0646977-00_13Jun.CSV
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-08-12 12:17:17 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '38-9004-0646977-00_13Jun.CSV': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for '38-9004-0646977-00_13Jun.CSV': 20 (Account + Columns)
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: KiwibankFullCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 38-9004-0646977-00_17Oct_kbank_fullCSV.CSV
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-08-12 12:17:17 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '38-9004-0646977-00_17Oct_kbank_fullCSV.CSV': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for '38-9004-0646977-00_17Oct_kbank_fullCSV.CSV': 20 (Account + Columns)
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: KiwibankFullCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 38-9004-0646977-01_06Aug.CSV
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-08-12 12:17:17 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '38-9004-0646977-01_06Aug.CSV': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for '38-9004-0646977-01_06Aug.CSV': 20 (Account + Columns)
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: KiwibankFullCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 38-9004-0646977-01_12Aug.CSV
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-08-12 12:17:17 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '38-9004-0646977-01_12Aug.CSV': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for '38-9004-0646977-01_12Aug.CSV': 20 (Account + Columns)
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: KiwibankFullCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 38-9004-0646977-04_06Aug.CSV
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-08-12 12:17:17 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '38-9004-0646977-04_06Aug.CSV': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for '38-9004-0646977-04_06Aug.CSV': 20 (Account + Columns)
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: KiwibankFullCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 38-9004-0646977-04_12Aug.CSV
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-08-12 12:17:17 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '38-9004-0646977-04_12Aug.CSV': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for '38-9004-0646977-04_12Aug.CSV': 20 (Account + Columns)
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: KiwibankFullCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 38-9004-0646977-04_13Jun.CSV
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-08-12 12:17:17 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '38-9004-0646977-04_13Jun.CSV': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for '38-9004-0646977-04_13Jun.CSV': 20 (Account + Columns)
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: KiwibankFullCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 38-9004-0646977-10_06Aug.CSV
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-08-12 12:17:17 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '38-9004-0646977-10_06Aug.CSV': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for '38-9004-0646977-10_06Aug.CSV': 20 (Account + Columns)
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: KiwibankFullCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 38-9004-0646977-10_12Aug.CSV
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-08-12 12:17:17 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '38-9004-0646977-10_12Aug.CSV': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for '38-9004-0646977-10_12Aug.CSV': 20 (Account + Columns)
2025-08-12 12:17:17 - [fm.modules.update_data.pipeline.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: KiwibankFullCSVHandler
2025-08-12 12:17:17 - [fm.modules.update_data._ui._presenter.file_info_manager] [DEBUG] - [FILE_INFO_MANAGER] Successfully enriched 12 files
2025-08-12 12:17:17 - [fm.modules.update_data._ui._presenter.file_info_manager] [DEBUG] - [FILE_LIST_MANAGER] Updated folder info: C:\Users\<USER>\Downloads\_flatmete_file_moniter_test (12 files)
2025-08-12 12:17:17 - [fm.modules.update_data._ui.ud_view] [DEBUG] - [UD_VIEW] Received file display update: <class 'fm.modules.update_data._ui.ui_events.FileListUpdatedEvent'>
2025-08-12 12:17:17 - [fm.modules.update_data._ui.ud_view] [DEBUG] - [UD_VIEW] Received 12 enriched FileInfoData objects
2025-08-12 12:17:17 - [fm.modules.update_data._ui._view.center_panel_components.ud_file_view] [DEBUG] - [UDFileView] Setting 12 enriched files directly
2025-08-12 12:17:20 - [fm.modules.update_data._ui._view.center_panel_components.ud_file_view] [DEBUG] - [UDFileView] Successfully set 12 enriched files with full metadata
2025-08-12 12:17:20 - [fm.modules.update_data._ui._view.center_panel_components.guide_pane_v2] [DEBUG] - [GuidePaneV2] set_section_info(source): bind folder context -> 'C:/Users/<USER>/Downloads/_flatmete_file_moniter_test'
2025-08-12 12:17:20 - [fm.modules.update_data._ui._view.center_panel_components.guide_pane_components.source_section] [DEBUG] - [SourceSection] show_enable_option: visible=True
2025-08-12 12:17:20 - [fm.modules.update_data._ui._view.center_panel_components.guide_pane_v2] [DEBUG] - [GuidePaneV2] source context bound: current_folder='C:/Users/<USER>/Downloads/_flatmete_file_moniter_test'
2025-08-12 12:17:20 - [fm.modules.update_data._ui._view.center_panel_components.guide_pane_v2] [DEBUG] - [GuidePaneV2] set_section_info(source): bind folder context -> 'C:/Users/<USER>/Downloads/_flatmete_file_moniter_test — 12 file(s) listed'
2025-08-12 12:17:20 - [fm.modules.update_data._ui._view.center_panel_components.guide_pane_components.source_section] [DEBUG] - [SourceSection] show_enable_option: visible=True
2025-08-12 12:17:20 - [fm.modules.update_data._ui._view.center_panel_components.guide_pane_v2] [DEBUG] - [GuidePaneV2] source context bound: current_folder='C:/Users/<USER>/Downloads/_flatmete_file_moniter_test — 12 file(s) listed'
2025-08-12 12:17:20 - [fm.modules.update_data._ui._presenter.file_info_manager] [DEBUG] - [FILE_INFO_MANAGER] Published FILE_LIST_UPDATED with 12 enriched files
2025-08-12 12:17:20 - [fm.modules.update_data._ui._presenter.file_config_manager] [DEBUG] - [FILE_CONFIG_MANAGER] Added 12 file(s) to file view from files selection
2025-08-12 12:17:20 - [fm.modules.update_data._ui._presenter.state_coordinator] [DEBUG] - [StateManager] sync_state_to_view: source_type=files, folder='C:/Users/<USER>/Downloads/_flatmete_file_moniter_test', files=12
2025-08-12 12:17:20 - [fm.modules.update_data._ui._presenter.state_coordinator] [DEBUG] - [StateManager] update_guide_pane -> files list: count=12; inferredFolder='C:/Users/<USER>/Downloads/_flatmete_file_moniter_test'
2025-08-12 12:17:20 - [fm.modules.update_data._ui._presenter.state_coordinator] [DEBUG] - [StateManager] guide(files): on_files_listed -> folder='C:/Users/<USER>/Downloads/_flatmete_file_moniter_test', count=12; no on_discovery_toggled during sync
2025-08-12 12:17:20 - [fm.modules.update_data._ui._view.center_panel_components.guide_pane_v2] [DEBUG] - [GuidePaneV2] set_section_info(source): bind folder context -> 'C:/Users/<USER>/Downloads/_flatmete_file_moniter_test'
2025-08-12 12:17:20 - [fm.modules.update_data._ui._view.center_panel_components.guide_pane_components.source_section] [DEBUG] - [SourceSection] show_enable_option: visible=True
2025-08-12 12:17:20 - [fm.modules.update_data._ui._view.center_panel_components.guide_pane_v2] [DEBUG] - [GuidePaneV2] source context bound: current_folder='C:/Users/<USER>/Downloads/_flatmete_file_moniter_test'
2025-08-12 12:17:20 - [fm.modules.update_data._ui._presenter.file_discovery_manager] [DEBUG] - [FileDiscoveryManager] Read discovery: folder='C:/Users/<USER>/Downloads/_flatmete_file_moniter_test', enabled=True
2025-08-12 12:17:20 - [fm.modules.update_data._ui._view.center_panel_components.guide_pane_v2] [DEBUG] - [GuidePaneV2] show_source_context_options: folderParam='C:/Users/<USER>/Downloads/_flatmete_file_moniter_test', resolved='C:/Users/<USER>/Downloads/_flatmete_file_moniter_test', discovery_enabled=True
2025-08-12 12:17:20 - [fm.modules.update_data._ui._view.center_panel_components.guide_pane_components.source_section] [DEBUG] - [SourceSection] show_enable_option: visible=True
2025-08-12 12:17:20 - [fm.modules.update_data._ui._view.center_panel_components.guide_pane_components.source_section] [DEBUG] - [SourceSection] set_enable_checked: to=True, was=False, signalsBlocked(before)=False
2025-08-12 12:17:20 - [fm.gui._shared_components.widgets.checkboxes] [DEBUG] - [LabeledCheckBox] set_checked: label='File discovery', to=True, was=False, signalsBlocked=True
2025-08-12 12:17:20 - [fm.gui._shared_components.widgets.checkboxes] [DEBUG] - [DebugCheckBox] setChecked: label='File discovery', to=True, was=False, signalsBlocked=True
2025-08-12 12:17:20 - [fm.modules.update_data._ui._view.center_panel_components.guide_pane_components.source_section] [DEBUG] - [SourceSection] set_enable_checked: now=True, signalsBlocked(active)=True
2025-08-12 12:17:20 - [fm.modules.update_data._ui._view.center_panel_components.guide_pane_components.source_section] [DEBUG] - [SourceSection] set_enable_checked: signals restored -> False
2025-08-12 12:17:20 - [fm.modules.update_data._ui._view.center_panel_components.guide_pane_v2] [DEBUG] - [GuidePaneV2] set_enable_checked(programmatic): folder='C:/Users/<USER>/Downloads/_flatmete_file_moniter_test', to=True
2025-08-12 12:17:20 - [fm.modules.update_data._ui._presenter.guide_pane_presenter] [DEBUG] - [GuidePanePresenter] on_files_listed: count=12, folder=C:/Users/<USER>/Downloads/_flatmete_file_moniter_test, source_type=files
2025-08-12 12:17:20 - [fm.modules.update_data._ui._presenter.file_config_manager] [DEBUG] - [FILE_CONFIG_MANAGER] State synchronised to view after source selection
2025-08-12 12:17:20 - [fm.modules.update_data._ui._view.center_panel_components.guide_pane_v2] [DEBUG] - [GuidePaneV2] show_source_context_options: folderParam='C:/Users/<USER>/Downloads/_flatmete_file_moniter_test', resolved='C:/Users/<USER>/Downloads/_flatmete_file_moniter_test', discovery_enabled=True
2025-08-12 12:17:20 - [fm.modules.update_data._ui._view.center_panel_components.guide_pane_components.source_section] [DEBUG] - [SourceSection] show_enable_option: visible=True
2025-08-12 12:17:20 - [fm.modules.update_data._ui._view.center_panel_components.guide_pane_components.source_section] [DEBUG] - [SourceSection] set_enable_checked: to=True, was=True, signalsBlocked(before)=False
2025-08-12 12:17:20 - [fm.gui._shared_components.widgets.checkboxes] [DEBUG] - [LabeledCheckBox] set_checked: label='File discovery', to=True, was=True, signalsBlocked=True
2025-08-12 12:17:20 - [fm.gui._shared_components.widgets.checkboxes] [DEBUG] - [DebugCheckBox] setChecked: label='File discovery', to=True, was=True, signalsBlocked=True
2025-08-12 12:17:20 - [fm.modules.update_data._ui._view.center_panel_components.guide_pane_components.source_section] [DEBUG] - [SourceSection] set_enable_checked: now=True, signalsBlocked(active)=True
2025-08-12 12:17:20 - [fm.modules.update_data._ui._view.center_panel_components.guide_pane_components.source_section] [DEBUG] - [SourceSection] set_enable_checked: signals restored -> False
2025-08-12 12:17:20 - [fm.modules.update_data._ui._view.center_panel_components.guide_pane_v2] [DEBUG] - [GuidePaneV2] set_enable_checked(programmatic): folder='C:/Users/<USER>/Downloads/_flatmete_file_moniter_test', to=True
2025-08-12 12:17:20 - [fm.modules.update_data._ui._presenter.state_coordinator] [DEBUG] - [StateManager] sync_state_to_view: source_type=files, folder='C:/Users/<USER>/Downloads/_flatmete_file_moniter_test', files=12
2025-08-12 12:17:20 - [fm.modules.update_data._ui._presenter.state_coordinator] [DEBUG] - [StateManager] update_guide_pane -> files list: count=12; inferredFolder='C:/Users/<USER>/Downloads/_flatmete_file_moniter_test'
2025-08-12 12:17:20 - [fm.modules.update_data._ui._presenter.state_coordinator] [DEBUG] - [StateManager] guide(files): on_files_listed -> folder='C:/Users/<USER>/Downloads/_flatmete_file_moniter_test', count=12; no on_discovery_toggled during sync
2025-08-12 12:17:20 - [fm.modules.update_data._ui._view.center_panel_components.guide_pane_v2] [DEBUG] - [GuidePaneV2] set_section_info(source): bind folder context -> 'C:/Users/<USER>/Downloads/_flatmete_file_moniter_test'
2025-08-12 12:17:20 - [fm.modules.update_data._ui._view.center_panel_components.guide_pane_components.source_section] [DEBUG] - [SourceSection] show_enable_option: visible=True
2025-08-12 12:17:20 - [fm.modules.update_data._ui._view.center_panel_components.guide_pane_v2] [DEBUG] - [GuidePaneV2] source context bound: current_folder='C:/Users/<USER>/Downloads/_flatmete_file_moniter_test'
2025-08-12 12:17:20 - [fm.modules.update_data._ui._presenter.file_discovery_manager] [DEBUG] - [FileDiscoveryManager] Read discovery: folder='C:/Users/<USER>/Downloads/_flatmete_file_moniter_test', enabled=True
2025-08-12 12:17:20 - [fm.modules.update_data._ui._view.center_panel_components.guide_pane_v2] [DEBUG] - [GuidePaneV2] show_source_context_options: folderParam='C:/Users/<USER>/Downloads/_flatmete_file_moniter_test', resolved='C:/Users/<USER>/Downloads/_flatmete_file_moniter_test', discovery_enabled=True
2025-08-12 12:17:20 - [fm.modules.update_data._ui._view.center_panel_components.guide_pane_components.source_section] [DEBUG] - [SourceSection] show_enable_option: visible=True
2025-08-12 12:17:20 - [fm.modules.update_data._ui._view.center_panel_components.guide_pane_components.source_section] [DEBUG] - [SourceSection] set_enable_checked: to=True, was=True, signalsBlocked(before)=False
2025-08-12 12:17:20 - [fm.gui._shared_components.widgets.checkboxes] [DEBUG] - [LabeledCheckBox] set_checked: label='File discovery', to=True, was=True, signalsBlocked=True
2025-08-12 12:17:20 - [fm.gui._shared_components.widgets.checkboxes] [DEBUG] - [DebugCheckBox] setChecked: label='File discovery', to=True, was=True, signalsBlocked=True
2025-08-12 12:17:20 - [fm.modules.update_data._ui._view.center_panel_components.guide_pane_components.source_section] [DEBUG] - [SourceSection] set_enable_checked: now=True, signalsBlocked(active)=True
2025-08-12 12:17:20 - [fm.modules.update_data._ui._view.center_panel_components.guide_pane_components.source_section] [DEBUG] - [SourceSection] set_enable_checked: signals restored -> False
2025-08-12 12:17:20 - [fm.modules.update_data._ui._view.center_panel_components.guide_pane_v2] [DEBUG] - [GuidePaneV2] set_enable_checked(programmatic): folder='C:/Users/<USER>/Downloads/_flatmete_file_moniter_test', to=True
2025-08-12 12:17:20 - [fm.modules.update_data._ui._presenter.guide_pane_presenter] [DEBUG] - [GuidePanePresenter] on_files_listed: count=12, folder=C:/Users/<USER>/Downloads/_flatmete_file_moniter_test, source_type=files
