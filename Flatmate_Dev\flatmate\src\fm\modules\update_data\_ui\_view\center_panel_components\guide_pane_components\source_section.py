from __future__ import annotations

from typing import Optional, Callable

from PySide6.QtCore import Qt
from PySide6.QtWidgets import QWidget, QLabel, QVBoxLayout

from fm.gui._shared_components.widgets.labels import InfoLabel
from fm.gui._shared_components.widgets.checkboxes import LabeledCheckBox
from .base_components.section import Section
from .base_components.slot_row import SlotRow
from fm.core.services.logger import log


class SourceSection(Section):
    """
    Source section with fixed, named slots:
      - message: placeholder/info text (hidden by default)
      - options: per-folder discovery toggle (hidden by default)

    API is explicit: set_message(text), show_message(visible),
    show_enable_option(visible), set_enable_checked(checked), connect_enable_toggled(cb).
    """

    def __init__(self, parent: Optional[QWidget] = None) -> None:
        super().__init__(title="Source", parent=parent)
        # Use base info line for placeholder/summary; left align and indent
        self.set_info_alignment(Qt.AlignLeft | Qt.AlignVCenter)
        self.set_info_indent(8)
        # Default placeholder visible on startup
        self.set_info("No files or folders selected…")

        # path slot (single-line label, hidden by default)
        self._path_label = QLabel("")
        self._path_label.setWordWrap(False)
        self._path_row = SlotRow()
        self._path_row.set_content(self._path_label)
        self._path_row.set_horizontal_padding(8)
        self._path_row.set_visible(False)
        self.add_to_body(self._path_row)

        # options slot (file discovery) — use shared LabeledCheckBox directly
        self._enable_checkbox = LabeledCheckBox("File discovery")
        self._enable_checkbox.checkbox.setToolTip(
            "When on, files are discovered automatically: initial scan and live updates for the selected folder.\n"
            "Saved per folder."
        )

        self._enable_row = SlotRow()
        self._enable_row.set_content(self._enable_checkbox)
        self._enable_row.set_horizontal_padding(8)
        self._enable_row.set_visible(False)
        self.add_to_body(self._enable_row)

        # notice slot (collapsible notice, hidden by default)
        # Use a plain QLabel with inline style to avoid global QSS side-effects.
        # TODO: Move styling into a scoped QSS for guide pane v2 when styles are rationalised.
        self._notice_label = QLabel("")
        self._notice_label.setWordWrap(True)
        try:
            self._notice_label.setObjectName("")  # ensure no id-based QSS applies
            self._notice_label.setStyleSheet("margin:0; padding:0; font-weight:normal; font-style:normal;")
        except Exception:
            pass
        self._notice_label.setContentsMargins(8, 4, 8, 4)
        self._notice_row = SlotRow()
        self._notice_row.set_content(self._notice_label)
        self._notice_row.set_horizontal_padding(0)
        self._notice_row.set_visible(False)
        self.add_to_body(self._notice_row)

        # Do not override `info` sizing here; Section manages zero-height when empty

    # --- API ---
    def set_message(self, text: str) -> None:
        # Delegate to base info slot
        self.set_info(text or "")

    def show_message(self, visible: bool) -> None:
        if visible:
            # Ensure a placeholder exists if empty
            current = getattr(self, "info", None)
            text = current.text() if current is not None else ""
            self.set_info(text or "No files or folders selected…")
        else:
            self.clear_info()

    # --- Path slot API ---
    def set_path_html(self, html: str, tooltip: str = "") -> None:
        self._path_label.setText(html or "")
        self._path_label.setToolTip(tooltip or "")
        self._path_row.set_visible(bool(html))
        # Hide placeholder when a path is present
        if html:
            self.clear_info()

    def clear_path(self) -> None:
        self._path_label.setText("")
        self._path_label.setToolTip("")
        self._path_row.set_visible(False)
        # Restore placeholder when path is cleared
        self.set_info("No files or folders selected…")

    def show_path(self, visible: bool) -> None:
        self._path_row.set_visible(bool(visible))

    def show_enable_option(self, visible: bool) -> None:
        log.debug(f"[SourceSection] show_enable_option: visible={bool(visible)}")
        self._enable_row.set_visible(bool(visible))

    def set_enable_checked(self, checked: bool) -> None:
        # Block signals to avoid emitting during programmatic updates
        cb = self._enable_checkbox.checkbox
        prev = cb.blockSignals(True)
        try:
            log.debug(
                f"[SourceSection] set_enable_checked: to={bool(checked)}, was={self._enable_checkbox.is_checked()}, "
                f"signalsBlocked(before)={prev}"
            )
            self._enable_checkbox.set_checked(bool(checked))
            log.debug(
                f"[SourceSection] set_enable_checked: now={self._enable_checkbox.is_checked()}, "
                f"signalsBlocked(active)={cb.signalsBlocked()}"
            )
        finally:
            cb.blockSignals(prev)
            log.debug(f"[SourceSection] set_enable_checked: signals restored -> {cb.signalsBlocked()}")

    def connect_enable_toggled(self, cb: Callable[[bool], None]) -> None:
        self._enable_checkbox.state_changed.connect(cb)

    # --- Notice API ---
    def set_notice(self, text: str) -> None:
        value = (text or "").strip()
        self._notice_label.setText(value)
        self._notice_row.set_visible(bool(value))

    def clear_notice(self) -> None:
        self._notice_label.setText("")
        self._notice_row.set_visible(False)

    def show_notice(self, visible: bool) -> None:
        self._notice_row.set_visible(bool(visible))
