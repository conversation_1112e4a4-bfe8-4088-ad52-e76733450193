from __future__ import annotations

import os
from pathlib import Path
from typing import Dict, List, Optional

from fm.core.services.logger import log
from fm.core.services.recent_folders_service import RecentFoldersService


class DirectoryAliasService:
    """Bidirectional label↔path mapping for directory options (MRU, etc.).

    - Labels are human-friendly and stable within a refresh cycle.
    - Collisions resolved with a short parent hint and numeric suffix if needed.
    - Resolution accepts labels (case-insensitive) or absolute paths.
    - No persistence: hydrated from RecentFoldersService on demand.
    """

    def __init__(self, recent_folders: Optional[RecentFoldersService] = None) -> None:
        self._recent = recent_folders or RecentFoldersService()
        self._label_to_path: Dict[str, str] = {}

    # ===== Public API =====
    def refresh_from_recent(self) -> None:
        """Reload mappings from RecentFoldersService archives list."""
        try:
            self._recent.load()
        except Exception as e:
            log.error(f"[DirectoryAliasService] Failed to load recent folders: {e}")
        self._label_to_path.clear()
        paths = [p for p in self._recent.get_archives() if isinstance(p, str)]
        labels = self._build_labels_for_paths(paths)
        self._label_to_path = dict(labels)
        log.debug(f"[DirectoryAliasService] Built {len(self._label_to_path)} archive labels")

    def labels(self) -> List[str]:
        return list(self._label_to_path.keys())

    def mapping(self) -> Dict[str, str]:
        return dict(self._label_to_path)

    def label_for(self, path: str) -> str:
        """Return a label for a single path, consistent with current rules."""
        pairs = self._build_labels_for_paths([path])
        return pairs[0][0] if pairs else os.path.basename(path.rstrip(os.sep)) or path

    def resolve(self, label_or_path: str) -> Optional[str]:
        """Resolve a label back to a full path.

        - Case-insensitive match against known labels
        - If input is an absolute existing directory, return as-is
        """
        if not label_or_path:
            return None
        s = str(label_or_path)
        try:
            if os.path.isabs(s) and os.path.isdir(s):
                return s
        except Exception:
            pass
        low = s.lower()
        for k, v in self._label_to_path.items():
            if k.lower() == low:
                return v
        return None

    def register_path(self, path: str) -> None:
        """Add a path into MRU and rebuild labels."""
        try:
            if path and os.path.isdir(path):
                self._recent.add_archive(path)
                self.refresh_from_recent()
        except Exception as e:
            log.error(f"[DirectoryAliasService] Failed to register path: {e}")

    # ===== Utility for arbitrary path sets (e.g., Source MRU) =====
    def build_mapping_for_paths(self, paths: List[str]) -> Dict[str, str]:
        """Return a collision-safe label->path mapping for the provided paths.

        Does not mutate internal state. Intended for building Source MRU labels
        using the same rules as archives.
        """
        try:
            pairs = self._build_labels_for_paths(paths)
            return dict(pairs)
        except Exception as e:
            log.error(f"[DirectoryAliasService] Failed to build mapping for paths: {e}")
            return {}

    # ===== Internal helpers =====
    def _build_labels_for_paths(self, paths: List[str]) -> List[tuple[str, str]]:
        """Build collision-safe labels for a list of paths.

        Strategy:
        - Base = basename(path)
        - If collision, append short parent: "Base — Parent"
        - If still collision, append numeric suffix: "Base — Parent (N)"
        """
        label_to_path: Dict[str, str] = {}
        seen: Dict[str, int] = {}

        def _short_parent(p: str) -> str:
            parent = os.path.basename(os.path.dirname(p.rstrip(os.sep)))
            return parent

        for path in paths:
            try:
                base = os.path.basename(path.rstrip(os.sep)) or path
                # initial proposal
                label = base
                if label in label_to_path:
                    parent = _short_parent(path)
                    label = f"{base} — {parent}" if parent else base
                # ensure uniqueness
                count = seen.get(label, 0)
                while label in label_to_path:
                    count += 1
                    seen[label] = count
                    parent = _short_parent(path)
                    if parent:
                        label = f"{base} — {parent} ({count})"
                    else:
                        label = f"{base} ({count})"
                label_to_path[label] = path
            except Exception:
                continue
        return list(label_to_path.items())
