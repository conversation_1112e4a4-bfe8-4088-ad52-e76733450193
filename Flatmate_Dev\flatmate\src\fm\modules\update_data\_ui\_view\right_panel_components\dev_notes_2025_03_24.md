dev_notes_2025_03_24.md

DISCUSS:
<PERSON><PERSON> lets consider this..
We have the nav pane, that works. 
It is currently imported by the main window. 
if I'm not mistaken, and placed in the 
"right_side_bar"
Where? in _main_window 
This is because it is a global component.
It could just as easily be called a task_pane.
because in reality the modules are concerned with tasks.
Regardless:
@main_window.py#L509-521 see todo. 
this shouldn't be in main window.
is it possible we need a container for each major component?

I don't think granular implementation details should be in main_window.
We have gui/components. 
They can be categorised as shared components.
There are global components:
-Always present, no matter which module is active in main_window, can be shown or hidden
Anything imported by main_window
Should be in a folder called main_window_content. 
But they would be very high level.
now, main window has a method called set content.
? in the original design, each module creates a frame, or q box layout
? I believe module coordinator has something to do with this.

The nuts and bolts of what I'm thinking is that we have an intermediary..
A container for everything in a given main_window element.
-This container could be called a module.(but all py files are modules, we may have to look at this naming convention conflict)

So these content containers could be named something like :
gui/components/
/main_window_content/
- center_panel_content.py 
- left_panel_content.py 
- right_panel_content.py
- side_bar_right_content.py
then we have
components/main_window_widgets
-info_bar.py
# if we do this, we should start with one element, say right side bar, and only create other files if we get this working. 
and 

components/shared_components

# ? now we have a question of - do we have collections of shared components?
# ? for now, we could just move the current components used by modules there.

basically, we have a container for each major main_window element.
in this way, we can set the content of each major main_window element.
modules can address the content containers. 
they will have a section at the top of the file, 
clearly commented
that will control the behaviour of default content.
the nav_pane will be imported by right_side_bar_content.py
Main_window will address side_bar_right__content (f'ing snake case)
?or should it put itself in there? is that possible?

Right now we have a utilites pane that simply contains a gear icon.
if we have a feature that can be addressed by a single icon, why does it need it's own pane?


I want main_window to remain reltively simple and navigable.










