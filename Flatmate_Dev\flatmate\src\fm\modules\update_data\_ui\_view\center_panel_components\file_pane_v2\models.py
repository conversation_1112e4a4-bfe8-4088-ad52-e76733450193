"""
Data models for the file view component.

Contains view models for managing file information and component state.
Uses the unified FileInfoData model from the models package.
"""

from typing import List, Optional
from fm.modules.update_data.models.file_info import FileInfoData

# For backward compatibility, create an alias
FileInfo = FileInfoData

class FileViewModel:
    """Data model for the file view component."""
    
    def __init__(self):
        self.files: List[FileInfo] = []
        self.selected_file: Optional[str] = None
        self._sort_by: str = "name"
        self._sort_order: str = "asc"
    
    def add_file(self, file_info: FileInfo) -> None:
        """Add a file to the model."""
        # Check if file already exists
        existing_paths = [f.path for f in self.files]
        if file_info.path not in existing_paths:
            self.files.append(file_info)
            self._sort_files()
    
    def remove_file(self, file_path: str) -> bool:
        """Remove a file from the model. Returns True if removed."""
        for i, file_info in enumerate(self.files):
            if file_info.path == file_path:
                del self.files[i]
                if self.selected_file == file_path:
                    self.selected_file = None
                return True
        return False
    
    def get_file_paths(self) -> List[str]:
        """Get list of all file paths."""
        return [f.path for f in self.files]
        
    def has_file(self, file_path: str) -> bool:
        """Check if a file exists in the model."""
        return file_path in self.get_file_paths()
        
    def update_file_info(self, file_path: str, updated_info: dict) -> None:
        """Update file information with enriched data from presenter.

        Args:
            file_path: Path of the file to update
            updated_info: Dictionary with updated file information
        """
        for file_info in self.files:
            if file_info.path == file_path:
                # Update FileInfoData fields - handle both old and new field names
                if 'size_bytes' in updated_info:
                    file_info.size_bytes = updated_info['size_bytes']
                if 'size' in updated_info:  # Legacy compatibility
                    file_info.size_bytes = updated_info['size']
                if 'size_str' in updated_info:
                    file_info.size_str = updated_info['size_str']
                if 'modified' in updated_info:
                    file_info.modified = updated_info['modified']
                if 'created' in updated_info:
                    file_info.created = updated_info['created']
                if 'is_valid' in updated_info:
                    file_info.is_valid = updated_info['is_valid']
                if 'is_processed' in updated_info:
                    file_info.is_processed = updated_info['is_processed']
                # Update enriched fields from FileInfoService
                if 'bank_type' in updated_info:
                    file_info.bank_type = updated_info['bank_type']
                if 'format_type' in updated_info:
                    file_info.format_type = updated_info['format_type']
                if 'handler' in updated_info:
                    file_info.handler = updated_info['handler']
                break
    
    def set_selected_file(self, file_path: Optional[str]) -> None:
        """Set the selected file."""
        if file_path is None or file_path in self.get_file_paths():
            self.selected_file = file_path
    
    def clear_files(self) -> None:
        """Clear all files from the model."""
        self.files.clear()
        self.selected_file = None
    
    def set_sort_criteria(self, sort_by: str, sort_order: str = "asc") -> None:
        """Set sorting criteria and re-sort files."""
        self._sort_by = sort_by
        self._sort_order = sort_order
        self._sort_files()
    
    def _sort_files(self) -> None:
        """Sort files based on current criteria."""
        reverse = self._sort_order == "desc"

        if self._sort_by == "name":
            self.files.sort(key=lambda f: f.name.lower(), reverse=reverse)
        elif self._sort_by == "size":
            self.files.sort(key=lambda f: f.size_bytes, reverse=reverse)
        elif self._sort_by == "type":
            # Sort by file_info_display for enriched type information
            self.files.sort(key=lambda f: f.file_info_display, reverse=reverse)
        elif self._sort_by == "date":
            self.files.sort(key=lambda f: f.modified or f.created, reverse=reverse)
