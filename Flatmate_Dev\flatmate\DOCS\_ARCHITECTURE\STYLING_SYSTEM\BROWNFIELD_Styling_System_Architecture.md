# Brownfield Architecture: Styling System (QSS Loader, Theming, Migration)

Status: Updated to reflect refactor completed and theme experiment defaults

Scope
- Focus areas: QSS loader, theming strategy, migration plan
- Inputs: Consolidated baseline [base_theme.qss](flatmate/src/fm/gui/styles/base_theme.qss:1), refactored modules [loader.py](flatmate/src/fm/gui/styles/loader.py:1) and [applier.py](flatmate/src/fm/gui/styles/applier.py:1)
- Note: The template variable {{FONT_SIZE}} is supported via application-level replacement (QSS has no native variables)

1) Current State (Post-Refactor)
- Single stylesheet approach:
  - Preferred filename: base_theme.qss (fallback to flatmate_consolidated.qss during migration)
- Module responsibilities:
  - [loader.py](flatmate/src/fm/gui/styles/loader.py:1)
    - Selects consolidated stylesheet (prefers base_theme.qss)
    - Replaces {{FONT_SIZE}} using ConfigKeys.App.BASE_FONT_SIZE with fallback 14
    - Optional YAML-driven color-only theming via hex_map
    - Provenance logs: selected file, replacement counts, theme hex_map totals and zero-hit keys
  - [applier.py](flatmate/src/fm/gui/styles/applier.py:1)
    - apply_styles(app) sets the stylesheet on QApplication
    - Provenance log: BASE_FONT_SIZE and its source (config or default), applied stylesheet path
    - Auditing: Qt-applied dump, diffs vs loader output, delayed widget color capture
- Import surface:
  - main.py explicitly uses: from .gui.styles.applier import apply_styles

2) Theming Strategy (Explicit and Deterministic)
- QSS has no native variables or CSS var() support for our purposes
- We perform deterministic, exact string replacements:
  - Tokens: {{FONT_SIZE}} at application load-time
  - Colors: optional hex_map replacement from a YAML theme file
- Theme Experiment Default
  - ENABLE_THEME_EXPERIMENT = False by default (per 2025-08-05 decision)
  - Provided scaffold: [theme-light.yaml](flatmate/src/fm/gui/styles/themes/theme-light.yaml:1)
  - To enable: toggle ENABLE_THEME_EXPERIMENT=True in [loader.py](flatmate/src/fm/gui/styles/loader.py:1) and set DEFAULT_THEME_NAME accordingly

3) Roadmap (A–E)
A) Create styles/README.md covering tokens, theme schema, provenance logging, audit outputs, and toggling instructions
B) Add theme-dark.yaml mirroring current dark palette for a documented baseline
C) Physically rename flatmate_consolidated.qss to base_theme.qss and remove fallback
D) Sweep consolidated QSS to ensure all colors are from an approved palette (no stray off-palette values)
E) Finalize docs; keep ENABLE_THEME_EXPERIMENT disabled by default, with clear enable instructions

4) Audit and Safety
- Maintain AUDIT_STYLESHEET toggle to emit:
  - debug_combined_output.qss (loader output)
  - debug_qt_applied.qss (Qt-applied)
  - debug_differences.txt (quick diff)
  - debug_actual_colors.txt (delayed palette snapshot)
- Log replacement counts and zero-hit mapping keys to avoid silent regressions

5) Contracts
- Replacement contract:
  - {{FONT_SIZE}} replaced before app.setStyleSheet(), from ConfigKeys.App.BASE_FONT_SIZE with fallback 14
  - Optional theme hex_map replaces exact hex literals only (case-sensitive with leading '#')
- Backward compatibility:
  - Fallback to flatmate_consolidated.qss remains until physical rename
  - No widget code changes required for color theming

Summary
The system now uses a clear loader/applier split with provenance logging and optional, disabled-by-default YAML color theming. The approach preserves deterministic behavior, improves observability, and provides a controlled path to introduce additional themes without destabilizing the UI.