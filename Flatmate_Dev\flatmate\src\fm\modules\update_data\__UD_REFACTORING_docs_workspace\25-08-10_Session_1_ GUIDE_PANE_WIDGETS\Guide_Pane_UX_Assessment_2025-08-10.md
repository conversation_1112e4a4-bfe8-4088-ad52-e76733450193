# Guide Pane – Initial State UX Assessment (Concise)

Date: 2025-08-10
Author: Cascade

## Findings

- __Empty-state blindness__
  The centre area appears mostly empty on first open. Users lack a clear starting action.
  
- __Weak CTAs__
  No obvious entry points in the guide for the two primary actions: select folder, add files.
  >> what are CTA's?
  >>
  >
- __Context not surfaced__
  After folder selection, the per-folder discovery toggle can be missed if the folder context is not set/passed.
- __Layout balance__
  Splitter defaults bias a large empty file area; guide content is compact and cannot counter the emptiness.
- __Deprecated naming__
  “Auto queue” is deprecated in favour of `enable_file_discovery_toggled`. A shim exists but the preferred name should be primary.
  >> this should take a payload, the dir_path >> NOte this is not' in production' code, we should clea up the old refferences now, a simple grep search should find them.
  >>
  >
- __Fail-fast drift__ (`center_panel_layout.py`)
  Broad try/except in `set_files/get_files/set_source_path/display_enriched_file_info` obscures real errors.
  >> pos cons reqs, we dont like obscuring errors.
  >>
  >
- __Minor hygiene__
  Unused imports in `guide_pane.py` (e.g., `QTextEdit`, `QFont`, `QPalette`, `QTextCharFormat`, `QTextCursor`) and in `center_panel_layout.py` (`pandas as pd`). >>clean them up

## Minimal, high-impact changes

- __Guide CTAs (initial state)__
  Add two compact buttons within `GuidePaneWidget` when state is `initial`:

  - “Select entire folder…” → raises the same intent the left panel uses.
  - “Add files…” → reuse `UDFileView.add_files_requested` wiring.
    Include a one-line tip: “Select a folder or add files to begin.”
- __Contextual prompt after folder select__
  On `set_state('folder_selected', count=N, folder=path)`:

  - Show: “Found N CSV files ready for processing”.
  - Show per-folder toggle row: “Enable file discovery for this folder”.
  - If `N == 0`, show warning and keep the two CTAs visible.
- __Always-visible subtle hint__
  A small grey hint at the bottom of the guide (e.g., drag-and-drop tip if supported, otherwise a generic “Use the buttons above to begin.”).
- __Empty file list placeholder__
  In `UDFileView`, when the model is empty show a centred placeholder message in the viewport: “No files yet — use Add files or Select folder.”
- __Splitter default balance__
  Adjust defaults from `[180, 820]` to `[220, 780]` to better host the CTAs without making the layout feel top-heavy. Continue to persist user-chosen sizes in `QSettings`.

## Wiring: enable_file_discovery_toggled

- __Preferred signal__
  `GuidePaneWidget.enable_file_discovery_toggled = Signal(str, bool)`  // (folder, enabled)
- __Deprecated alias (shim)__
  Keep `publish_toggle_auto_queue_requested(str, bool)` emitting the same payload. Mark as deprecated in-code.
- __View-layer bridge__
  In `ud_view.py`, connect the guide signal to a typed local intent (e.g., `LocalIntent.ENABLE_FILE_DISCOVERY_TOGGLED(folder, enabled)`). The presenter subscribes to the intent (Qt-free) and calls the directory service.
- __Folder context is required__
  Ensure callers use `show_source_context_options(..., folder=resolved_path)` so `_current_folder` is set. If missing, the toggle should no-op with a clear warning (already logged).

## Recommended copy

- __Initial (info):__ “Select source files or a folder to begin.”Buttons: “Select entire folder…”, “Add files…”
- __Folder selected (info):__ “Found N CSV files ready for processing”Option: “[ ] Enable file discovery for this folder”
- __No files (warning):__ “No compatible files found in the selected location”Buttons remain visible
- __Processing (processing):__ “Processing file X of Y…”
- __Success (success):__ “Successfully processed N files”

## Small code hygiene (safe to do now)

- Remove unused imports in `guide_pane.py` and `center_panel_layout.py`.
- Remove broad try/except in `CenterPanelManager.set_files/get_files/set_source_path/display_enriched_file_info` (fail-fast). If defensive guards are truly needed (shutdown edge cases), narrow exception scopes and log the specific failure.
- Prevent message overwrite in `CenterPanelManager.set_source_path()` by only displaying the path when the guide is in `initial` state or when the guide message is hidden.

## Why this helps

- Puts two clear actions in the user’s eye-line, reducing confusion.
- Surfaces folder discovery at the right moment, per-folder.
- Reduces perceived emptiness with a tasteful placeholder and slightly larger guide area.
- Keeps presenter Qt-free and aligns with the view-as-switchboard decision.

## Next steps

- Implement the guide CTAs and the empty-state placeholder.
- Rename the signal (keep alias) and wire the local intent in `ud_view.py`.
- Apply the small hygiene/fail-fast fixes in `CenterPanelManager`.

## Design options (to decide later)

* __[A] Directions-only guide (no CTAs)__

  - Copy only: “Select a source folder or choose files on the left to begin.”
  - Pros: Zero duplication with left panel; minimal visual noise; simplest to maintain.
  - Cons: Users may still miss the first step if they ignore the left panel; relies entirely on panel discoverability.
* __[B] Directions + per-folder discovery toggle (current)__

  - Keep the existing “Enable file discovery for this folder” option in the guide when a folder is selected (preferred name; shim supports old “auto queue”).
  - Pros: Presents the decision at the right moment and in context; reduces left panel hunting; aligns with Directory Discovery MVP.
  - Cons: One option inside the guide can feel uneven when no other options are present; ensure `_current_folder` is set to avoid no-op.
* __[C] Directions + subtle helper links (no buttons)__

  - Short guidance plus small inline links: “learn more”, “where is file discovery?”. Links open docs/tooltips; no action duplication.
  - Pros: Keeps guide uncluttered; educates without adding controls.
  - Cons: Requires lightweight help content; still relies on users using the left panel for actions.
* __[D] Empty-state placeholder in file list (recommended regardless)__

  - Show centred text in the file view when empty: “No files yet — select a folder or choose files on the left.”
  - Pros: Addresses the largest blank area; orients users even if they overlook the guide.
  - Cons: Minor implementation effort in `UDFileView` (overlay/label in viewport).

### Selection criteria

- __Clarity first__: reduce ambiguity on the first-run screen without duplicating controls.
- __MVP compliance__: presenter remains Qt-free; view acts as switchboard; guide avoids becoming a second action hub.
- __Consistency__: discovery toggle text and behaviour use `enable_file_discovery_toggled` (old name shims only).

### Working recommendation

- Adopt __[A] + [B] + [D]__ together:
  - Directions-only baseline, retain the in-context discovery toggle when a folder is selected, and add the empty-state placeholder in the file view.
  - Result: Clear first step, no action duplication, and discovery option where it belongs.




>> 2025-08-10 @ 12:06:14
  
okay I think we need to discuss how this guide pane should work  and initial state should work 
A) it should fit to content  and the browser should take up the available sapce. 
Note: 1) the browser could have a a message in italics *files selected for processing will appear here...*
          2) the info bar should take its cues from state - simple messages like a) no files selected...
b) awaiting archive location...(if archive option menu changed to select folder on selection changed) 
c) processing...
d) proecessing complete: 

Getting back to the guide pane inital state, no files selected archive set to default: a draft sketch.
-------------------------------------------------------------------
source files: section 
   folder slot 1 No files or folders selected...
         per folder options slot (empty) section fits contents
------------------------------------------------------------------------
Archive: section 
   Archive:
          Same as source folder. (default)
               options slot 
-------------------------------------------------------------------------
Master File: section, not shown, for future use)
     destination: 
               options slot 
--------------------------------------------------------------------------
