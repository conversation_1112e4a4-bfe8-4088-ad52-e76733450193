# Clean Architecture: Update Data Module

## Logic Flow Diagram

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   User Action   │───▶│   Presenter      │───▶│  State Machine  │
│   (Qt Signal)   │    │  (Business Logic)│    │   (Pure Logic)  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   UI Widgets    │◀───│   View Manager   │◀───│  Event Bus      │
│   (PySide6)     │    │  (Configuration) │    │   (Messages)    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Simple Implementation

### 1. Pure Logic Model
```python
# models.py - Pure business logic
dataclass UpdateDataState:
    source_configured: bool = False
    destination_configured: bool = False
    processing: bool = False
    file_count: int = 0
    source_path: str = ""
    destination_path: str = ""

class UpdateDataLogic:
    def can_process(self, state: UpdateDataState) -> bool:
        return state.source_configured and state.destination_configured and not state.processing
    
    def get_status_message(self, state: UpdateDataState) -> str:
        if state.processing:
            return "Processing..."
        if self.can_process(state):
            return f"Ready to process {state.file_count} files"
        return "Select source files to begin"
```

### 2. Event Bus (Decoupled Communication)
```python
# events.py - Simple event system
from enum import Enum

class UpdateDataEvent(Enum):
    SOURCE_SELECTED = "source_selected"
    DESTINATION_SELECTED = "destination_selected"
    PROCESS_REQUESTED = "process_requested"
    PROCESS_COMPLETED = "process_completed"

class SimpleEventBus:
    def __init__(self):
        self._handlers = {}
    
    def subscribe(self, event: UpdateDataEvent, handler):
        self._handlers.setdefault(event, []).append(handler)
    
    def emit(self, event: UpdateDataEvent, data: dict):
        for handler in self._handlers.get(event, []):
            handler(data)
```

### 3. State Manager (Pure Logic)
```python
# state_manager.py - Simple state management
class StateManager:
    def __init__(self, event_bus: SimpleEventBus):
        self.event_bus = event_bus
        self.state = UpdateDataState()
        self._subscribe_to_events()
    
    def _subscribe_to_events(self):
        self.event_bus.subscribe(UpdateDataEvent.SOURCE_SELECTED, self._on_source_selected)
        self.event_bus.subscribe(UpdateDataEvent.DESTINATION_SELECTED, self._on_destination_selected)
    
    def _on_source_selected(self, data):
        self.state.source_configured = True
        self.state.file_count = data['file_count']
        self.state.source_path = data['path']
        self._emit_state_changed()
    
    def _emit_state_changed(self):
        self.event_bus.emit(UpdateDataEvent.STATE_CHANGED, {
            'state': self.state,
            'can_process': UpdateDataLogic().can_process(self.state),
            'status_message': UpdateDataLogic().get_status_message(self.state)
        })
```

### 4. Presenter (Business Logic Bridge)
```python
# presenter.py - Business logic coordinator
class UpdateDataPresenter:
    def __init__(self, event_bus: SimpleEventBus):
        self.event_bus = event_bus
        self.state_manager = StateManager(event_bus)
    
    def handle_source_selection(self, path: str, file_count: int):
        self.event_bus.emit(UpdateDataEvent.SOURCE_SELECTED, {
            'path': path,
            'file_count': file_count
        })
```

### 5. View Manager (Declarative UI)
```python
# view_manager.py - Pure configuration
class ViewManager:
    def __init__(self, event_bus: SimpleEventBus):
        self.event_bus = event_bus
        self.event_bus.subscribe(UpdateDataEvent.STATE_CHANGED, self._update_ui)
    
    def _update_ui(self, data):
        # Pure configuration - no business logic
        self._configure_buttons(data['can_process'])
        self._update_status_label(data['status_message'])
    
    def _configure_buttons(self, can_process: bool):
        # Simple declarative configuration
        self.process_button.setEnabled(can_process)
```

### 6. UI Widgets (Thin Layer)
```python
# ui_widgets.py - Thin UI layer
class UpdateDataView:
    def __init__(self):
        self.event_bus = SimpleEventBus()
        self.presenter = UpdateDataPresenter(self.event_bus)
        self.view_manager = ViewManager(self.event_bus)
        
        # Connect Qt signals to presenter
        self.source_button.clicked.connect(self._on_source_clicked)
    
    def _on_source_clicked(self):
        # Thin adapter - delegates to presenter
        path = self._select_folder()
        files = self._scan_files(path)
        self.presenter.handle_source_selection(path, len(files))
```

## Communication Flow

```
1. User clicks source button
2. Qt signal → View._on_source_clicked()
3. View → Presenter.handle_source_selection()
4. Presenter → EventBus.emit(SOURCE_SELECTED)
5. StateManager → EventBus.emit(STATE_CHANGED)
6. ViewManager → EventBus.subscribe(STATE_CHANGED)
7. ViewManager → Update UI widgets
```

## Key Benefits

- **No Circular Imports**: Each component depends only on abstractions
- **Testable**: Each layer can be tested independently
- **Decoupled**: UI changes don't affect business logic
- **Simple**: Each component has single responsibility
- **Pythonic**: Uses dataclasses, enums, and simple patterns

## Directory Structure

```
update_data/
├── models.py          # Pure data models
├── events.py          # Event definitions
├── state_manager.py   # Pure state logic
├── presenter.py       # Business logic
├── view_manager.py    # UI configuration
└── ui_widgets.py      # Thin UI layer
```
