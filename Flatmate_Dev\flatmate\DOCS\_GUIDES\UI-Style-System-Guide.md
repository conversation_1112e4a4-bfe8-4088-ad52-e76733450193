# UI Style System Guide

**Purpose**: Practical guide for widget development and maintenance in Flatmate's QSS styling system.

## Current System (Verified)

The app currently uses pyside6, Qaaplication and applied style sheets. 

### Styling Architecture
- **Single stylesheet**: `flatmate_consolidated.qss` (being renamed to `base_theme.qss`)
- **Font replacement**: `{{FONT_SIZE}}` → config value (default 14px) via `loader.py`
- **Theme experiment**: YAML hex replacement (disabled by default)

### Core Modules
- **`loader.py`**: Loads stylesheet, replaces `{{FONT_SIZE}}`, optional theme mapping
- **`applier.py`**: `apply_styles(app)` - applies to QApplication, logs provenance
- **`main.py`**: Explicitly calls `apply_styles(app)` during startup

### Widget Architecture (Actual)
- **`_shared_components/widgets/`**: Simple button/checkbox/menu components
  - `buttons.py`: `ActionButton`, `SecondaryButton`, `ExitButton` (use `setProperty("type", "action_btn")`)
  - `checkboxes.py`: `LabeledCheckBox` (QWidget container with QCheckBox)
  - `option_menus.py`: `OptionMenuWithLabel`, `SelectOptionGroupVLayout`
- **`_shared_components/table_view_v2/`**: Mature table component family
- **`_shared_components/tool_bar_base/`**: Toolbar buttons with inline styling
- **`styles/button_types.py`**: **UNUSED** - ButtonType enum exists but only used in archived code

## QSS Styling Patterns (Working)

### Button Types (via setProperty)
```python
# Actual working button types in consolidated stylesheet
button.setProperty("type", "action_btn")    # Primary actions - 45px height, bold
button.setProperty("type", "nav_btn")       # Navigation - 35px height
button.setProperty("type", "select_btn")    # Option menus - 26px height
button.setProperty("type", "exit_btn")      # Exit/cancel - muted colors
```

### Object Names (for specific styling)
```python
# Panel-specific styling
widget.setObjectName("left_panel")         # Nav background color
widget.setObjectName("content_area")       # Main content styling
widget.setObjectName("file_tree")          # File display styling

# Label types
label.setObjectName("heading")             # 1.7em, bold
label.setObjectName("subheading")          # 1.3em, #CCCCCC color
```

### Font System
- **Base size**: `{{FONT_SIZE}}px` replaced with config value
- **Relative sizing**: `1.15em` (action buttons), `1.3em` (subheadings), `1.7em` (headings)

## Widget Development Patterns

### Simple Widgets (Current Working Pattern)
```python
class ActionButton(QPushButton):
    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        self.setProperty("type", "action_btn")  # QSS handles styling
```

### Composite Widgets (Current Working Pattern)
```python
class OptionMenuWithLabel(QWidget):
    def __init__(self, label_text, options, parent=None):
        super().__init__(parent)
        self.label = QLabel(label_text)
        self.label.setObjectName("subheading")  # QSS styling
        self.combo_box = QComboBox()            # QSS handles styling
```

### Inline Styling (Tool Bar Pattern)
```python
# BaseToolbarButton uses inline setStyleSheet() - different approach
class BaseToolbarButton(QPushButton):
    def _apply_styling(self, variant):
        self.setStyleSheet(f"QPushButton {{ background: {color}; }}")
```

## Theme System (Working Implementation)

### Current Theme Support
- **Default**: Disabled (`ENABLE_THEME_EXPERIMENT = False` in `loader.py`)
- **Mechanism**: YAML `hex_map` with exact string replacement
- **Working example**: `themes/theme-light.yaml`

### How It Actually Works
```python
# From loader.py - the real implementation
def _apply_hex_map(stylesheet: str, hex_map: Dict[str, str]) -> Tuple[str, Dict[str, int]]:
    counts: Dict[str, int] = {}
    out = stylesheet
    for key, new_val in hex_map.items():
        literal = f"#{key}"  # Add # prefix to key
        count_before = out.count(literal)
        if count_before:
            out = out.replace(literal, new_val)
        counts[literal] = count_before
    return out, counts
```

### YAML Format (Actual)
```yaml
# themes/theme-light.yaml
hex_map:
  # Key = hex WITHOUT #, Value = hex WITH #
  "1E1E1E": "#FFFFFF"   # Dark background → White
  "3B8A45": "#2E7D32"   # Primary green → Light green
  "1a381f": "#E8F5E8"   # Nav background → Light green
```
This system is in its infancy it works, but it needs refinement .
### To Enable Themes
```python
# In loader.py
ENABLE_THEME_EXPERIMENT = True
DEFAULT_THEME_NAME = "light"  # Uses themes/theme-light.yaml
```

## Maintenance Tasks

### Audit Tools (Available)
- **`AUDIT_STYLESHEET = True`** in `loader.py` generates:
  - `debug_combined_output.qss` - What loader produces
  - `debug_qt_applied.qss` - What Qt actually applies
  - `debug_actual_colors.txt` - Widget color inspection (needs fixing)

### Clean Up Tasks
1. **Remove unused code**: `styles/button_types.py` (only used in archived modules)
2. **Rename file**: `flatmate_consolidated.qss` → `base_theme.qss`
3. **Move loader/applier**: Out of `styles/__init__.py` into separate modules

### Widget Development Checklist
- [ ] Use `setProperty("type", "...")` for button variants
- [ ] Use `setObjectName("...")` for specific styling needs
- [ ] No inline `setStyleSheet()` unless toolbar component
- [ ] Test with `AUDIT_STYLESHEET = True` to verify styling
- [ ] Document QSS dependencies in widget docstring

## Current Color Palette
**Complete palette**: See `flatmate/src/fm/gui/styles/flatmate_consolidated.qss`

**Key colors (partial list)**:
```css
/* Primary colors */
#3B8A45  /* Primary green */
#4BA357  /* Primary hover */
#2E6E37  /* Primary pressed */

/* Navigation */
#1a381f  /* Nav background */
#1E1E1E  /* Content background */
```
**Note**: Full palette includes secondary button colors (#3B7443, #488E52, etc.), scrollbar colors, and all UI element colors. Reference the consolidated stylesheet for complete color definitions.

## References
- **Loader**: `flatmate/src/fm/gui/styles/loader.py`
- **Applier**: `flatmate/src/fm/gui/styles/applier.py`
- **Stylesheet**: `flatmate/src/fm/gui/styles/flatmate_consolidated.qss`
- **Widgets**: `flatmate/src/fm/gui/_shared_components/widgets/`