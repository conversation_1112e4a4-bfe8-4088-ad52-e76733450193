# QSS Loader Audit Methods

**Date**: 2025-01-30  
**Purpose**: Debug and understand the actual QSS loading and application process  
**Location**: `flatmate/src/fm/gui/styles/__init__.py`

## Problem Statement

We need to understand:
1. What the loader actually produces from combining `theme.qss` + `style.qss`
2. What Qt actually applies after processing our combined QSS
3. How the current system produces correct colors despite apparent conflicts
4. Whether Qt is doing any QSS processing/resolution we're not aware of

## Proposed Solution: Audit Functions in Loader

### Implementation Strategy

Add audit functions to the existing loader that can be toggled on/off via a boolean flag.

```python
# At top of flatmate/src/fm/gui/styles/__init__.py
AUDIT_STYLESHEET = True  # Set to False to disable debugging
```

### Method 1: Capture Loader Output

```python
def _audit_loader_output(combined_stylesheet: str, styles_dir: Path) -> None:
    """Audit what the loader produces before sending to Qt."""
    if not AUDIT_STYLESHEET:
        return
        
    # Save the combined output
    debug_file = styles_dir / "debug_combined_output.qss"
    with open(debug_file, 'w', encoding='utf-8') as f:
        f.write(combined_stylesheet)
    
    # Print summary
    print(f"\n=== LOADER AUDIT ===")
    print(f"Combined stylesheet saved to: {debug_file}")
    print(f"Total length: {len(combined_stylesheet)} characters")
    print(f"Total lines: {len(combined_stylesheet.splitlines())}")
    
    # Check for CSS variables
    var_count = combined_stylesheet.count('var(--')
    print(f"CSS variables found: {var_count}")
    
    # Check for font size placeholders
    placeholder_count = combined_stylesheet.count('{{FONT_SIZE}}')
    print(f"Font size placeholders found: {placeholder_count}")
    
    # Check for common selectors
    selectors_to_check = [
        '#left_panel',
        '#right_panel', 
        'QPushButton[type="primary"]',
        'QWidget'
    ]
    
    print(f"\nSelector occurrences:")
    for selector in selectors_to_check:
        count = combined_stylesheet.count(selector)
        print(f"  {selector}: {count} times")
```

### Method 2: Capture Qt Applied Stylesheet

```python
def _audit_qt_applied(app: QApplication, styles_dir: Path) -> None:
    """Audit what Qt actually applied after processing."""
    if not AUDIT_STYLESHEET:
        return
        
    # Get what Qt actually has
    actual_applied = app.styleSheet()
    
    # Save Qt's version
    debug_file = styles_dir / "debug_qt_applied.qss"
    with open(debug_file, 'w', encoding='utf-8') as f:
        f.write(actual_applied)
    
    print(f"\n=== QT APPLICATION AUDIT ===")
    print(f"Qt applied stylesheet saved to: {debug_file}")
    print(f"Applied length: {len(actual_applied)} characters")
    print(f"Applied lines: {len(actual_applied.splitlines())}")
```

### Method 3: Compare Loader vs Qt Applied

```python
def _audit_compare_stylesheets(loader_output: str, qt_applied: str, styles_dir: Path) -> None:
    """Compare what we sent vs what Qt applied."""
    if not AUDIT_STYLESHEET:
        return
        
    print(f"\n=== COMPARISON AUDIT ===")
    
    if loader_output == qt_applied:
        print("✓ Qt applied stylesheet UNCHANGED from loader output")
    else:
        print("⚠ Qt MODIFIED the stylesheet during application")
        
        # Save the differences
        diff_file = styles_dir / "debug_differences.txt"
        with open(diff_file, 'w', encoding='utf-8') as f:
            f.write("=== LOADER OUTPUT ===\n")
            f.write(f"Length: {len(loader_output)} chars\n")
            f.write(f"Lines: {len(loader_output.splitlines())}\n\n")
            
            f.write("=== QT APPLIED ===\n")
            f.write(f"Length: {len(qt_applied)} chars\n")
            f.write(f"Lines: {len(qt_applied.splitlines())}\n\n")
            
            f.write("=== FIRST 1000 CHARS OF EACH ===\n")
            f.write("LOADER:\n")
            f.write(loader_output[:1000])
            f.write("\n\nQT APPLIED:\n")
            f.write(qt_applied[:1000])
        
        print(f"Differences saved to: {diff_file}")
```

### Method 4: CSS Variable Resolution Check

```python
def _audit_css_variables(stylesheet: str) -> None:
    """Check if CSS variables are being resolved."""
    if not AUDIT_STYLESHEET:
        return
        
    print(f"\n=== CSS VARIABLE AUDIT ===")
    
    # Find all var() calls
    import re
    var_pattern = r'var\(--[\w-]+\)'
    var_matches = re.findall(var_pattern, stylesheet)
    
    if var_matches:
        print(f"Found {len(var_matches)} CSS variable calls:")
        for var_call in set(var_matches):  # Remove duplicates
            count = stylesheet.count(var_call)
            print(f"  {var_call}: {count} times")
    else:
        print("No CSS variable calls found")
    
    # Find all CSS variable definitions
    def_pattern = r'--[\w-]+:\s*[^;]+;'
    def_matches = re.findall(def_pattern, stylesheet)
    
    if def_matches:
        print(f"\nFound {len(def_matches)} CSS variable definitions:")
        for var_def in def_matches:
            print(f"  {var_def}")
    else:
        print("No CSS variable definitions found")
```

## Updated Loader Implementation

```python
# Updated flatmate/src/fm/gui/styles/__init__.py

# Audit control flag
AUDIT_STYLESHEET = True  # Set to False to disable debugging

def load_styles() -> str:
    """Load and combine application styles."""
    styles_dir = Path(__file__).parent
    
    # Load base theme and styles
    with open(styles_dir / "theme.qss", 'r') as f:
        theme = f.read()
    with open(styles_dir / "style.qss", 'r') as f:
        style = f.read()
        
    # Apply any dynamic values
    font_size = config.get_value(ConfigKeys.App.BASE_FONT_SIZE, 14)
    combined = theme + "\n" + style
    combined = combined.replace('{{FONT_SIZE}}', str(font_size))
    
    # AUDIT: Capture loader output
    _audit_loader_output(combined, styles_dir)
    _audit_css_variables(combined)
    
    return combined

def apply_styles(app: QApplication) -> None:
    """Apply styles to the application."""
    styles_dir = Path(__file__).parent
    stylesheet = load_styles()
    app.setStyleSheet(stylesheet)
    
    # AUDIT: Capture what Qt applied
    _audit_qt_applied(app, styles_dir)
    
    # AUDIT: Compare loader vs Qt applied
    qt_applied = app.styleSheet()
    _audit_compare_stylesheets(stylesheet, qt_applied, styles_dir)

# [Include all the audit functions here]
```

## Expected Debug Output Files

When `AUDIT_STYLESHEET = True`, the following files will be created in the styles directory:

1. **`debug_combined_output.qss`** - What the loader produces
2. **`debug_qt_applied.qss`** - What Qt actually applied
3. **`debug_differences.txt`** - Comparison if they differ

## Console Output Example

```
=== LOADER AUDIT ===
Combined stylesheet saved to: debug_combined_output.qss
Total length: 15847 characters
Total lines: 589
CSS variables found: 23
Font size placeholders found: 0

Selector occurrences:
  #left_panel: 4 times
  #right_panel: 4 times
  QPushButton[type="primary"]: 3 times
  QWidget: 2 times

=== CSS VARIABLE AUDIT ===
Found 23 CSS variable calls:
  var(--color-primary): 5 times
  var(--color-text-primary): 8 times
  var(--color-bg-dark): 3 times

No CSS variable definitions found

=== QT APPLICATION AUDIT ===
Qt applied stylesheet saved to: debug_qt_applied.qss
Applied length: 15847 characters
Applied lines: 589

=== COMPARISON AUDIT ===
✓ Qt applied stylesheet UNCHANGED from loader output
```

## Usage Instructions

1. Set `AUDIT_STYLESHEET = True` at top of `__init__.py`
2. Run the application
3. Check console output for audit results
4. Examine generated debug files in styles directory
5. Set `AUDIT_STYLESHEET = False` when done debugging

This approach gives us complete visibility into the QSS loading and application process without affecting normal operation when auditing is disabled.

## Test Results - 2025-08-05 12:35:35

### Key Findings

#### 1. CSS Variables Are Present But Not Resolved
- **Combined stylesheet contains 34 CSS variable calls** (`var(--color-name)`)
- **palette.qss defines 19 CSS variables** but is never loaded due to non-functional `@import`
- **Qt applies stylesheet unchanged** - no CSS variable resolution occurs
- **Application renders correctly** despite broken CSS variables

#### 2. Fallback Color System Working
The app displays correct colors even with broken `var()` calls because:
- **Hardcoded colors in theme.qss** provide fallbacks (e.g., `#1a382f` for nav panels)
- **Qt's default styling** fills gaps where CSS variables fail
- **Some CSS variables are in commented sections** (lines 139-213 are commented out)

#### 3. Widget Object Names Not Found
- `left_panel`, `right_panel`, `right_side_bar`, `file_tree` widgets not found by `findChildren()`
- Suggests widgets either:
  - Use different object names
  - Are created after the 1-second delay
  - Are nested in ways that prevent discovery

#### 4. Actual System Behavior
```
theme.qss (215 lines) + style.qss (374 lines) = 589 lines total
├── CSS variables: 34 calls to undefined variables
├── Hardcoded colors: Provide actual styling
├── Qt processing: No variable resolution
└── Result: App works due to fallback colors
```

### Debug Files Generated
- `debug_combined_output.qss` (589 lines) - What loader produces
- `debug_qt_applied.qss` (589 lines) - What Qt applies (identical)
- `debug_actual_colors.txt` - Widget colors (widgets not found)

### Critical Discovery
**The CSS variable system is completely non-functional**, but the app works because:
1. Many styles use hardcoded colors as fallbacks
2. Qt provides default styling where CSS fails
3. Some problematic CSS variable sections are commented out

This explains why consolidating to a single static QSS file with hardcoded colors is the right approach.

## Research Update: Qt CSS Variable Support Investigation

### Web Research Findings (2025-08-05)

#### Qt CSS Variable Support Status
- **No official documentation** confirming CSS variable (`var()`) support in Qt 6.9
- **Stack Overflow evidence** suggests CSS variables are NOT supported in Qt Style Sheets
- **Qt Style Sheet documentation** focuses on traditional CSS properties only
- **No CSS variable examples** found in Qt documentation or examples

#### Qt Debug Logging System
From Qt Forum research, Qt provides extensive logging categories:
```
qt.qpa.*           - Platform abstraction layer
qt.widgets.*       - Widget system debugging
qt.gui.*           - GUI system debugging
qt.accessibility.* - Accessibility system
qt.text.*          - Text rendering system
```

**Notable absence**: No `qt.qss.*`, `qt.stylesheet.*`, or `qt.css.*` categories found in the 230+ Qt logging categories.

#### Recommended Debug Testing
```python
# Add to main.py or audit system:
import os

# Test different Qt logging categories
os.environ['QT_LOGGING_RULES'] = 'qt.widgets.painting=true'
# or try: 'qt.gui.*=true'
# or try: '*=true;qt*=false'  # All non-Qt categories only
```

### Hypothesis Status: UNCONFIRMED

#### Original Hypothesis
- CSS variables completely broken
- Qt silently ignores `var()` calls
- App works due to hardcoded fallbacks

#### Current Status
- **CSS variable support**: UNKNOWN (needs testing with Qt debug logging)
- **@import functionality**: UNKNOWN (needs verification)
- **Variable resolution**: UNKNOWN (no Qt warnings doesn't prove functionality)

### Next Investigation Phase

1. **Enable Qt Debug Logging** in audit system
2. **Test minimal CSS variable** to verify functionality
3. **Check @import behavior** with debug logging
4. **Improve widget discovery** with longer delays and better methods

The consolidation approach remains valid regardless of CSS variable functionality, but proper investigation is needed before drawing conclusions.
