"""
Update Data view implementation.
"""

import os
from pathlib import Path
from typing import List, Optional, Dict, Any

from PySide6.QtCore import Signal
from PySide6.QtWidgets import QMessageBox, QFileDialog

from fm.core.services.event_bus import global_event_bus
from fm.core.services.logger import log
from fm.modules.base.base_module_view import BaseModuleView
from ._view.center_panel_layout import CenterPanelLayout
from ._view.left_panel_layout import LeftPanelLayout
from ..services.local_event_bus import (
    ViewEvents,
    update_data_local_bus,
)
from .interface import IUpdateDataView


class UpdateDataView(BaseModuleView):
    # Queue UI work onto the main thread to avoid cross-thread UI updates
    files_display_update_requested = Signal(object)
    """
    Update Data view.

    Implements IUpdateDataView interface methods for presenter interaction.
    """
    cancel_clicked = Signal()
    source_select_requested = Signal(str)
    save_select_requested = Signal()
    source_option_changed = Signal(str)
    save_option_changed = Signal(str)
    process_clicked = Signal()
    update_database_changed = Signal(bool)

    def __init__(self, parent=None, gui_config=None, gui_keys=None):
        self.event_bus = global_event_bus
        self.local_bus = update_data_local_bus
        super().__init__(parent, gui_config, gui_keys)
        # Direct handler for discovery toggle (wired by presenter)
        self._discovery_toggle_handler = None
        # Connect cross-thread safe updater
        try:
            self.files_display_update_requested.connect(self._apply_files_display_update)
        except Exception:
            pass

    def set_discovery_toggle_handler(self, handler) -> None:
        """Presenter wires this to receive discovery toggle calls directly."""
        self._discovery_toggle_handler = handler

    def setup_ui(self):
        self.left_panel_manager = LeftPanelLayout()
        self.center_display = CenterPanelLayout()
        # Direct handle to the smart file widget; layout manager is layout-only
        self.ud_file_view = getattr(self.center_display, 'ud_file_view', None)
        # Use the guide pane managed by CenterPanelLayout (single source of truth)
        self.guide_pane = getattr(self.center_display, 'guide_pane', None)
        # Signal router: wire GuidePaneWidget toggle intent -> view handler (canonical path)
        # This is the single source for discovery toggle user intent; no duplicate wiring.
        if (
            hasattr(self, 'guide_pane') and self.guide_pane
            and hasattr(self.guide_pane, 'publish_enable_file_discovery_toggled')
            and True
        ):
            self.guide_pane.publish_enable_file_discovery_toggled.connect(self.on_enable_file_discovery_toggled)
        self._subscribe_to_events()
        self._connect_signals()
        self._connect_center_pane_intents()
        # Notify presenter explicitly that the UI is ready
        handler = getattr(self, '_ready_handler', None)
        if callable(handler):
            handler()

    def set_ready_handler(self, handler) -> None:
        """Presenter wires this to be notified when setup_ui completes."""
        self._ready_handler = handler

    def setup_left_panel(self, layout):
        layout.addWidget(self.left_panel_manager)

    def setup_center_panel(self, layout):
        layout.addWidget(self.center_display)

    

    def _subscribe_to_events(self):
        # View renders from state events and file list updates
        self.local_bus.subscribe(ViewEvents.UI_STATE_CHANGED.value, self.update_ui_state)
        self.local_bus.subscribe(ViewEvents.STATUS_MESSAGE_CHANGED.value, self.update_status_message)
        self.local_bus.subscribe(ViewEvents.FILE_DISPLAY_UPDATED.value, self.update_files_display)
        # Single canonical path: Managers -> FILE_LIST_UPDATED -> View
        self.local_bus.subscribe(ViewEvents.FILE_LIST_UPDATED.value, self.update_files_display)

        # Processing lifecycle routed via StateManager -> UI_STATE_CHANGED only. No local bus processing lifecycle here.
        self.local_bus.subscribe(ViewEvents.ERROR_DIALOG_REQUESTED.value, self._on_dialog_requested)
        self.local_bus.subscribe(ViewEvents.SUCCESS_DIALOG_REQUESTED.value, self._on_dialog_requested)
    
    def _connect_signals(self):
        # Translate LeftPanel Qt signals into typed user-intent events on Local Event Bus
        # Use enumerated option types where applicable to ensure consistent payloads
        from ..models.config import SourceOptions, SaveOptions
        def _to_enum_or_label(value):
            # Prefer enum objects for canonical options; allow MRU labels (str) to pass through
            if isinstance(value, SourceOptions):
                return value
            if value == SourceOptions.SELECT_FILES.value:
                return SourceOptions.SELECT_FILES
            if value == SourceOptions.SELECT_FOLDER.value:
                return SourceOptions.SELECT_FOLDER
            return value
        def _to_save_enum_or_label(value):
            # Prefer enum objects for canonical save options; allow MRU labels (str) to pass through
            if isinstance(value, SaveOptions):
                return value
            if value == SaveOptions.SAME_AS_SOURCE.value:
                return SaveOptions.SAME_AS_SOURCE
            if value == SaveOptions.SELECT_LOCATION.value:
                return SaveOptions.SELECT_LOCATION
            return value

        # IMPORTANT: Avoid duplicate dialog opens by ensuring only ONE source of SOURCE_SELECT_REQUESTED.
        # Separate channels to prevent collisions:
        # - Left Panel source-select (folder/files) -> SOURCE_SELECT_REQUESTED
        # - Center pane "Add files" -> ADD_FILES_REQUESTED (distinct)
        # Connect directly to underlying widget signals (shifted out of LeftPanelLayout)
        # Source select button -> emit with current selected option
        self.left_panel_manager.source_options_group.button_clicked.connect(
            lambda: self.local_bus.emit(
                ViewEvents.SOURCE_SELECT_REQUESTED.value,
                _to_enum_or_label(
                    self.left_panel_manager.source_options_group.get_selected_option()
                )
            )
        )
        # Destination select button
        self.left_panel_manager.archive_options_group.button_clicked.connect(
            lambda: self.local_bus.emit(ViewEvents.DESTINATION_SELECT_REQUESTED.value, None)
        )
        # Process and Cancel buttons
        self.left_panel_manager.action_button.clicked.connect(
            lambda: self.local_bus.emit(ViewEvents.PROCESS_REQUESTED.value, None)
        )
        self.left_panel_manager.exit_button.clicked.connect(
            lambda: self.local_bus.emit(ViewEvents.CANCEL_REQUESTED.value, None)
        )
        # Option changes (source/archive)
        self.left_panel_manager.source_options_group.option_changed.connect(
            lambda opt: self.local_bus.emit(
                ViewEvents.SOURCE_OPTION_CHANGED.value,
                _to_enum_or_label(opt)
            )
        )
        self.left_panel_manager.archive_options_group.option_changed.connect(
            lambda opt: self.local_bus.emit(
                ViewEvents.SAVE_OPTION_CHANGED.value,
                _to_save_enum_or_label(opt)
            )
        )
        # Database checkbox
        self.left_panel_manager.database_checkbox.state_changed.connect(
            lambda checked: self.local_bus.emit(ViewEvents.UPDATE_DATABASE_CHANGED.value, checked)
        )

    def _connect_center_pane_intents(self):
        """
        Connect centre pane (UDFileView) user intents to Local Event Bus.
        No fallbacks. Single UDFileView instance is accessed directly via self.ud_file_view.
        """
        file_view = self.ud_file_view
        file_view.add_files_requested.connect(
            lambda: self.local_bus.emit(ViewEvents.ADD_FILES_REQUESTED.value, None)
        )
        log.debug("[UD_VIEW] Wired UDFileView.add_files_requested -> add_files_requested")

    def on_enable_file_discovery_toggled(self, folder: str, enabled: bool) -> None:
        """Guide pane toggle intent → call presenter directly (no event)."""
        norm_folder = str(Path(folder).expanduser().resolve()) if folder else ""
        if not norm_folder:
            log.warning("[UD_VIEW] enable_file_discovery_toggled with empty folder; presenter will decide")
        log.debug(f"[UD_VIEW] enable_file_discovery_toggled: folder='{norm_folder}', enabled={bool(enabled)}")
        if callable(self._discovery_toggle_handler):
            self._discovery_toggle_handler(norm_folder or None, bool(enabled))
        else:
            log.warning("[UD_VIEW] Discovery toggle handler not set; ignoring toggle")

    def _on_dialog_requested(self, event_data):
        """Always show an error dialogue with the provided message (minimal handler)."""
        # Accept dataclass or dict
        message = ""
        if isinstance(event_data, dict):
            extra = event_data.get("extra_data") or {}
            message = extra.get("message") or event_data.get("message") or ""
        else:
            extra = getattr(event_data, "extra_data", {}) or {}
            message = extra.get("message") or getattr(event_data, "message", "") or ""
        QMessageBox.critical(self, "Error", message or "An error occurred.")

    def update_ui_state(self, ui_state_data):
        if 'can_process' in ui_state_data:
            self.set_process_enabled(ui_state_data['can_process'])
        if 'archive_enabled' in ui_state_data:
            self.set_archive_enabled(ui_state_data['archive_enabled'])
        if 'process_button_text' in ui_state_data:
            self.set_process_button_text(ui_state_data['process_button_text'])
        if 'processing' in ui_state_data:
            self.set_all_controls_enabled(not ui_state_data['processing'])

    # === Relay helpers used by adapter/presenter ===
    def set_process_enabled(self, enabled: bool) -> None:
        """Enable/disable the Process button (Left Panel)."""
        self.left_panel_manager.action_button.setEnabled(bool(enabled))

    # === Guide pane namespaced API (Option 1) ===
    def guide_display(self, text: str) -> None:
        if not getattr(self, 'guide_pane', None):
            raise RuntimeError("Guide pane not initialised")
        # GuidePaneV2 mapping
        if hasattr(self.guide_pane, 'set_main_message'):
            self.guide_pane.set_main_message(text)
            return
        # Fail fast if the expected API is missing
        raise AttributeError("GuidePaneV2 is missing set_main_message")

    def guide_set_status(self, status: str) -> None:
        if not getattr(self, 'guide_pane', None):
            raise RuntimeError("Guide pane not initialised")
        if hasattr(self.guide_pane, 'set_status'):
            self.guide_pane.set_status(status)

    def guide_set_source_summary(self, text: str) -> None:
        if not getattr(self, 'guide_pane', None):
            raise RuntimeError("Guide pane not initialised")
        # GuidePaneV2 maps to set_section_info('source', text)
        if hasattr(self.guide_pane, 'set_section_info'):
            self.guide_pane.set_section_info('source', text)
            return
        raise AttributeError("GuidePaneV2 is missing set_section_info for source")

    def guide_show_source_context_options(self, *, discovery_enabled: bool = False, folder: str | None = None) -> None:
        if not getattr(self, 'guide_pane', None):
            raise RuntimeError("Guide pane not initialised")
        if hasattr(self.guide_pane, 'show_source_context_options'):
            self.guide_pane.show_source_context_options(discovery_enabled=discovery_enabled, folder=folder)

    def guide_set_archive_summary(self, text: str) -> None:
        if not getattr(self, 'guide_pane', None):
            raise RuntimeError("Guide pane not initialised")
        if hasattr(self.guide_pane, 'set_archive_summary'):
            self.guide_pane.set_archive_summary(text)

    # Back-compat: existing callers may use set_guide_archive_summary
    def set_guide_archive_summary(self, text: str) -> None:
        self.guide_set_archive_summary(text)

    def guide_set_source_notice(self, text: str) -> None:
        if not getattr(self, 'guide_pane', None):
            raise RuntimeError("Guide pane not initialised")
        if hasattr(self.guide_pane, 'set_source_notice'):
            self.guide_pane.set_source_notice(text)

    def guide_clear_source_notice(self) -> None:
        if not getattr(self, 'guide_pane', None):
            raise RuntimeError("Guide pane not initialised")
        if hasattr(self.guide_pane, 'clear_source_notice'):
            self.guide_pane.clear_source_notice()

    def guide_set_actions_enabled(self, *, process_enabled: bool, reset_enabled: bool) -> None:
        if not getattr(self, 'guide_pane', None):
            raise RuntimeError("Guide pane not initialised")
        if hasattr(self.guide_pane, 'set_actions_enabled'):
            self.guide_pane.set_actions_enabled(process_enabled=bool(process_enabled), reset_enabled=bool(reset_enabled))

    def guide_set_discovery_badge(self, active: bool) -> None:
        if not getattr(self, 'guide_pane', None):
            raise RuntimeError("Guide pane not initialised")
        if hasattr(self.guide_pane, 'set_discovery_badge'):
            self.guide_pane.set_discovery_badge(bool(active))

    def set_source_options(self, options: list[str], selected: str | None = None) -> None:
        """Set full list of source options and optionally select one.

        This is the concrete view method expected by FileConfigManager.
        """
        try:
            grp = getattr(self.left_panel_manager, 'source_options_group', None)
            try:
                log.debug(f"[UD_VIEW] set_source_options: opts={len(options)}, selected={selected}, has_group={bool(grp)}")
            except Exception:
                pass
            if grp and hasattr(grp, 'set_options'):
                # Prefer silent, developer-friendly API if available
                if hasattr(grp, 'set_options_and_select'):
                    grp.set_options_and_select(options, selected=selected, silent=True)
                else:
                    grp.set_options(options)
                    if selected:
                        grp.set_selected_option(selected)
        except Exception as e:
            log.error(f"[UD_VIEW] Error setting source options: {e}")

    def set_archive_options(self, options: list[str], selected: str | None = None) -> None:
        """Set full list of archive options and optionally select one.

        This is the concrete view method expected by FileConfigManager.
        """
        try:
            grp = getattr(self.left_panel_manager, 'archive_options_group', None)
            if grp and hasattr(grp, 'set_options'):
                # Prefer silent, developer-friendly API if available
                if hasattr(grp, 'set_options_and_select'):
                    grp.set_options_and_select(options, selected=selected, silent=True)
                else:
                    grp.set_options(options)
                    if selected:
                        grp.set_selected_option(selected)
        except Exception as e:
            log.error(f"[UD_VIEW] Error setting archive options: {e}")

    def set_process_button_text(self, text: str) -> None:
        """Update Process button text (Left Panel)."""
        self.left_panel_manager.action_button.setText(text)

    def set_all_controls_enabled(self, enabled: bool) -> None:
        """Globally enable/disable primary controls during processing.

        For MVP we at least toggle the Process button. Extend as needed.
        """
        self.left_panel_manager.action_button.setEnabled(bool(enabled))

    def set_save_select_enabled(self, enabled: bool) -> None:
        """Enable/disable destination selection controls (Left Panel)."""
        self.left_panel_manager.archive_options_group.set_button_enabled(bool(enabled))

    def set_archive_enabled(self, enabled: bool):
        """Alias to set_save_select_enabled for semantic clarity."""
        self.set_save_select_enabled(enabled)

    def get_save_option(self) -> str:
        """Get current save option selection."""
        return self.left_panel_manager.archive_options_group.get_selected_option()

    def get_update_database(self) -> bool:
        """Get update database checkbox state."""
        return self.left_panel_manager.database_checkbox.is_checked()

    def set_source_option(self, option: str) -> None:
        """Set source option selection (Left Panel)."""
        self.left_panel_manager.source_options_group.set_selected_option(option)

    def update_status_message(self, message_data):
        if hasattr(self, 'guide_pane') and 'message' in message_data:
            self.guide_pane.display(message_data['message'])

    def update_files_display(self, files_data):
        log.debug(f"[UD_VIEW] Received file display update: {type(files_data)}")
        # Always marshal to UI thread to avoid cross-thread UI updates
        try:
            self.files_display_update_requested.emit(files_data)
        except Exception as e:
            log.error(f"[UD_VIEW] Failed to enqueue files display update: {e}")

    def _apply_files_display_update(self, files_data):
        # Accept dataclass FileListUpdatedEvent or FileDisplayUpdateEvent or dict payloads
        # Direct-widget paradigm: route straight to UDFileView (no layout-manager middleman)
        try:
            # Resolve UDFileView once
            file_view = getattr(self, 'ud_file_view', None)

            if hasattr(files_data, 'files'):
                files = getattr(files_data, 'files', []) or []
                source_path = getattr(files_data, 'source_path', "") or ""

                # Handle both FileInfoData objects and plain strings for backward compatibility
                if files and hasattr(files[0], 'path'):
                    # New format: FileInfoData objects - pass enriched data directly to file pane
                    log.debug(f"[UD_VIEW] Received {len(files)} enriched FileInfoData objects")
                    if file_view:
                        file_view.set_enriched_files(files)  # Pass enriched objects directly
                else:
                    # Legacy format: plain file paths
                    file_paths = files
                    log.debug(f"[UD_VIEW] Received {len(files)} plain file paths")
                    if file_view:
                        file_view.set_files(file_paths)
                # Optionally display source path hint without filtering
                if source_path and hasattr(self.center_display, 'set_source_path'):
                    self.center_display.set_source_path(source_path)
                # Also update guide pane source summary directly to reflect new files
                try:
                    count = len(files)
                    if hasattr(self, 'guide_set_source_summary') and source_path:
                        self.guide_set_source_summary(f"{source_path} — {count} file(s) listed")
                except Exception:
                    pass
            elif isinstance(files_data, dict) and 'files' in files_data:
                files = files_data.get('files', []) or []
                source_path = files_data.get('source_path', "") or ""

                # Handle both FileInfoData objects and plain strings for backward compatibility
                if files and hasattr(files[0], 'path'):
                    # New format: FileInfoData objects - pass enriched data directly to file pane
                    log.debug(f"[UD_VIEW] Received {len(files)} enriched FileInfoData objects (dict format)")
                    if file_pane:
                        file_pane.set_enriched_files(files)  # Pass enriched objects directly
                else:
                    # Legacy format: plain file paths
                    file_paths = files
                    log.debug(f"[UD_VIEW] Received {len(files)} plain file paths (dict format)")
                    if file_pane:
                        file_pane.set_files(file_paths)
                if source_path and hasattr(self.center_display, 'set_source_path'):
                    self.center_display.set_source_path(source_path)
                
            else:
                log.warning(f"[UD_VIEW] Unknown files_data format: {files_data}")
        except Exception as e:
            log.error(f"[UD_VIEW] Error updating files display: {e}")



    def set_process_button_text(self, text: str):
        if hasattr(self, 'left_panel_manager') and hasattr(self.left_panel_manager, 'action_button'):
            self.left_panel_manager.action_button.setText(text)

    def set_all_controls_enabled(self, enabled: bool):
        if hasattr(self, 'left_panel_manager') and hasattr(self.left_panel_manager, 'action_button'):
            self.left_panel_manager.action_button.setEnabled(bool(enabled))

    def set_save_path(self, path: str):
        if hasattr(self, 'center_display'):
            self.center_display.set_save_path(path)

    def set_archive_options(self, options: list[str], selected: str | None = None) -> None:
        """Set full list of archive options and optionally select one."""
        try:
            grp = getattr(self.left_panel_manager, 'archive_options_group', None) if hasattr(self, 'left_panel_manager') else None
            if grp and hasattr(grp, 'set_options'):
                grp.set_options(options)
                if selected:
                    grp.set_selected_option(selected)
        except Exception as e:
            from fm.core.services.logger import log
            log.error(f"[UD_VIEW] Error setting archive options: {e}")

    def set_guide_archive_summary(self, text: str) -> None:
        """Back-compat: delegate to namespaced guide method (no presenter notification)."""
        self.guide_set_archive_summary(text)

    def get_save_option(self) -> str:
        """Get current save option selection."""
        try:
            grp = getattr(self.left_panel_manager, 'archive_options_group', None)
            if grp and hasattr(grp, 'get_selected_option'):
                return grp.get_selected_option() or ""
        except Exception as e:
            log.error(f"[UD_VIEW] Error getting save option: {e}")
        return ""

    def get_update_database(self) -> bool:
        """Get update database checkbox state."""
        try:
            if hasattr(self, 'left_panel_manager') and hasattr(self.left_panel_manager, 'database_checkbox'):
                return bool(self.left_panel_manager.database_checkbox.is_checked())
        except Exception as e:
            log.error(f"[UD_VIEW] Error getting update database state: {e}")
        return False

    def set_save_select_enabled(self, enabled: bool):
        """Enable/disable save selection controls."""
        if hasattr(self, 'left_panel_manager') and hasattr(self.left_panel_manager, 'set_archive_enabled'):
            self.left_panel_manager.set_archive_enabled(enabled)

    def set_source_option(self, option: str):
        """Set source option selection."""
        self.left_panel_manager.set_source_option(option)

    def set_source_options(self, options: list[str], selected: str | None = None) -> None:
        """Set full list of source options and optionally select one."""
        try:
            grp = getattr(self.left_panel_manager, 'source_options_group', None) if hasattr(self, 'left_panel_manager') else None
            try:
                from fm.core.services.logger import log
                log.debug(f"[UD_VIEW] set_source_options: opts={len(options)}, selected={selected}, has_group={bool(grp)}")
            except Exception:
                pass
            if grp and hasattr(grp, 'set_options'):
                grp.set_options(options)
                if selected:
                    grp.set_selected_option(selected)
        except Exception as e:
            from fm.core.services.logger import log
            log.error(f"[UD_VIEW] Error setting source options: {e}")

    def display_selected_source(self, source_info: dict):
        if not source_info:
            return
        try:
            # Direct-widget routing
            file_view = getattr(self, 'ud_file_view', None)

            if source_info.get("type") == "folder":
                folder_path = source_info.get("path", "") or ""
                files = source_info.get("file_paths", []) or []
                if hasattr(self, 'center_display') and hasattr(self.center_display, 'set_source_path'):
                    self.center_display.set_source_path(folder_path)
                if file_view:
                    file_view.set_files(files)
            else:
                files = source_info.get("file_paths", []) or []
                source_dir = os.path.dirname(files[0]) if files else ''
                if hasattr(self, 'center_display') and hasattr(self.center_display, 'set_source_path'):
                    self.center_display.set_source_path(source_dir)
                if file_view:
                    file_view.set_files(files)
        except Exception as e:
            log.error(f"[UD_VIEW] Error displaying selected source: {e}")

    def display_enriched_file_info(self, file_info_list: List[Dict[str, Any]]) -> None:
        if not file_info_list:
            return
        log.debug(f"Displaying enriched file info: {file_info_list}")
        self.center_display.display_enriched_file_info(file_info_list)

    def show_error(self, message: str, title: str = "Error"):
        """
        Deprecated: dialogs must be requested via local bus dialog-request events.
        This method now emits ERROR_DIALOG_REQUESTED for compatibility callers.
        """
        try:
            from .ui_events import DialogRequestEvent
            if hasattr(self._view, 'local_bus'):
                self._view.local_bus.emit(
                ViewEvents.ERROR_DIALOG_REQUESTED.value,
                DialogRequestEvent(
                    dialog_type="error",
                    title=title or "Error",
                    extra_data={"message": message or "An error occurred."}
                )
                )
        except Exception as e:
            # Last resort: fall back to direct dialog if event emission fails
            QMessageBox.critical(self, title, message)

    def show_success(self, message: str, title: str = "Success"):
        """
        Deprecated: dialogs must be requested via local bus dialog-request events.
        This method now emits SUCCESS_DIALOG_REQUESTED for compatibility callers.
        """
        try:
            from .ui_events import DialogRequestEvent
            if hasattr(self._view, 'local_bus'):
                self._view.local_bus.emit(
                ViewEvents.SUCCESS_DIALOG_REQUESTED.value,
                DialogRequestEvent(
                    dialog_type="success",
                    title=title or "Success",
                    extra_data={"message": message or "Operation completed."}
                )
                )
        except Exception as e:
            # Last resort fallback
            QMessageBox.information(self, title, message)

    def show_folder_dialog(self, title: str, initial_dir: Optional[str] = None) -> str:
        """Show a native folder selection dialog and return the selected path or empty if cancelled."""
        start_dir = initial_dir or str(Path.home())
        path = QFileDialog.getExistingDirectory(self, title or "Select Folder", start_dir)
        return path or ""

    def show_files_dialog(self, title: str, initial_dir: Optional[str] = None, filter_str: str = "Data Files (*.csv *.ofx *.pdf)") -> List[str]:
        """Deprecated shim. Emit FILES_DIALOG_REQUESTED and return empty list."""
        try:
            self.local_bus.emit(ViewEvents.FILES_DIALOG_REQUESTED.value, {
                "title": title or "Select Files",
                "initial_dir": initial_dir or str(Path.home()),
                "filter": filter_str
            })
        finally:
            # Dialog is owned by FileManager; callers should not expect a return
            return []

    def show_guide_source_options(self, monitor_enabled: bool = False, discovery_enabled: bool = False, folder: Optional[str] = None) -> None:
        """Show contextual guide options for source handling.

        Args:
            monitor_enabled: reserved for future
            discovery_enabled: initial toggle state
            folder: resolved folder path to associate with the toggle
        """
        try:
            if hasattr(self, 'guide_pane') and hasattr(self.guide_pane, 'show_source_context_options'):
                self.guide_pane.show_source_context_options(
                    monitor_enabled=monitor_enabled,
                    discovery_enabled=discovery_enabled,
                    folder=folder,
                )
        except Exception as e:
            log.error(f"[UD_VIEW] Error showing guide source options: {e}")

    

    # === FILE OPERATIONS (New file_pane_v2 support) ===
    def add_files(self, files: List[str]) -> None:
        """Add files to the file view."""
        file_view = getattr(self._view, 'ud_file_view', None)
        for file_path in files:
            if file_view:
                file_view.add_file(file_path)
        log.debug(f"[UD_VIEW] Added {len(files)} files to UDFileView")
    
    def remove_file(self, file_path: str) -> None:
        """Remove a file from the file view and publish canonical remove intent."""
        # Update local widget immediately for responsiveness
        getattr(self._view, 'ud_file_view', None).remove_file(file_path)
        log.debug(f"[UD_VIEW] Removed file locally: {file_path}")

        # Publish canonical remove intent so FileListManager updates the canonical list
        from .ui_events import FileRemovedEvent  # local import to avoid cycles
        self._view.local_bus.emit(ViewEvents.FILE_REMOVED.value, FileRemovedEvent(file_path=file_path))
        log.debug("[UD_VIEW] Published FILE_REMOVED intent")
    
    def set_files(self, files: List[str]) -> None:
        """Deprecated path: prefer FILE_LIST_UPDATED -> update_files_display. Kept for compatibility."""
        file_view = getattr(self._view, 'ud_file_view', None)
        if file_view:
            file_view.set_files(files)
            log.debug(f"[UD_VIEW] (compat) Set {len(files)} files in UDFileView")
    
    def get_selected_file(self) -> str:
        """Get the currently selected file path."""
        try:
            if hasattr(self.center_display, 'file_pane'):
                selected = self.center_display.file_pane.get_selected_file()
                return selected if selected else ""
            return ""
        except Exception as e:
            log.error(f"[UD_VIEW] Error getting selected file: {e}")
            return ""
    
    def clear_files(self) -> None:
        """Clear all files from the file view."""
        file_view = getattr(self._view, 'ud_file_view', None)
        if file_view:
            file_view.clear_files()
        log.debug("[UD_VIEW] Cleared all files from UDFileView")

    # === Phase 3: Processing lifecycle & dialog handlers ===
    # Processing lifecycle handled via StateManager.sync_state_to_view().
    # Legacy local handlers removed to prevent duplicate or conflicting updates.

    def _on_dialog_requested(self, event_data):
        """
        Handle dialog requests for success/error uniformly:
        - event_data: DialogRequestEvent dataclass or dict with dialog_type, title, extra_data.message
        """
        try:
            log.debug(f"[UD_VIEW] Dialog request event received: {type(event_data)}")
            dialog_type = None
            title = ""
            message = ""
            extra = {}

            if isinstance(event_data, dict):
                dialog_type = event_data.get('dialog_type')
                title = event_data.get('title') or ""
                extra = event_data.get('extra_data') or {}
            else:
                dialog_type = getattr(event_data, 'dialog_type', None)
                title = getattr(event_data, 'title', "") or ""
                extra = getattr(event_data, 'extra_data', {}) or {}

            if isinstance(extra, dict):
                message = extra.get('message', "") or message

            if dialog_type == "error":
                self.show_error(message or "An error occurred.", title=title or "Error")
            elif dialog_type == "success":
                self.show_success(message or "Operation completed.", title=title or "Success")
            else:
                log.warning(f"[UD_VIEW] Unknown dialog type: {dialog_type}")
        except Exception as e:
            log.error(f"[UD_VIEW] Error handling dialog request: {e}")

