# Update Data – Processing Lifecycle Wiring (Session 3)

Date: 2025-08-09
Owner: Cascade

## Summary of Changes

- **Centralised lifecycle state via `StateManager`**
  - `ProcessingManager.on_processing_started()` now calls `state_manager.set_processing(True)` then `sync_state_to_view()`.
  - `ProcessingManager.on_processing_completed(...)` calls `state_manager.set_processing(False)` then `sync_state_to_view()`.
  - Both set a human-readable `status_message` in `state_manager.state` prior to syncing, keeping InfoBar/guide messaging unified.

- **Removed duplicate local lifecycle emissions**
  - Eliminated `ViewEvents.PROCESSING_STARTED/COMPLETED` emissions from `ProcessingManager.handle_process()`.
  - Canonical emissions remain in pipeline (`dw_director` → `UpdateDataEventService`) to global bus.

- **Presenter wiring updated**
  - `ud_presenter.py` now constructs `ProcessingManager(..., state_manager=self.state_manager)`.
  - Deleted speculative navigation method `request_transition`; Update Data is a single-view module with no event-based navigation.

## UI/Event Wiring – Current Status

- **Startup crash fixed**: `UpdateDataView._connect_center_pane_intents()` implemented and called by `setup_ui()`.
- **Enums used for labels**: Left panel option labels sourced from `SourceOptions`/`SaveOptions`.
- **Event enum cleaned**: Removed mismatched/speculative items in `local_event_bus.ViewEvents`.
- **New issue**: Presenter raised `AttributeError` due to missing `IUpdateDataView` methods on `UpdateDataView` (e.g., `get_save_option()`).

## Decision: Left Panel as Layout Manager Only

- Signals moved out of the left panel; it serves as a layout/aggregation manager.
- The view (`UpdateDataView`) must call widget APIs directly (read state, set state) rather than re-emitting left panel signals.
- Presenter remains Qt-free and talks only to the view interface.

## Files Touched

- `update_data/_ui/_presenter/processing_manager.py`
  - Constructor accepts optional `state_manager`.
  - Start/completion handlers route state via `StateManager` and perform a single UI sync.
  - Removed local bus emissions that duplicated pipeline events.

- `update_data/ud_presenter.py`
  - Pass `self.state_manager` to `ProcessingManager` during initialisation.
  - Remove speculative `request_transition` method; no `REQUEST_VIEW_TRANSITION` usage remains.

## Rationale

- **Single source of truth**: All processing state funnels through `StateManager.set_processing()`, eliminating divergent flows.
- **MVP compliance**: Presenter/manager stay clear of Qt; view updates occur via interface/state sync.
- **No speculative clutter**: Changes are strictly scoped to Priority 1.

## Observed Startup Errors (after `fm` run)

- `ud_view.py` expects handlers that are missing:
  - Missing method: `UpdateDataView.on_auto_queue_toggled` (guide pane wiring).
  - Missing methods: `UpdateDataView._on_processing_started`, `UpdateDataView._on_processing_completed` (local bus subscriptions).

- These surfaced because we stopped emitting `ViewEvents.PROCESSING_*` locally. The view still subscribes to them.

## Immediate Next Steps (recommended)

1. **Remove legacy local-bus lifecycle subscriptions in view**
   - In `update_data/_ui/ud_view.py`, delete subscriptions to `ViewEvents.PROCESSING_STARTED` and `...COMPLETED` and their handlers (`_on_processing_started`, `_on_processing_completed`).
   - Rationale: processing UI now derives solely from `StateManager.sync_state_to_view()`.

2. **Fix guide pane toggle hookup**
   - Either implement `UpdateDataView.on_auto_queue_toggled` or (preferred) move the intent wiring so that the presenter subscribes to a typed local intent event (e.g., `LOCAL_INTENT.AUTO_QUEUE_TOGGLED`) and forwards to `GuidePanePresenter.on_discovery_toggled(...)`.
   - Stay zero-Qt in presenter; the view should expose an interface or emit a local intent event the presenter already subscribes to.

3. **Ensure Guide Pane reflects processing state via StateManager**
   - If not already, make `StateManager.sync_state_to_view()` call the guide presenter’s interface (e.g., `guide_presenter.on_processing_started/completed`) based on `state.processing` and available counts/messages.
   - For now, we set `state.status_message`. Wire this into guide presenter message/status to avoid duplication.

4. **Guardrail tests**
   - Add unit tests around `ProcessingManager.on_processing_started/completed` verifying:
     - `StateManager.set_processing(True/False)` called once.
     - `sync_state_to_view()` invoked.
     - No local `PROCESSING_*` emissions.

5. **Doc update**
   - Update `proposed_events.md/csv` to mark `PROCESSING_STARTED/COMPLETED` (view-local) as removed, with canonical source: pipeline/global events + `StateManager`-driven UI sync.

## Open Items for Next Session

- Implement minimal `IUpdateDataView` methods on `UpdateDataView` as thin pass-throughs to left panel widgets:
  - `get_save_option()`, `get_update_database()`, `set_save_select_enabled(bool)`, `set_process_button_text(str)`, `set_source_option(SourceOptions)`.
- Remove broad try/except around guide/centre wiring to follow fail-fast policy.
- Smoke test UI: left panel selections, process/cancel, centre pane “add files” propagation on event bus.

## Concrete Implementation Plan

- Step 1: `ud_view.py`
  - Remove local bus subscriptions to processing lifecycle and their handlers.
  - Keep InfoBar control callable via interface methods invoked in `StateManager.sync_state_to_view()`.

- Step 2: Guide pane wiring
  - Replace direct method expectation `on_auto_queue_toggled` with either:
    - View interface method that the presenter binds during setup, or
    - Local intent event already present in the view layer that the presenter subscribes to.

- Step 3: `StateManager.sync_state_to_view()`
  - Ensure it sets:
    - Controls enabled/disabled from `state.can_process`.
    - Guide pane status/message from `state.processing` and `state.status_message`.

- Step 4: Verify end-to-end
  - Run `fm`, trigger a small processing job, confirm:
    - Start: processing disabled, message shows "Processing N files…".
    - Completion: processing re-enabled (when applicable), summary message rendered once, no duplicate flashes.

## Risks / Considerations

- Mixed old/new pathways can reintroduce double updates. Remove legacy view subscriptions before testing.
- If other components rely on local `ViewEvents.PROCESSING_*`, audit and migrate them to either global pipeline events or `StateManager`-driven sync.

## Done vs TODO

- Done: StateManager lifecycle routing in manager; presenter passes StateManager; duplicate local emissions removed.
- TODO: Remove legacy view subscriptions; fix guide pane toggle wiring; align `sync_state_to_view()` with guide; add tests.

## Addendum (22:25 NZT)

- Implemented core `IUpdateDataView` methods in `ud_view.py` as thin pass-throughs to underlying widgets:
  - `set_source_option`, `get_save_option`, `get_update_database`, `set_save_select_enabled`, `set_process_button_text`, `set_process_enabled`, `set_all_controls_enabled`.
- Centralised signal routing in the view:
  - `UpdateDataView._connect_signals()` now connects directly to `LeftPanelManager`'s widgets (`source_options_group`, `archive_options_group`, `database_checkbox`, `action_button`, `exit_button`).
  - Left panel remains a layout manager only; no re-emission of domain signals.
- Fail-fast policy applied:
  - Removed broad try/except around the above interface pathways.
- Next:
  - Remove remaining broad try/except in adapters/dialog handling.
  - Audit `IUpdateDataView` coverage vs `interface/i_view_interface.py` and smoke test to confirm no presenter `AttributeError`s.

## UI Polish — Guide Pane (23:55–02:30 NZT)

* __Border + padding__
  - Guide pane `QFrame#guide_frame`: 1px solid `#4CAF50`, radius 6, transparent background.
  - `_update_frame_style()` now constant; state affects label colour/weight only.
  - Outer margins owned by `GuidePaneWidget` (2px); splitter wrapper margins/spacing set to 0 in `center_panel_layout.py`.

* __Message + options__
  - Swapped `QTextEdit` → compact `QLabel` with word wrap; hidden when empty.
  - Flattened `QGroupBox` for Options (no border, tight spacing); single row: “Enable file discovery” + inline info.
  - Checkbox indicator subtly green; options container only visible when it has children.

* __Archive summary__
  - Added `archive_summary_label` (subtle green), hidden by default; `set_archive_summary()` manages text.

* __Syntax fix__
  - Removed stray triple-quote in `guide_pane.py` causing lints; unified stylesheet.

## Run Notes (02:30 NZT)

* __fm run__
  - File list enrichment OK; guide pane options rendered compactly; border looks clean and consistent.
  - Observed repeated `QPainter::... Painter not active` warnings — likely from a custom paint path outside guide pane; investigate separately.
  - Auto-queue toggle persisted and reconciled (monitor on/off logs observed) for folder `C:\Users\<USER>\Downloads\_flatmete_file_moniter_test`.
