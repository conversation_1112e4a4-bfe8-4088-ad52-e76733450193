"""
Styles applier module.

Responsibilities:
- Apply stylesheet to QApplication
- Run audits on what Qt applied vs loader output
- Provide a simple public API: apply_styles(app)
"""

from __future__ import annotations

from pathlib import Path
from PySide6.QtWidgets import QApplication  # type: ignore

from .loader import load_styles  # reuse loader API
from fm.core.services.logger import log
from fm.core.config import config  # type: ignore
from fm.core.config.keys import ConfigKeys  # type: ignore


# Mirror audit flag; can be centralized later if desired
AUDIT_STYLESHEET = True


def _audit_qt_applied(app: QApplication, styles_dir: Path) -> None:
    if not AUDIT_STYLESHEET:
        return

    actual_applied = app.styleSheet()
    debug_file = styles_dir / "debug_qt_applied.qss"
    with open(debug_file, "w", encoding="utf-8") as f:
        f.write(actual_applied)

    print("\n=== QT APPLICATION AUDIT ===")
    print(f"Qt applied stylesheet saved to: {debug_file}")
    print(f"Applied length: {len(actual_applied)} characters")
    print(f"Applied lines: {len(actual_applied.splitlines())}")


def _audit_compare_stylesheets(loader_output: str, qt_applied: str, styles_dir: Path) -> None:
    if not AUDIT_STYLESHEET:
        return

    print("\n=== COMPARISON AUDIT ===")
    if loader_output == qt_applied:
        print("✓ Qt applied stylesheet UNCHANGED from loader output")
        print("⚠ CSS variables still present - Qt must be handling them internally")
    else:
        print("⚠ Qt MODIFIED the stylesheet during application")
        diff_file = styles_dir / "debug_differences.txt"
        with open(diff_file, "w", encoding="utf-8") as f:
            f.write("=== LOADER OUTPUT ===\n")
            f.write(f"Length: {len(loader_output)} chars\n")
            f.write(f"Lines: {len(loader_output.splitlines())}\n\n")

            f.write("=== QT APPLIED ===\n")
            f.write(f"Length: {len(qt_applied)} chars\n")
            f.write(f"Lines: {len(qt_applied.splitlines())}\n\n")

            f.write("=== FIRST 1000 CHARS OF EACH ===\n")
            f.write("LOADER:\n")
            f.write(loader_output[:1000])
            f.write("\n\nQT APPLIED:\n")
            f.write(qt_applied[:1000])

        print(f"Differences saved to: {diff_file}")


def _audit_actual_widget_colors(app: QApplication, styles_dir: Path) -> None:
    if not AUDIT_STYLESHEET:
        return

    print("\n=== ACTUAL WIDGET COLORS AUDIT ===")
    widgets_to_check = [
        "left_panel",
        "right_panel",
        "right_side_bar",
        "file_tree",
    ]

    color_report: list[str] = []
    color_report.append("=== ACTUAL WIDGET COLORS ===\n")

    for widget_name in widgets_to_check:
        widgets = app.findChildren(object, widget_name)
        if widgets:
            widget = widgets[0]
            palette = widget.palette()
            bg_color = palette.color(palette.Window)
            text_color = palette.color(palette.WindowText)
            print(f"{widget_name}:")
            print(f"  Background: {bg_color.name()}")
            print(f"  Text: {text_color.name()}")

            color_report.append(f"{widget_name}:\n")
            color_report.append(f"  Background: {bg_color.name()}\n")
            color_report.append(f"  Text: {text_color.name()}\n")
            color_report.append(f"  Widget type: {type(widget).__name__}\n\n")
        else:
            print(f"{widget_name}: NOT FOUND")
            color_report.append(f"{widget_name}: NOT FOUND\n")

    color_file = styles_dir / "debug_actual_colors.txt"
    with open(color_file, "w", encoding="utf-8") as f:
        f.writelines(color_report)

    print(f"Actual colors saved to: {color_file}")


def apply_styles(app: QApplication) -> None:
    """
    Apply styles to the application.
    Emits concise provenance logs for BASE_FONT_SIZE and selected stylesheet.
    """
    styles_dir = Path(__file__).parent

    # Provenance: determine font size source (config vs default)
    font_size = 14
    font_source = "default"
    try:
        configured_font_size = config.get_value(ConfigKeys.App.BASE_FONT_SIZE, None)
        if isinstance(configured_font_size, int) and configured_font_size > 0:
            font_size = configured_font_size
            font_source = "config"
    except Exception as e:
        font_source = f"default (exception during config lookup: {e})"

    # Concise provenance log (font size)
    try:
        log.info(f"Styles: BASE_FONT_SIZE={font_size} [source={font_source}]")
    except Exception:
        print(f"[styles] BASE_FONT_SIZE={font_size} [source={font_source}]")

    # Apply stylesheet
    stylesheet = load_styles()
    app.setStyleSheet(stylesheet)

    # Concise provenance log (stylesheet file path actually chosen)
    try:
        primary_file = styles_dir / "base_theme.qss"
        legacy_file = styles_dir / "flatmate_consolidated.qss"
        consolidated_file = primary_file if primary_file.exists() else legacy_file
        log.info(f"Styles: applied stylesheet from: {consolidated_file}")
    except Exception:
        print(f"[styles] applied stylesheet from: {consolidated_file if 'consolidated_file' in locals() else 'unknown'}")

    # AUDIT: Capture what Qt applied
    _audit_qt_applied(app, styles_dir)

    # AUDIT: Compare loader vs Qt applied
    qt_applied = app.styleSheet()
    _audit_compare_stylesheets(stylesheet, qt_applied, styles_dir)

    # AUDIT: Delay widget color inspection to allow UI to build
    if AUDIT_STYLESHEET:
        from PySide6.QtCore import QTimer  # type: ignore
        QTimer.singleShot(1000, lambda: _audit_actual_widget_colors(app, styles_dir))