{
  "files.exclude": {
    "**/__pycache__": true,
    "**/.pytest_cache": true,
  
   
  },
  "files.autoSave": "afterDelay",
  "python.defaultInterpreterPath": "${workspaceFolder}/flatmate/.venv_fm313/Scripts/python.exe",
  // Terminal settings
  "terminal.integrated.cwd": "${workspaceFolder}/flatmate",
  // "terminal.integrated.profiles.windows": {
  //   "Git Bash": {
  //     "path": "C:\\Program Files\\Git\\bin\\bash.exe",
  //     "args": ["--init-file", "${workspaceFolder}/terminal-startup.sh"]
  //   }
  // },
  // "terminal.integrated.defaultProfile.windows": "Git Bash",
  // File watcher exclusions
  "files.watcherExclude": {
    "**/.git/**": true,
    "**/.hg/**": true,
    "**/.svn/**": true,
    "**/.DS_Store": true,
    "**/__pycache__/**": true,
    "**/.mypy_cache/**": true,
    "**/.pytest_cache/**": true,
    "**/.vscode/**": true,
    "**/.idea/**": true,
    "**/env/**": true,
    "**/.env/**": true,
    "**/*venv*/**": true,
    "**/.*venv*/**": true,
    "**/build/**": true,
    "**/dist/**": true,
    "**/node_modules/**": true,
    "**/*z_archive*/**": true,
   "**/*.md": true
  },
  "accessibility.verbosity.terminal": false,
  "autoflake-extension.remove-all-unused-imports": false
}