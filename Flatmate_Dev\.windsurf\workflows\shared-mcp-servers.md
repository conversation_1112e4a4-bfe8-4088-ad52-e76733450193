---
description: Configure shared MCP servers with PM2 to prevent duplicate processes across IDEs
---

# Shared MCP Servers with PM2

This guide sets up persistent MCP servers that can be shared across multiple IDE instances, eliminating resource duplication.

## Transport Protocol Limitations

- **Sequential Thinking**: Only supports stdio transport - cannot be shared via HTTP/port
- **Excel**: Supports stdio, SSE (deprecated), and streamable HTTP - can be shared via port
- **Tavily**: Supports stdio and HTTP transport - can be shared via port

## Prerequisites

- Node.js and npm installed
- PM2 installed globally: `npm install -g pm2`

## Step 1: Install MCP Servers

Install the servers you want to share globally:

```bash
npm install -g @modelcontextprotocol/server-sequential-thinking
npm install -g tavily-mcp@0.1.3
npm install -g excel-mcp-server
```

## Step 2: Start Servers with PM2

### Excel Server (HTTP/Port-based - Shareable)
```bash
# Set environment variables
export EXCEL_FILES_PATH="/path/to/excel/files"
export FASTMCP_PORT=8007

# Start Excel server on HTTP
pm2 start npx --name "excel-mcp" -- excel-mcp-server streamable-http
```

### Tavily Server (HTTP/Port-based - Shareable)
```bash
pm2 start npx --name "tavily-mcp" -- tavily-mcp@0.1.3
```

### Sequential Thinking (stdio only - Not shareable)
```bash
# This must remain stdio-based - cannot be shared across IDEs
# Keep original configuration in mcp_config.json
```

## Step 3: Configure Environment Variables

```bash
# Excel server
pm2 set excel-mcp EXCEL_FILES_PATH="/path/to/excel/files"
pm2 set excel-mcp FASTMCP_PORT=8007

# Tavily server (if API key needed)
pm2 set tavily-mcp TAVILY_API_KEY=your-key-here
```

## Step 4: Configure Windsurf

Update your `mcp_config.json`:

```json
{
  "mcpServers": {
    "sequential-thinking": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"],
      "type": "stdio"
    },
    "tavily-mcp": {
      "name": "tavily-mcp",
      "endpoint": "http://localhost:3000",
      "type": "node"
    },
    "excel": {
      "name": "excel",
      "endpoint": "http://localhost:8007",
      "type": "node"
    }
  }
}
```

## Step 5: Auto-start on Boot

```bash
pm2 startup
pm2 save
```

## Step 6: Management Commands

```bash
pm2 list                    # View running servers
pm2 logs excel-mcp         # View Excel server logs
pm2 logs tavily-mcp        # View Tavily server logs
pm2 restart excel-mcp      # Restart Excel server
pm2 stop excel-mcp         # Stop Excel server
pm2 delete excel-mcp       # Remove Excel server
```

## Verification

1. Check servers are running: `pm2 list`
2. Verify endpoints: `curl http://localhost:8007` (Excel) and `curl http://localhost:3000` (Tavily)
3. Sequential Thinking will spawn new stdio processes per IDE (limitation of the server)

## Important Notes

- **Sequential Thinking**: Cannot be shared due to stdio-only transport limitation
- **Excel & Tavily**: Can be shared via HTTP endpoints, reducing resource usage
- **Resource Usage**: Only Excel and Tavily servers will be shared; Sequential Thinking will still spawn new processes per IDE
