"""
Utility functions for the file view component.

Contains helper functions for file operations, validation, and formatting.
"""

import os
from datetime import datetime
from pathlib import Path
from typing import List, Optional
from .models import FileInfo
from .config import FileConfig
from .....services.file_info_service import FileInfoService   


def create_file_info(file_path: str) -> Optional[FileInfo]:
    """Create a FileInfo object from a file path using FileInfoService for enrichment."""
    try:
        path_obj = Path(file_path)

        if not path_obj.exists():
            return None

        # Get enriched file info from FileInfoService (includes statement handler detection)
        try:
            enriched_info = FileInfoService.get_file_info(file_path)
            bank_type = enriched_info.get('bank_type', 'Unknown')
            format_type = enriched_info.get('format_type', 'Unknown')

            # Create meaningful file type from statement handler info
            if bank_type != 'Unknown' and format_type != 'Unknown':
                file_type = f"{bank_type} {format_type}"
            else:
                file_type = path_obj.suffix.lower()

        except Exception:
            # Fallback to basic info if FileInfoService fails
            file_type = path_obj.suffix.lower()

        stat = path_obj.stat()

        return FileInfo(
            path=str(path_obj.resolve()),
            size=stat.st_size,
            modified=datetime.fromtimestamp(stat.st_mtime),
            file_type=file_type,  # Now uses statement handler info
            created=datetime.fromtimestamp(getattr(stat, "st_ctime", stat.st_mtime)),
            is_valid=True,
            is_processed=False
        )

    except (OSError, PermissionError):
        return None


def validate_file_paths(file_paths: List[str], config: FileConfig) -> List[str]:
    """Validate a list of file paths and return only valid ones."""
    valid_paths = []
    
    for file_path in file_paths:
        if validate_file_path(file_path, config):
            valid_paths.append(file_path)
    
    return valid_paths


def validate_file_path(file_path: str, config: FileConfig) -> bool:
    """Validate a single file path."""
    try:
        path_obj = Path(file_path)
        
        # Check if file exists
        if not path_obj.exists():
            return False
        
        # Check if it's a file (not directory)
        if not path_obj.is_file():
            return False
        
        # Check file type is allowed
        if not config.is_file_type_allowed(file_path):
            return False
        
        return True
    
    except (OSError, PermissionError):
        return False


def get_file_icon_name(file_path: str) -> str:
    """Get appropriate icon name for a file type."""
    extension = Path(file_path).suffix.lower()
    
    icon_mapping = {
        '.csv': 'file-csv',
        '.xlsx': 'file-excel',
        '.xls': 'file-excel',
        '.txt': 'file-text',
        '.pdf': 'file-pdf',
        '.json': 'file-code',
        '.xml': 'file-code',
    }
    
    return icon_mapping.get(extension, 'file')


def format_file_size(size_bytes: int) -> str:
    """Format file size in human-readable format."""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes / (1024 * 1024):.1f} MB"
    else:
        return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"


def get_relative_path(file_path: str, base_path: Optional[str] = None) -> str:
    """Get relative path for display purposes."""
    if base_path is None:
        return Path(file_path).name
    
    try:
        return str(Path(file_path).relative_to(Path(base_path)))
    except ValueError:
        return Path(file_path).name


def group_files_by_directory(file_infos: List[FileInfo]) -> dict:
    """Group files by their parent directory."""
    groups = {}
    
    for file_info in file_infos:
        parent_dir = str(Path(file_info.path).parent)
        
        if parent_dir not in groups:
            groups[parent_dir] = []
        
        groups[parent_dir].append(file_info)
    
    return groups
