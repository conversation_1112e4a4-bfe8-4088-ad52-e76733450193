# Update Data File List System – Implementation & Verification Checklist

## 1. Recent Folders (Source/Archive) Functionality

### Discovery & Persistence
- [ ] Recent source folders are tracked and persisted when a user selects a source folder.
- [ ] Recent archive folders are tracked and persisted when a user selects an archive folder.
- [ ] Recent folders are loaded on app start and saved on update (to config or suitable store).

### UI Integration
- [ ] Recent source folders are displayed in the left panel’s source option group.
- [ ] Recent archive folders are displayed in the left panel’s archive option group.
- [ ] The most recently used folder is shown as the selected option.
- [ ] Selecting a recent folder updates the file list as expected.

### Removal & Update
- [ ] If a recent folder is deleted or renamed on disk, it is removed from the recent list.
- [ ] UI updates promptly when a folder is removed from disk.

### Limits & Order
- [ ] A sensible maximum number of recent folders is enforced (e.g., 5-10).
- [ ] Folders are ordered by most recent use.

### Testing & Verification
- [ ] Manual test: Add, select, and remove folders; verify recent lists update correctly.
- [ ] Edge case: Remove folder on disk while app is running; verify UI updates.
- [ ] Persistence test: Restart app; recent folders list is restored.

---

## 2. File Info Display Refinement

### Column Order & Content
- [ ] File info columns (name, type, bank, created, etc.) are displayed in the correct order.
- [ ] All relevant file info fields (from FileInfoData) are visible and formatted as intended.
- [ ] Unknown files show “.csv” or fallback info as specified.

### Formatting & Usability
- [ ] Dates/times are shown in a clear, user-friendly format.
- [ ] Column headers use UK spelling and clear terminology (e.g., “File Info”, “Created”).

### Testing & Verification
- [ ] Manual test: Add files of various types and sources; verify all info displays correctly.
- [ ] Edge case: Add unknown file types; verify fallback info displays.
