"""
Styles loader module.

Responsibilities:
- Load consolidated stylesheet (prefer base_theme.qss, fallback to flatmate_consolidated.qss)
- Apply app-level templating (e.g., {{FONT_SIZE}})
- Optional YAML-driven color-only theming via hex_map
- Provide auditing helpers related to loader output
"""

from __future__ import annotations

from pathlib import Path
from typing import Optional, Tuple, Dict

from ...core.config import config  # type: ignore
from ...core.config.keys import ConfigKeys  # type: ignore
from ...core.services.logger import log  # type: ignore

# Audit control flag - set to False to disable debugging
AUDIT_STYLESHEET = True

# Consolidated stylesheet switch - True -> use single consolidated file
USE_CONSOLIDATED_STYLESHEET = True  # base_theme.qss preferred; falls back to flatmate_consolidated.qss

# Theme experiment flag: when set, attempt to load a YAML theme and apply color-only hex mapping
# Default disabled per user direction; document how to enable/select themes in styles/README.md
ENABLE_THEME_EXPERIMENT = False
DEFAULT_THEME_NAME = "light"     # scaffold provided
THEMES_DIR_NAME = "themes"


def _audit_loader_output(combined_stylesheet: str, styles_dir: Path) -> None:
    if not AUDIT_STYLESHEET:
        return

    debug_file = styles_dir / "debug_combined_output.qss"
    with open(debug_file, "w", encoding="utf-8") as f:
        f.write(combined_stylesheet)

    print("\n=== LOADER AUDIT ===")
    print(f"Combined stylesheet saved to: {debug_file}")
    print(f"Total length: {len(combined_stylesheet)} characters")
    print(f"Total lines: {len(combined_stylesheet.splitlines())}")

    var_count = combined_stylesheet.count("var(--")
    print(f"CSS variables found: {var_count}")

    placeholder_count = combined_stylesheet.count("{{FONT_SIZE}}")
    print(f"Font size placeholders found: {placeholder_count}")

    selectors_to_check = [
        "#left_panel",
        "#right_panel",
        'QPushButton[type="action_btn"]',
        "QWidget",
    ]

    print("\nSelector occurrences:")
    for selector in selectors_to_check:
        count = combined_stylesheet.count(selector)
        print(f"  {selector}: {count} times")


def _audit_css_variables(stylesheet: str) -> None:
    if not AUDIT_STYLESHEET:
        return

    print("\n=== CSS VARIABLE AUDIT ===")
    import re

    var_pattern = r"var\(--[\w-]+\)"
    var_matches = re.findall(var_pattern, stylesheet)

    if var_matches:
        print(f"Found {len(var_matches)} CSS variable calls:")
        for var_call in set(var_matches):
            count = stylesheet.count(var_call)
            print(f"  {var_call}: {count} times")
    else:
        print("No CSS variable calls found")

    def_pattern = r"--[\w-]+:\s*[^;]+;"
    def_matches = re.findall(def_pattern, stylesheet)

    if def_matches:
        print(f"\nFound {len(def_matches)} CSS variable definitions:")
        for var_def in def_matches:
            print(f"  {var_def}")
    else:
        print("No CSS variable definitions found")


def _load_theme_mapping(styles_dir: Path, theme_name: str) -> Optional[Dict[str, str]]:
    """
    Load a YAML theme file and return its hex_map dict (exact literal replacements).
    Returns None if file missing or invalid.
    """
    try:
        import yaml  # type: ignore
    except Exception:
        try:
            log.info("Styles: PyYAML not available; skipping theme mapping")
        except Exception:
            print("[styles] PyYAML not available; skipping theme mapping")
        return None

    theme_path = styles_dir / THEMES_DIR_NAME / f"theme-{theme_name}.yaml"
    if not theme_path.exists():
        try:
            log.info(f"Styles: theme file not found: {theme_path}")
        except Exception:
            print(f"[styles] theme file not found: {theme_path}")
        return None

    try:
        with open(theme_path, "r", encoding="utf-8") as f:
            data = yaml.safe_load(f)
        hex_map = (data or {}).get("hex_map") or {}
        if not isinstance(hex_map, dict) or not hex_map:
            try:
                log.info(f"Styles: theme '{theme_name}' has no hex_map or is not a dict; skipping")
            except Exception:
                print(f"[styles] theme '{theme_name}' has no hex_map or is not a dict; skipping")
            return None
        # Normalize keys (strip leading '#') and values (ensure they start with '#')
        normalized: Dict[str, str] = {}
        for k, v in hex_map.items():
            key = str(k).lstrip("#")
            val = str(v)
            if not val.startswith("#"):
                val = f"#{val}"
            normalized[key] = val
        return normalized
    except Exception as e:
        try:
            log.info(f"Styles: failed loading theme '{theme_name}': {e}")
        except Exception:
            print(f"[styles] failed loading theme '{theme_name}': {e}")
        return None


def _apply_hex_map(stylesheet: str, hex_map: Dict[str, str]) -> Tuple[str, Dict[str, int]]:
    """
    Apply exact, case-sensitive hex replacements based on hex_map where keys are hex without '#'.
    Returns (new_stylesheet, counts_dict).
    """
    counts: Dict[str, int] = {}
    out = stylesheet
    for key, new_val in hex_map.items():
        literal = f"#{key}"
        count_before = out.count(literal)
        if count_before:
            out = out.replace(literal, new_val)
        counts[literal] = count_before
    return out, counts


def load_consolidated_styles() -> str:
    """
    Load consolidated stylesheet with hardcoded colors.
    Applies {{FONT_SIZE}} replacement and optional theme mapping.
    """
    styles_dir = Path(__file__).parent

    # Prefer base_theme.qss, fall back to flatmate_consolidated.qss for backward compatibility
    primary_file = styles_dir / "base_theme.qss"
    legacy_file = styles_dir / "flatmate_consolidated.qss"
    consolidated_file = primary_file if primary_file.exists() else legacy_file
    if not consolidated_file.exists():
        raise FileNotFoundError(f"Consolidated stylesheet not found (checked): {primary_file} and {legacy_file}")

    with open(consolidated_file, "r", encoding="utf-8") as f:
        stylesheet = f.read()

    # Apply dynamic font size
    font_size = config.get_value(ConfigKeys.App.BASE_FONT_SIZE, 14)
    before = stylesheet.count("{{FONT_SIZE}}")
    stylesheet = stylesheet.replace("{{FONT_SIZE}}", str(font_size))
    after = stylesheet.count("{{FONT_SIZE}}")

    # Optional theme mapping
    if ENABLE_THEME_EXPERIMENT:
        hex_map = _load_theme_mapping(styles_dir, DEFAULT_THEME_NAME)
        if hex_map:
            themed, counts = _apply_hex_map(stylesheet, hex_map)
            try:
                log.info(f"Styles: theme '{DEFAULT_THEME_NAME}' applied with {sum(counts.values())} total replacements; zero-hit keys: {[k for k,v in counts.items() if v == 0]}")
            except Exception:
                print(f"[styles] theme '{DEFAULT_THEME_NAME}' applied; counts={counts}")
            stylesheet = themed
        else:
            try:
                log.info("Styles: theme mapping not applied (no hex_map)")
            except Exception:
                print("[styles] theme mapping not applied (no hex_map)")

    # Informational logging
    try:
        log.info(f"Styles: using consolidated stylesheet: {consolidated_file.name}  (path={consolidated_file})")
        log.info(f"Styles: BASE_FONT_SIZE={font_size}  FONT_SIZE replacements={before - after}")
    except Exception:
        print(f"[styles] using consolidated stylesheet: {consolidated_file.name} (path={consolidated_file})")
        print(f"[styles] BASE_FONT_SIZE={font_size}  FONT_SIZE replacements={before - after}")

    if AUDIT_STYLESHEET:
        print("\n=== CONSOLIDATED STYLESHEET LOADED ===")
        print(f"File: {consolidated_file}")
        print(f"Length: {len(stylesheet)} characters")
        print(f"Lines: {len(stylesheet.splitlines())}")
        print(f"FONT_SIZE replacements: {before - after}")

    return stylesheet


def load_styles() -> str:
    """
    Load and combine application styles.
    Uses consolidated stylesheet if USE_CONSOLIDATED_STYLESHEET is True,
    otherwise loads theme.qss and style.qss separately.
    """
    if USE_CONSOLIDATED_STYLESHEET:
        return load_consolidated_styles()

    styles_dir = Path(__file__).parent

    with open(styles_dir / "theme.qss", "r", encoding="utf-8") as f:
        theme = f.read()
    with open(styles_dir / "style.qss", "r", encoding="utf-8") as f:
        style = f.read()

    font_size = config.get_value(ConfigKeys.App.BASE_FONT_SIZE, 14)
    combined = theme + "\n" + style
    combined = combined.replace("{{FONT_SIZE}}", str(font_size))

    _audit_loader_output(combined, styles_dir)
    _audit_css_variables(combined)

    return combined