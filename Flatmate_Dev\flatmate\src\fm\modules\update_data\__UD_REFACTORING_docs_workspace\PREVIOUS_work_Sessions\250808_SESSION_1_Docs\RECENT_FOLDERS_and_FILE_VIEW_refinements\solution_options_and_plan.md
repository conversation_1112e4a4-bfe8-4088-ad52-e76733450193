# Recent Folders, File Info, and Guide Pane – Options and Plan (Concise)

Status: Draft for sign‑off
Date: 2025-08-08
Audience: Practical, novice‑friendly. UK spelling. No speculative clutter.

---

## 1) Goals (what “good” looks like)
- File Tree shows: Selected Files | File Info | Size | Created (correct dates)
- Recent folders appear in Source/Archive option menus, pruned if missing
- Guide pane offers per‑folder “Monitor this folder?” when new folders are encountered
- Presenter stays Qt‑free (MVP). View exposes clear interface methods.

---

## 2) Current gaps (short)
- Created not shown: service didn’t supply timestamps (fixed in `file_info_service.py`).
- No single source‑of‑truth for file list + info cache (risk of desync).
- No Quick Access persistence for recent/archived folders.
- Guide pane prompt exists in parts but not standardised or reliably triggered.

---

## 3) Solution options

- A) Event‑first + tiny store + hydrator (recommended)
  - Single `FileListUpdated(paths, op={set|merge|remove})` event.
  - Small in‑memory `FileListStore` maintains `paths` and `info_cache`; hydrates via `FileInfoService.hydrate_many()`.
  - View subscribes; presenter orchestrates (no Qt coupling).
  - Pros: simple, predictable, easy to test. Cons: small plumbing.

- B) Keep ad hoc updates
  - Minimal change now; higher risk of drift and regressions later.
  - Pros: fastest. Cons: ongoing instability.

- C) Heavy MVVM/Redux pattern
  - Uniform, but overkill for current scope.
  - Pros: consistency. Cons: complexity, time.

Recommendation: Option A.

---

## 4) Minimal contracts (stable and testable)
- Event: `FileListUpdated(paths: list[str], op: Literal['set','merge','remove'])`
- FileListStore:
  - `set_list()`, `merge_in()`, `remove()`, `clear()`, `get_list()`, `get_info(path)`, `subscribe(cb)`
  - On change: delta‑hydrate via `FileInfoService`, update `info_cache`, notify subscribers.
- FileInfoService:
  - `get_file_info(path)`, `hydrate_many(paths) -> dict[path->info]`
  - Guarantees `bank_type`, `format_type`, `file_type?`, `size_bytes/size_str`, `created`, `modified`.
- QuickAccessStore (JSON): `load/save/prune`, `add_source(dir)`, `add_archive(dir)`.
- View interface:
  - File tree: `set_files(paths)`, `update_file_info(info_map)`
  - Options: `set_source_options(opts, selected)`, `set_archive_options(opts, selected)`
  - Guide: `show_monitor_prompt(dir)`, `hide_monitor_prompt()` (or keep current single method – pick one and standardise)

---

## 5) Plan of attack (small phased tasks)

Phase 0 – Verify Created (done or quick)
- Service now returns `created/modified`. Ensure presenter hydrates after file selection: call `discover_files()` then `view.display_enriched_file_info(...)`.

Phase 1 – Tiny FileListStore + event
- Implement `FileListStore` (module‑local, no frameworks).
- Producers (left panel, monitor) emit `FileListUpdated` with `set/merge/remove`.
- Presenter wires store -> view: on store notify, call `view.set_files()` then `view.update_file_info()`.

Phase 2 – Quick Access options
- Implement `QuickAccessStore` JSON file.
- On source/archive selection: add to store, prune on startup, update option menus with selected dir name.

Phase 3 – Guide pane monitor prompt
- Standardise view API: `show_monitor_prompt(dir)`.
- Trigger when user selects files from a previously unseen folder or selects a new folder source.
- Accept -> start per‑folder monitoring (debounced), new files -> `merge_in` via `FileListUpdated`.

Phase 4 – Smoke test
- Manual/automated: select folder -> list + info hydrate, Created shows; prompt appears; accept -> monitor merges new files; options list updates and persists.

---

## 6) Definition of Done
- Created column shows correct date/time for all files.
- Recent folders shown in Source/Archive menus; missing paths pruned after restart.
- Guide pane reliably prompts to monitor new folders; toggle is per folder; new files appear after debounce.
- Presenter remains Qt‑free; view interface methods used; logs via `fm.core.services.logger.log`.

---

## 7) Risks and mitigations
- Desync during transition -> Mitigate with store as single source and one smoke test.
- Over‑coupling to Qt -> Keep presenter using only view interface.
- Performance during hydration -> Use delta hydration + simple LRU/TTL cache.

---

## 8) Small backlog (nice‑to‑have)
- Context menu: “Refresh file info” for selected file.
- Batch operations indicator while hydrating large lists.
