# PRD — Update Data: Table View and UI Enhancements (Phase 3.1)

Status: Draft v0  
Owner: Update Data Initiative  
Scope: Aesthetic quality improvements, interaction affordances, and directory/API surface clarifications for Update Data UI, building on Phase 3 v0.

References
- Brownfield PRD (Update Data): [DOCS/brownfield-prd.md](../../../DOCS/brownfield-prd.md)
- Phase 3 v0 Plan: [DOCS/Table-View-Improvement-Plan_v0.md](../../../DOCS/Table-View-Improvement-Plan_v0.md)
- UI Deep Dive: [flatmate/DOCS/_GUIDES/Update-Data-UI-Deep-Dive.md](../../../DOCS/_GUIDES/Update-Data-UI-Deep-Dive.md)
- Testing Protocol: [flatmate/DOCS/_PROTOCOLS/GUIDES/testing-protocol.md](../../../DOCS/_PROTOCOLS/GUIDES/testing-protocol.md)
- Event Contracts: [flatmate/src/fm/modules/update_data/_ui/ui_event_contracts.md](../_ui/ui_event_contracts.md)

Background
Phase 3 v0 focused on correctness: deterministic refresh on FileListUpdatedEvent and selection clearing on data rebind. This PRD captures the broader UX and structural concerns and organizes them into actionable, test-gated increments.

Goals
- Aesthetics: Match or improve legacy Table View visual quality with sensible defaults and consistency.
- UX Affordances: Improve column resizing, show/hide columns, and layout clarity to enhance usability and legibility.
- Structural Clarity: Reduce developer confusion in directory structure and API surface exposure with a lightweight RFC and implementation.

Non-Goals
- No wholesale redesign of the entire Update Data module UI.
- No heavy data model refactors beyond what is needed for the enhancements.
- No new runtime dependencies unless explicitly justified.

User Value
- Clearer, more professional visual presentation of files.
- Predictable and discoverable table interactions (resizing, show/hide).
- Lower cognitive load for maintainers via consistent API exposure and folder naming patterns.

Deliverables

1) Table View Visual Quality and Interactions
- Column Resizing & Persistence:
  - Sensible default widths; stretch strategy that avoids horizontal jitter.
  - Optional persistence (per user session or config) if low risk.
- Show/Hide Columns:
  - Lightweight affordance to toggle commonly hidden columns.
- Layout & Spacing:
  - Row height, padding, header styling for readability.
  - Alternating row colors (already applied in v0), verify consistency.
- Visual States:
  - Clear selection/highlight states.
  - Empty/error states are visually obvious and non-jarring.

Acceptance Criteria
- After FileListUpdatedEvent, visible row count equals dataset size; no flicker on consecutive updates.
- Column widths are reasonable by default; last section stretch does not hide key columns.
- Users can hide/show defined columns via a clear control or context menu entry.
- Selection and highlight remain consistent across add/remove and rebinds.
- Manual visual check documented; any automated checks added where feasible.

2) Directory Structure and API Exposure RFC
- Problem Statement:
  - Current folder naming and API exposure diverge from app norms; entry-point/API colocated with components.
- Options (RFC):
  - Option A: Introduce a module-level api/ folder re-exporting the public surface.
  - Option B: Keep colocation but add a root __init__.py that defines a clear __all__ for sanctioned imports.
- Decision Record:
  - Choose A or B with rationale and migration steps (imports, docs).
- Implementation (Zero functional change):
  - Apply import cleanup and update docs to reference the sanctioned API surface.

Acceptance Criteria
- RFC document approved and committed (UD_REFACTORING docs).
- Imports updated to reference sanctioned API; no runtime behavior changes.
- Update-Data-Module-Guide references the new API exposure pattern.

3) Testing and Validation
- Run Tests (repo root or project root):
  - Git Bash:
    - ./.venv_fm313/Scripts/python.exe -m pytest -q tests/gui/update_data -k "smoke or harden or subscription" --maxfail=1 --disable-warnings
  - Windows CMD/PowerShell:
    - .venv_fm313\Scripts\python.exe -m pytest -q tests\gui\update_data -k "smoke or harden or subscription" --maxfail=1 --disable-warnings
  - Optional headless: add --offscreen (see conftest)
- Evidence:
  - Inline-by-default; add screenshots or brief notes to SESSION_LOG for visual checks.

Risks and Mitigations
- Risk: Over-scoping aesthetics causing regressions.
  - Mitigation: Keep changes limited to low-risk defaults; validate with smoke tests and manual notes.
- Risk: Table View lifecycle makes automation for visuals hard.
  - Mitigation: Combine targeted smoke/contract tests with manual evidence; consider pytest-qt extension later.

Plan and Timeline
- Week 1:
  - Draft and approve RFC for API exposure.
  - Implement low-risk table defaults (done in Phase 3 v0) and iterate on show/hide affordance.
- Week 2:
  - Apply structural import/API exposure changes and update guides.
  - Validate via focused test runs and document manual checks.

Change Log
- v0: Draft created from final PRD comments and Phase 3.1 intent.