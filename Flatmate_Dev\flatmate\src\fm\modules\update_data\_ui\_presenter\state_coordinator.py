"""
State management for Update Data module.

This module contains the UpdateDataState dataclass and consolidated state management logic.
Consolidates StateManager and WidgetStateManager as part of the consolidation refactoring.
"""

from dataclasses import dataclass, field
from typing import List, Optional, TYPE_CHECKING

from fm.core.services.logger import log
from ...config.ud_config import ud_config

if TYPE_CHECKING:
    from ..interface import IUpdateDataView
    from .guide_pane_presenter import GuidePanePresenter


@dataclass
class UpdateDataState:
    """
    Presenter state for Update Data module.

    Following MVP pattern: Presenter owns all state, View is stateless.
    This replaces the archived view_context_manager approach.
    """
    # Source configuration
    source_configured: bool = False
    source_type: str = ""  # "folder", "files", "auto_import"
    source_path: str = ""
    selected_files: List[str] = field(default_factory=list)

    # Destination configuration
    destination_configured: bool = False
    save_option: str = "csv"  # "csv", "master", "archive"
    save_path: str = ""
    update_database: bool = True

    # Processing state
    processing: bool = False
    can_process: bool = False
    process_button_text: str = "Select Files First"

    # UI state
    status_message: str = "Select source files or folder to begin"
    error_message: str = ""

    # Auto-import state
    auto_import_enabled: bool = False
    auto_import_pending_count: int = 0

    # Recent folders (Quick Access) - Phase 3
    recent_source_folders: List[str] = field(default_factory=list)

    def update_can_process(self) -> None:
        """Update can_process based on current state."""
        self.can_process = (
            self.source_configured and
            self.destination_configured and
            not self.processing and
            len(self.selected_files) > 0
        )

        # Update process button text - keep it simple
        if self.processing:
            self.process_button_text = "Processing..."
        else:
            self.process_button_text = "Process Files"

    def reset(self) -> None:
        """Reset state to initial values."""
        self.source_configured = False
        self.source_type = ""
        self.source_path = ""
        self.selected_files.clear()
        self.destination_configured = False
        self.processing = False
        self.error_message = ""
        self.status_message = "Select source files or folder to begin"
        self.update_can_process()


class StateManager:
    """
    Consolidated state and UI synchronization management.

    This class combines:
    - State data management (UpdateDataState)
    - UI synchronization (from WidgetStateManager)
    - State-to-view updates
    - Guide pane management
    """

    def __init__(self, view: 'IUpdateDataView', info_bar_service, folder_monitor_service, *, guide_presenter: Optional['GuidePanePresenter'] = None):
        """
        Initialize the consolidated state manager.

        Args:
            view: The view interface for UI updates
            info_bar_service: Service for info bar messages
            folder_monitor_service: Service for folder monitoring
            guide_presenter: Optional Guide Pane presenter for MVP-compliant guide updates
        """
        # State data
        self.state = UpdateDataState()

        # UI sync dependencies
        self.view = view
        self.info_bar_service = info_bar_service
        self.folder_monitor_service = folder_monitor_service
        self.guide_presenter = guide_presenter

    # =============================================================================
    # STATE MANAGEMENT METHODS
    # =============================================================================

    def update_can_process(self):
        """Delegate to state object."""
        self.state.update_can_process()

    def reset(self):
        """Delegate to state object."""
        self.state.reset()

    # =============================================================================
    # EXPLICIT STATE SETTERS (single source of truth)
    # =============================================================================

    def set_source_files(self, files: List[str]) -> None:
        """Set source as an explicit file list."""
        self.state.source_type = 'files'
        self.state.source_configured = bool(files)
        self.state.selected_files = list(files or [])
        # Derive source folder from first file when available
        try:
            import os
            self.state.source_path = os.path.dirname(files[0]) if files else ''
        except Exception:
            self.state.source_path = ''
        self.update_can_process()

    def set_source_folder(self, path: str, files: Optional[List[str]] = None) -> None:
        """Set source as a folder with optional enumerated files."""
        self.state.source_type = 'folder'
        self.state.source_configured = bool(path)
        self.state.source_path = path or ''
        if files is not None:
            self.state.selected_files = list(files or [])
        self.update_can_process()

    def set_destination(self, option: str, path: Optional[str] = None) -> None:
        """Set destination (save option and optional path)."""
        self.state.save_option = option or self.state.save_option
        self.state.save_path = path or ''
        # Consider destination configured if any valid option is chosen
        self.state.destination_configured = bool(option)
        self.update_can_process()

    def set_update_database(self, enabled: bool) -> None:
        """Set update database toggle and recompute processability."""
        self.state.update_database = bool(enabled)
        self.update_can_process()

    def set_processing(self, is_processing: bool) -> None:
        """Set processing flag and recompute derived state."""
        self.state.processing = bool(is_processing)
        self.update_can_process()

    # =============================================================================
    # UI SYNCHRONIZATION METHODS (from WidgetStateManager)
    # =============================================================================

    def sync_state_to_view(self) -> None:
        """
        Sync presenter state to view.

        MVP pattern: Presenter state is source of truth, view reflects state.
        This replaces the archived view_context_manager approach.
        """
        try:
            log.debug(
                f"[StateManager] sync_state_to_view: source_type={self.state.source_type}, "
                f"folder='{self.state.source_path}', files={len(self.state.selected_files)}"
            )
            # Update process button state and text
            self.view.set_process_enabled(self.state.can_process)
            self.view.set_process_button_text(self.state.process_button_text)

            # Update status message
            if self.state.status_message:
                self.info_bar_service.publish_message(self.state.status_message)

            # Update guide pane based on current state
            self.update_guide_pane()

        except Exception as e:
            # Log error but don't crash the application
            log.error(f"Error syncing state to view: {e}")

    def update_guide_pane(self) -> None:
        """Update guide pane content based on current state."""
        try:
            if not getattr(self, 'guide_presenter', None):
                return
            if self.state.source_type == 'folder':
                log.debug(
                    f"[StateManager] update_guide_pane -> folder path: '{getattr(self.state, 'source_path', '')}', "
                    f"files={len(getattr(self.state, 'selected_files', []))}"
                )
                self.update_guide_pane_for_folder()
            elif self.state.source_type == 'files':
                log.debug(
                    f"[StateManager] update_guide_pane -> files list: count={len(self.state.selected_files)}; "
                    f"inferredFolder='{getattr(self.state, 'source_path', '')}'"
                )
                self.update_guide_pane_for_files()
            else:
                # No-op; presenter will update when a concrete state arrives
                return

        except Exception as e:
            log.error(f"Error updating guide pane: {e}")

    def update_guide_pane_for_folder(self) -> None:
        """Update guide pane for folder source type."""
        try:
            if not getattr(self, 'guide_presenter', None):
                return
            folder_path = getattr(self.state, 'selected_folder', self.state.source_path)
            file_count = len(getattr(self.state, 'selected_files', []))

            # Drive guide pane via presenter (Qt-free)
            # Only list files summary; presenter will fetch discovery state via FileDiscoveryManager.
            if folder_path:
                log.debug(
                    f"[StateManager] guide(folder): on_files_listed -> folder='{folder_path}', count={file_count}; "
                    f"no on_discovery_toggled during sync"
                )
                self.guide_presenter.on_files_listed(
                    count=file_count,
                    folder=folder_path,
                    source_type='folder',
                )
            else:
                # No folder set: no toggle, neutral message
                log.debug(
                    f"[StateManager] guide(folder): on_files_listed -> folder=None, count={file_count}"
                )
                self.guide_presenter.on_files_listed(
                    count=file_count,
                    folder=None,
                    source_type='folder',
                )
                
        except Exception as e:
            log.error(f"Error updating folder guide: {e}")

    def update_guide_pane_for_files(self) -> None:
        """Update guide pane for files source type."""
        try:
            if not getattr(self, 'guide_presenter', None):
                return
            file_count = len(self.state.selected_files)

            # Infer folder for discovery context (used for toggle + badge)
            folder_path = ''
            spath = getattr(self.state, 'source_path', '')
            if spath:
                folder_path = spath
            elif file_count > 0:
                try:
                    import os
                    folder_path = os.path.dirname(self.state.selected_files[0])
                except Exception:
                    folder_path = ''

            # Drive guide pane via presenter, passing the folder if known so context options render immediately
            # Only list files summary; presenter will fetch discovery state via FileDiscoveryManager.
            log.debug(
                f"[StateManager] guide(files): on_files_listed -> folder='{folder_path or None}', count={file_count}; "
                f"no on_discovery_toggled during sync"
            )
            self.guide_presenter.on_files_listed(
                count=file_count,
                folder=folder_path or None,
                source_type='files',
            )

        except Exception as e:
            log.error(f"Error updating files guide: {e}")
