# ✅ Task Backlog: UpdateDataPresenter Decomposition

This document contains a granular checklist of all tasks required for the refactoring. We will pull tasks from this backlog for each session.

---

### Phase 1: Project Setup & State Extraction

- [x ] **1.1** Create directory: `src/fm/modules/update_data/presenter/`
- [x ] **1.2** Create empty file: `src/fm/modules/update_data/presenter/__init__.py`
- [x ] **1.3** Create empty file: `src/fm/modules/update_data/presenter/state_manager.py`
- [x ] **1.4** Move `UpdateDataState` class definition from `ud_presenter.py` to `state_manager.py`.
- [x ] **1.5** In `ud_presenter.py`, remove the local `UpdateDataState` definition.
- [ x] **1.6** In `ud_presenter.py`, add `from .presenter.state_manager import UpdateDataState`.
- [ x] **1.7** Run all tests and confirm the application starts and runs without errors.

### Phase 2: Widget State Manager Extraction

- [x] **2.1** Create empty file: `src/fm/modules/update_data/presenter/widget_state_manager.py`
- [x] **2.2** In `widget_state_manager.py`, create the `WidgetStateManager` class.
- [x] **2.3** Implement `__init__(self, view, state, info_bar_service, folder_monitor_service)` in `WidgetStateManager`.
- [x] **2.4** Move method `_sync_state_to_view` from `ud_presenter.py` to `WidgetStateManager` and rename to `sync_state_to_view`.
- [x] **2.5** Move method `_update_guide_pane` from `ud_presenter.py` to `WidgetStateManager` and rename to `update_guide_pane`.
- [x] **2.6** Move methods `_update_guide_pane_for_folder` and `_update_guide_pane_for_files` to `WidgetStateManager`.
- [x] **2.7** In `ud_presenter.py`, instantiate `self.widget_state_manager = WidgetStateManager(...)` in `_connect_signals()`.
- [x] **2.8** In `ud_presenter.py`, replace all calls to the original methods with calls to `self.widget_state_manager`.
- [x] **2.9** Run all tests and confirm UI updates correctly.

### Phase 3: Source Manager Extraction

- [x] **3.1** Create empty file: `src/fm/modules/update_data/presenter/source_manager.py`
- [x] **3.2** In `source_manager.py`, create the `SourceManager` class.
- [x] **3.3** Implement `__init__(self, view, state, widget_state_manager, folder_monitor_service, local_bus, info_bar_service)` in `SourceManager`.
- [x] **3.4** Move all source-related methods (`_handle_source_select`, `_enrich_file_info`, `_handle_folder_monitor_file_discovered`, etc.) to `SourceManager`.
- [x] **3.5** In `ud_presenter.py`, instantiate `self.source_manager` in `_connect_signals()`.
- [x] **3.6** In `_connect_signals`, wire the `source_select_requested` signal to `self.source_manager.handle_source_select`.
- [x] **3.7** Run all tests and confirm source selection works correctly.

### Phase 4: Archive & Processing Manager Extraction

- [x] **4.1** Create empty files for `archive_manager.py` and `processing_manager.py`.
- [x] **4.2** Implement `ArchiveManager` class and move `_handle_save_select` and `_handle_save_option_change` logic.
- [x] **4.3** Implement `ProcessingManager` class and move `_handle_process` and `_on_processing_*` event handlers.
- [x] **4.4** Instantiate managers in `ud_presenter.py` in `_connect_signals()`.
- [x] **4.5** Connect signals for save and process to the new managers.
- [x] **4.6** Implement inter-manager communication (source -> archive -> processing).
- [x] **4.7** Update event subscriptions to use processing manager.
- [x] **4.8** Remove original methods from presenter.
- [ ] **4.9** Run all tests and confirm the end-to-end workflow is successful.

### Phase 5: Final Review and Cleanup

- [x] **5.1** Move `ud_presenter.py` to `presenter/update_data_presenter.py`.
- [x] **5.2** Update all necessary imports that pointed to the old location.
- [x] **5.3** Review all new manager classes for clarity and consistency.
- [x] **5.4** Add docstrings and comments where needed.
- [x] **5.5** Delete any old, unused methods from the main presenter.
- [x] **5.6** Final test run.

[ ] Some funtionality needs to be restored.
[ ] An architectural review is in order .