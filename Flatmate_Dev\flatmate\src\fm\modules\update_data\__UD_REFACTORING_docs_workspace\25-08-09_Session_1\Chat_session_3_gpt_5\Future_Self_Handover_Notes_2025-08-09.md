# Update Data UI — Future Me Handover (Session 1)

Date: 2025-08-09
Author: Cascade

## Quick Start
- Run the app
  - cd `.../flatmate`
  - Activate venv: `venv` (alias)
  - Start: `fm`
  - Fallback (when alias not available): from `src/` run
    - `"../.venv_fm313/Scripts/python.exe" -m fm.main`
- Open these files
  - `src/fm/modules/update_data/_ui/ud_view.py`
  - `src/fm/modules/update_data/_ui/_view/left_panel_components/widgets/widgets.py`
  - `src/fm/modules/update_data/models/config.py`

## Critical Fix (resolved)
- Fixed: `_connect_center_pane_intents()` implemented in `ud_view.py::UpdateDataView` and invoked by `setup_ui()`.
- Behaviour: wires `UDFileView.add_files_requested` → `self.local_bus.emit(ViewEvents.ADD_FILES_REQUESTED.value, None)`.
- Note: `CenterPanelManager` exposes `file_pane` (UDFileView). v2 is internal to `UDFileView`; fallback handles this correctly.
- Follow-up: remove broad try/except around wiring per fail-fast policy (see Immediate Tasks).

## Immediate Tasks (Phase 1)
- [x] Unify labels with enums
  - In `widgets.py`, replaced hard-coded strings with `SourceOptions`/`SaveOptions` `.value` from `models/config.py`.
- [x] Clean up mismatched/speculative `ViewEvents` in `local_event_bus.py`.
- [x] Implement core view interface methods in `UpdateDataView`
  - Implemented: `set_source_option`, `get_save_option`, `get_update_database`, `set_save_select_enabled`, `set_process_button_text`, `set_process_enabled`, `set_all_controls_enabled`.
- [ ] Trim broad try/except in `ud_view.py` that obscure errors (fail-fast policy). (Partially removed in interface methods; remaining in adapters/dialog handling)
- [ ] Smoke test
  - App starts, left panel selections work, process/cancel, centre pane “add files” emits expected event.

## This Session (Summary)
- Direct widget API usage in `ud_view.py` for interface methods; removed broad try/except around these paths (fail-fast).
- Confirmed widget APIs in `left_panel_layout.py` and `widgets.py`.
- Left panel remains a layout-only component; all signal routing is centralised in the view.
- Removed speculative navigation method `UpdateDataPresenter.request_transition` (deleted; single-view module; no event-based navigation).

## Short-Term Architecture (Phase 2)
- Move left panel user intents off the event bus to explicit view interface methods (MVP).
- Keep the local bus for multi-listener/async flows (dialogue requests/results, state, `FILE_LIST_UPDATED`).
- Standardise bus payloads to dataclasses per `proposed_events.md/csv`.

## Design Decision Update — Left Panel
- The left panel becomes a layout manager only.
- The view (`UpdateDataView`) should call the widgets directly for reads/updates, not re-emit widget signals.
- Presenter remains Qt-free and talks to the view interface only.

## Key Truths
- `SourceOptions` and `SaveOptions` in `models/config.py` are authoritative for labels.
- Use `ViewEvents.ADD_FILES_REQUESTED.value` for the centre pane “add files” intent (distinct from left panel source select).
- Presenter remains Qt-free—communicate via view interface/adapters.

## References
- `src/fm/modules/update_data/_ui/ud_view.py`
- `src/fm/modules/update_data/_ui/_view/left_panel_layout.py`
- `src/fm/modules/update_data/_ui/_view/left_panel_components/widgets/widgets.py`
- `src/fm/modules/update_data/_ui/_view/center_panel_layout.py`
- `src/fm/modules/update_data/models/config.py`
- `src/fm/modules/update_data/services/local_event_bus.py`

## Definition of Done (next session)
- [x] App starts without `_connect_center_pane_intents` error.
- [x] Left panel labels sourced from enums.
- [ ] No broad try/except masking errors in `ud_view.py`.
- [x] Centre pane “add files” intent wired to `ViewEvents.ADD_FILES_REQUESTED.value`.
- [ ] `UpdateDataView` implements required interface methods (no presenter AttributeErrors). (Core methods implemented; full audit + smoke test pending)
- [x] Remove speculative `request_transition` from `ud_presenter.py` and purge any references to `REQUEST_VIEW_TRANSITION`.

## UI Polish — Guide Pane (Session 3)

* __Frame styling__
  - Thin green border `#4CAF50`, radius 6, transparent background.
  - `_update_frame_style()` now keeps a constant, subtle border (no padding/background changes).

* __External padding ownership__
  - `GuidePaneWidget._setup_ui()` sets `layout.setContentsMargins(2,2,2,2)` so the pane owns its external padding.
  - Wrapper in `center_panel_layout.py::CenterPanelManager._create_panes()` uses zero margins/spacing to avoid double padding.

* __Message display__
  - Replaced `QTextEdit` with compact `QLabel` (`wordWrap=True`), hidden when empty.
  - State colour/weight handled in `display()`; avoids blocky backgrounds.

* __Options declutter__
  - Flattened `QGroupBox` (no border, tight margins/spacings).
  - Checkbox indicators themed subtly green; spacing reduced.
  - Single row: “Enable file discovery” + inline info button.
  - Emits `publish_toggle_auto_queue_requested(folder, enabled)`; visibility toggled only when children exist.

* __Archive summary__
  - `archive_summary_label` styled subtle green, hidden by default; `set_archive_summary()` controls content.

* __Lint/syntax fix__
  - Removed stray triple-quote in `guide_pane.py` and unified frame styling.

* __Fail-fast__
  - Broad try/except removed around view interface paths; remaining adapters/dialogue handling still to trim.

* __Follow-ups__
  - Consider hiding the “Options” title when only one option is present.
  - Micro-polish font weights/icons for scanability.
  - Investigate console `QPainter` warnings seen during run (likely from custom paint paths outside the guide pane).
