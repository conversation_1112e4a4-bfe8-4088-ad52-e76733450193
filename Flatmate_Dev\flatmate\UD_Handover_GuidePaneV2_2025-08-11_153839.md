---
description: Guide Pane V2 refactor + splitter fix handover
---

# Handover: Guide Pane V2 Refactor and Test
Date: 2025-08-11 15:38:39 (NZ)

## Summary
- Implemented Source/Archive symmetry in Guide Pane V2 with explicit, discoverable slots.
- Removed legacy/opaque components and fail‑fasted layout logic.
- Fixed large blank space under Source by correcting <PERSON><PERSON><PERSON>litter sizing to match content height.
- Restored default placeholder message for Source on startup.
- Ensured presenter remains Qt‑free; UI logic stays in view.

## Changes by File

- c:/Users/<USER>/_DEV/__PROJECTS/Flatmate_Dev/flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/guide_pane_components/source_section.py
  - Added dedicated path slot (`_path_row`, `_path_label`).
  - APIs: `set_path_html()`, `clear_path()`, `show_path()`, `show_message()`, `show_enable_option()`, `set_enable_checked()`, `connect_enable_toggled()`.
  - Placeholder message kept: "No files or folders selected…". No extra margins; hard-left. Managed by `SlotRow` for zero layout footprint when hidden.

- c:/.../guide_pane_components/archive_section.py
  - Removed deprecated imports; kept existing path slot API.

- c:/.../guide_pane_components/base_components/section.py
  - Default inter-row spacing set to 0. Info label collapses to zero height when empty.

- c:/.../guide_pane_components/base_components/slot_row.py
  - Confirms full collapse on hide (fixed/min height = 0), zero gaps when invisible.

- c:/.../center_panel_components/guide_pane_v2.py
  - Simplified to sections-only; removed top-level main message.
  - Source paths now render in Source path slot; non-path text uses the info line.
  - Per-folder option strictly bound to Source path presence; connected to `_on_src_enable_toggled`.
  - Default placeholder shown on first render: `self.section_source.show_message(True)`.
  - Layout spacing tightened; fail‑fast (no broad try/except) except one minor case in `set_archive_mode()`.

- c:/.../_view/center_panel_layout.py
  - Dropped legacy `guide_pane` import and fallback. V2 only.
  - Wrapper set to size-to-contents; splitter top pane collapsible.
  - Startup sizes now use `guide_pane.sizeHint().height()` for the top, eliminating blank space.

- c:/.../guide_pane_components/__init__.py and base_components/__init__.py
  - Re-export only `Section`, `SlotRow`. Removed `FolderPathRow`/`KeyValueRow` dependencies.

## Execution/Environment
- Use your normal run path to ensure theme loads correctly:
  - From `flatmate/`: activate venv alias: `venv`
  - Run: `fm`
- Avoid `python -m fm.main` for now as it may bypass theme initialisation on first run.

## Behavioural Checks
- Source empty → placeholder visible, path/toggle hidden.
- Source set to a valid folder → Source path slot shows rich path; placeholder hidden; per‑folder toggle visible.
- Source cleared or non-path text → path/toggle hidden; placeholder shown; info text used for non-path summaries.
- Archive "Same as Source" → mirrors Source path in Archive path slot.

## Design Notes
- Presenter remains Qt‑free; view exposes clear methods. MVP respected.
- Fail‑fast: removed broad try/except around geometry updates where modified.
- UK spelling and explicit code per project rules. Logging via `from fm.core.services.logger import log`.

## Open Items / Next Steps
- Optional: remove the last broad try/except in `GuidePaneV2.set_archive_mode()`.
- Optional: attach context menus to Source path label (Archive already has one).
- Directory Discovery MVP wiring (per memory): presenter -> services without events; checkbox text: “Enable file discovery for this folder”.

## Risks/Notes
- Theme sometimes misapplies on first run after widget changes; re-running via `fm` typically resolves.
- Any external styling applied to labels may need harmonising after italics/typography choices for the placeholder.

## Tested Outcome
- App launches via `fm` with correct theme.
- Splitter no longer reserves excess height; guide pane collapses to content.
- Placeholder shows by default; hides when Source path is set.
