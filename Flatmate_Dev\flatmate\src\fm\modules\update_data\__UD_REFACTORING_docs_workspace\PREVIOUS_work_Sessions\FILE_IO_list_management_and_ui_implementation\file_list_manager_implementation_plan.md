# File List Manager Implementation Plan

## Current State Analysis

### Existing Components
1. **FileManager** (`_presenter/file_manager.py`):
   - Maintains `file_paths_list` (canonical list)
   - Handles file/folder selection dialogs
   - Integrates with FolderMonitorService
   - File enrichment via FileInfoService

2. **FolderMonitorService** (`core/services/folder_monitor_service.py`):
   - Tracks `known_folders` with monitoring status
   - Each folder: `{'path': str, 'monitored': bool}`
   - Persists configuration to settings

3. **FilePane** (`_view/center_panel_components/file_pane.py`):
   - FileBrowser → FileDisplayWidget for file display
   - Monitor folder checkbox
   - File add/remove UI operations

### Issues Identified
- **Mixed responsibilities**: FileManager handles both UI dialogs and data management
- **Recent add/remove issues**: User reported problems with file operations
- **Interface bypass**: Direct calls to `view.center_panel.set_files()`
- **Dual communication**: Mix of events and direct interface calls

## Proposed Solution: FileListManager

### Core Concept
Create a dedicated `FileListManager` that owns the canonical file list and integrates with existing folder monitoring, while keeping FileManager focused on UI dialogs.

### Design Principles
- **Single responsibility**: File list operations only
- **Event-driven**: Responds to state changes via events
- **Integration**: Works with existing FolderMonitorService
- **Simple dataclass**: For folder monitoring status as user suggested
- **NOT in state.py**: As explicitly requested by user

## Implementation Structure

### 1. FileListManager Class

```python
# _presenter/file_list_manager.py

@dataclass
class MonitoredFolder:
    """Simple dataclass for folder monitoring status."""
    path: str
    monitor_new_files: bool = False
    last_scan: Optional[datetime] = None
    file_count: int = 0

class FileListManager:
    """
    Manages the canonical file list and folder monitoring integration.
    
    Responsibilities:
    - Owns canonical file_paths_list
    - Manages monitored folders list
    - Handles file add/remove operations
    - Integrates with FolderMonitorService
    - Emits events for UI updates
    """
    
    def __init__(self, folder_monitor_service, local_bus, file_info_service):
        self.folder_monitor_service = folder_monitor_service
        self.local_bus = local_bus
        self.file_info_service = file_info_service
        
        # Canonical file list - moved from FileManager
        self.file_paths_list: List[str] = []
        
        # Monitored folders - simple dict as user suggested
        self.monitored_folders: Dict[str, MonitoredFolder] = {}
        
        self._subscribe_to_events()
    
    def set_files(self, file_paths: List[str], source_path: str = ""):
        """Set the canonical file list."""
        self.file_paths_list = file_paths.copy()
        
        # Update monitored folder if source is a directory
        if source_path and os.path.isdir(source_path):
            self._update_monitored_folder(source_path, len(file_paths))
        
        # Emit event for UI updates
        self.local_bus.emit(ViewEvents.FILE_LIST_UPDATED.value, 
                           FileListUpdatedEvent(files=self.file_paths_list, source_path=source_path))
    
    def add_files(self, new_files: List[str]) -> bool:
        """Add files to the canonical list."""
        try:
            # Avoid duplicates
            unique_files = [f for f in new_files if f not in self.file_paths_list]
            if unique_files:
                self.file_paths_list.extend(unique_files)
                self._emit_list_updated()
                log.debug(f"Added {len(unique_files)} files to list")
                return True
            return False
        except Exception as e:
            log.error(f"Error adding files: {e}")
            return False
    
    def remove_file(self, file_path: str) -> bool:
        """Remove file from the canonical list."""
        try:
            if file_path in self.file_paths_list:
                self.file_paths_list.remove(file_path)
                self._emit_list_updated()
                log.debug(f"Removed file from list: {os.path.basename(file_path)}")
                return True
            return False
        except Exception as e:
            log.error(f"Error removing file: {e}")
            return False
    
    def get_files(self) -> List[str]:
        """Get copy of canonical file list."""
        return self.file_paths_list.copy()
    
    def set_folder_monitoring(self, folder_path: str, enabled: bool):
        """Set monitoring status for a folder."""
        if folder_path in self.monitored_folders:
            self.monitored_folders[folder_path].monitor_new_files = enabled
        else:
            self.monitored_folders[folder_path] = MonitoredFolder(
                path=folder_path, 
                monitor_new_files=enabled
            )
        
        # Update FolderMonitorService
        self.folder_monitor_service.set_folder_monitored(folder_path, enabled)
        
        log.debug(f"Folder monitoring {'enabled' if enabled else 'disabled'} for: {folder_path}")
```

### 2. Event Definitions

```python
# services/events_data.py - Add new events

@dataclass
class FileListUpdatedEvent:
    """Event for file list updates."""
    files: List[str]
    source_path: str = ""

@dataclass
class FileAddedEvent:
    """Event for individual file addition."""
    file_path: str

@dataclass
class FileRemovedEvent:
    """Event for individual file removal."""
    file_path: str

@dataclass
class FolderMonitoringToggledEvent:
    """Event for folder monitoring changes."""
    folder_path: str
    enabled: bool
```

### 3. FileManager Refactoring

```python
# _presenter/file_manager.py - Simplified

class FileManager:
    """
    Handles file/folder selection dialogs and save location logic.
    
    Delegates file list management to FileListManager.
    """
    
    def __init__(self, view, state_manager, file_list_manager, local_bus, info_bar_service):
        self.view = view
        self.state_manager = state_manager
        self.file_list_manager = file_list_manager  # Delegate file list operations
        self.local_bus = local_bus
        self.info_bar_service = info_bar_service
        
        # Remove file_paths_list - now owned by FileListManager
        self.selected_source = None
    
    def _select_files(self):
        """Select individual files - delegate list management."""
        file_paths = self.view.show_files_dialog("Select CSV Files to Process", last_dir)
        if file_paths:
            # Update state
            self.state.selected_files = file_paths
            self.state.source_type = 'files'
            self.selected_source = file_paths
            
            # Delegate to FileListManager
            self.file_list_manager.set_files(file_paths)
            
            # Enrich and display
            enriched_info = self.enrich_file_info(file_paths)
            self.view.display_enriched_file_info(enriched_info)
            
            self.state.update_can_process()
            self.state_manager.sync_state_to_view()
    
    def _select_folder(self):
        """Select folder - delegate list management."""
        folder_path = self.view.show_folder_dialog("Select Folder Containing CSV Files", last_dir)
        if folder_path:
            discovered_files = self._discover_files_in_folder(folder_path)
            
            # Update state
            self.state.selected_folder = folder_path
            self.state.source_type = 'folder'
            self.selected_source = folder_path
            
            # Delegate to FileListManager
            self.file_list_manager.set_files(discovered_files, folder_path)
            
            # Continue with enrichment and UI updates...
```

### 4. UI Integration

The FilePane will subscribe to FileListManager events:

```python
# _view/center_panel_components/file_pane.py - Event subscription

class FilePane(BasePane):
    def __init__(self, parent=None):
        super().__init__(parent)
        self._init_ui()
        self._connect_signals()
        self._subscribe_to_file_list_events()
    
    def _subscribe_to_file_list_events(self):
        """Subscribe to file list manager events."""
        # Get local bus from presenter (via interface)
        if hasattr(self.parent(), 'get_local_bus'):
            local_bus = self.parent().get_local_bus()
            local_bus.subscribe(ViewEvents.FILE_LIST_UPDATED.value, self._on_file_list_updated)
    
    def _on_file_list_updated(self, event_data):
        """Handle file list updates from FileListManager."""
        self.set_files(event_data.files, event_data.source_path)
```

## Implementation Benefits

### 1. Clear Separation of Concerns
- **FileManager**: UI dialogs and save location logic
- **FileListManager**: Canonical file list and monitoring
- **FilePane**: Display and user interactions

### 2. Robust File Operations
- Centralized add/remove logic
- Duplicate prevention
- Error handling and logging
- Event-driven UI updates

### 3. Integrated Monitoring
- Simple dataclass for folder status
- Integration with existing FolderMonitorService
- Persistent monitoring configuration

### 4. Event-Driven Architecture
- Consistent communication pattern
- Loose coupling between components
- Easy to test and maintain

## Implementation Steps

1. **Create FileListManager** - New class with canonical list ownership
2. **Add Event Definitions** - New events for file list operations
3. **Refactor FileManager** - Remove list management, add delegation
4. **Update FilePane** - Subscribe to FileListManager events
5. **Update Presenter** - Wire up FileListManager in dependency injection
6. **Test Integration** - Ensure add/remove operations work correctly

## Migration Notes

- **Backward Compatibility**: Existing interfaces remain unchanged
- **Gradual Migration**: Can be implemented incrementally
- **Testing**: Focus on add/remove operations that were problematic
- **Configuration**: Monitored folders persist via existing FolderMonitorService

This approach addresses the user's requirements for focused file list management while maintaining integration with existing systems and avoiding major architectural changes.
