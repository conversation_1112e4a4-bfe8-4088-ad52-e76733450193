/* Global styles */
* {
    font-family: ".AppleSystemUIFont", "Helvetica Neue", Arial, sans-serif;
    font-size: 1em;  /* Base font size */
}

QWidget {
    background-color: #1E1E1E;  /* BACKGROUND_DARK */
    color: white;
}

/* Main panels */
#left_panel, #right_panel {
    background-color: #1a381f;  /* NAV_BACKGROUND - deep green */
}

/* Right side bar - no min-width to conform to content */
#right_side_bar {
    background-color: #1a381f;  /* NAV_BACKGROUND - deep green */
}

#left_panel QLabel {
    background: transparent;
}

/* Center Panel */
#center_panel_content {
    background-color: #1E1E1E;  /* BACKGROUND_DARK */
    padding: 0;                 /* No padding */
    border: none;              /* No border */
}

/* Center Panel Widgets */
#center_panel_content QTextEdit,
#center_panel_content QTableView {
    border: none;               /* Remove widget borders */
    background-color: #1E1E1E;  /* Same as panel */
}

/* Text Display */
QTextEdit {
    background-color: #1E1E1E;
    color: #D4D4D4;
    border: none;
}

QTextEdit::selection {
    background-color: #264F78;
}

/* Table View */
QTableView {
    border: none;
    background-color: #1E1E1E;
}

QTableView::item {
    padding: 4px;
}

QTableView::item:alternate {
    background-color: #2A2A2A;
}

QTableView::item:selected {
    background-color: #3E3E3E;
}

/* Tree View - same styling as table view */
QTreeWidget {
    border: none;
    background-color: #1E1E1E;
    color: white;
}

QTreeWidget::item {
    padding: 4px;
}

QTreeWidget::item:alternate {
    background-color: #2A2A2A;
}

QTreeWidget::item:selected {
    background-color: #3B7443;  /* Green selection to match app theme */
    color: white;
}

QTreeWidget::item:hover {
    background-color: #2A2A2A;
}

QHeaderView::section {
    background-color: #333333;
    color: white;
    padding: 4px;
    border: none;
    border-right: 1px solid #555555;
    border-bottom: 1px solid #555555;
}

/* Display Area */
#display_area {
    padding: 10px;
    font-size: 14px;
}

/* Scroll Areas */
QScrollArea {
    border: none;
    background: transparent;
}

QScrollArea > QWidget > QWidget {
    background: transparent;
}

/* Content Area */
#content_area {
    background-color: #1E1E1E;  /* BACKGROUND_DARK */
    padding: 1.5em;            /* Consistent padding */
    border: none;
}

#content_area QTextEdit, 
#content_area QTableView {
    background-color: #1E1E1E;  /* Same as background */
    border: none;
    border-radius: 4px;
    padding: 0.5em;
}

#content_area QLabel {
    background: transparent;
    padding: 0.3em 0;          /* Vertical padding only */
}

/* Main Action Buttons */
QPushButton[type="action_btn"] {
    background-color: #3B8A45;  /* PRIMARY */
    color: white;
    border: none;
    border-radius: 8px;       /* Larger radius */
    padding: 8px 16px;        /* More padding */
    height: 45px;             /* Taller */
    font-size: 1.15em;        /* 16px -> 1.15em */
    font-weight: bold;
}

QPushButton[type="action_btn"]:hover {
    background-color: #4BA357;  /* PRIMARY_HOVER */
}

QPushButton[type="action_btn"]:pressed {
    background-color: #2E6E37;  /* PRIMARY_PRESSED */
}

/* Navigation Buttons */
QPushButton[type="nav_btn"] {
    background-color: #3B7443;  /* Same as secondary */
    color: white;
    border: none;
    border-radius: 6px;
    padding: 6px;
    height: 35px;
    font-size: 1em;          /* 14px -> 1em (base size) */
}

QPushButton[type="nav_btn"]:hover {
    background-color: #488E52;
}

QPushButton[type="nav_btn"]:pressed {
    background-color: #2E5A35;
}

/* Select Buttons - Smaller variant for selection actions */
QPushButton[type="select_btn"] {
    background-color: #3B7443;  /* Same as secondary */
    color: white;
    border: none;
    border-radius: 4px;  /* Slightly smaller radius */
    padding: 4px 8px;    /* Less padding */
    height: 25px;        /* Smaller height */
    font-size: 0.85em;       /* 12px -> 0.85em */
}

QPushButton[type="select_btn"]:hover {
    background-color: #4B8453;
}

QPushButton[type="select_btn"]:pressed {
    background-color: #2E6436;
}

/* Exit/Cancel/Quit Buttons */
QPushButton[type="exit_btn"] {
    background-color: #2E5A35;  /* Darker base color */
    color: #E0E0E0;            /* Slightly dimmer text */
    border: none;
    border-radius: 6px;
    padding: 6px;
    height: 35px;
    font-size: 0.93em;       /* 13px -> 0.93em */
    opacity: 0.9;              /* Slightly transparent */
}

QPushButton[type="exit_btn"]:hover {
    background-color: #386B3F;  /* Darker hover */
}

QPushButton[type="exit_btn"]:pressed {
    background-color: #264B2C;  /* Darker pressed */
}

/* Headings */
QLabel#heading {
    color: white;
    font-size: 1.7em;        /* 24px -> 1.7em */
    font-weight: bold;
    margin-bottom: 10px;
}

/* Use explicit naming conventions for widget styling */
QLabel#lbl_panel_subheading,
QLabel#subheading {  /* Keep old name for backward compatibility */
    color: #CCCCCC;         /* Explicit calm-white color */
    font-size: 1.3em;       /* 18px -> 1.3em */
    font-weight: bold;
    margin-top: 15px;
    margin-bottom: 5px;
}

/* Navigation Pane Buttons */
QToolButton[class="nav_button"] {
    border: none;
    background-color: transparent;
    padding: 6px;
    margin: 2px;
    color: var(--color-text-primary);
    border-radius: 4px;
}

QToolButton[class="nav_button"]:checked {
    background-color: rgba(255, 255, 255, 30);
    border-radius: 4px;
}

QToolButton[class="nav_button"]:hover {
    background-color: rgba(255, 255, 255, 15);
    border-radius: 4px;
}

/* Settings toggle */
#settings_toggle {
    background: none;
    border: none;
    color: white;
    font-size: 1.2em;  /* 20% larger than base */
}

#settings_toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Labels */
QLabel {
    color: white;
    font-size: 1em;  /* Same as base */
}

/* ComboBox */
QComboBox {
    background-color: #2C2C2C;  /* BACKGROUND */
    color: white;
    border: 1px solid #3B8A45;  /* PRIMARY */
    border-radius: 4px;
    padding: 5px;
}

QComboBox:hover {
    background: rgba(255, 255, 255, 0.1);
}

QComboBox::drop-down {
    border: none;
}

QComboBox::down-arrow {
    border: none;
}

/* Checkbox */
QCheckBox {
    color: white;
    background-color: transparent;
    spacing: 5px;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 1px solid #3B8A45;  /* PRIMARY */
    border-radius: 3px;
    background-color: #2C2C2C;  /* BACKGROUND */
}

QCheckBox::indicator:hover {
    background-color: rgba(59, 138, 69, 0.2);  /* PRIMARY with transparency */
    border-color: #4BA357;  /* PRIMARY_HOVER */
}

QCheckBox::indicator:checked {
    background-color: #3B8A45;  /* PRIMARY */
    border-color: #3B8A45;
}

QCheckBox::indicator:checked:hover {
    background-color: #4BA357;  /* PRIMARY_HOVER */
    border-color: #4BA357;
}

QCheckBox::indicator:pressed {
    background-color: #2E6E37;  /* PRIMARY_PRESSED */
    border-color: #2E6E37;
}

QComboBox QAbstractItemView {
    background-color: #1E1E1E;
    color: white;
    border: 1px solid #3B8A45;  /* PRIMARY */
}

QComboBox QAbstractItemView::item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

QComboBox QAbstractItemView::item:selected {
    background-color: #3B8A45;  /* PRIMARY */
    color: white;
}

/* Scrollbars */
QScrollBar:vertical {
    background-color: #1C2A3A;
    width: 10px;
}

QScrollBar::handle:vertical {
    background-color: #2C3A4A;
    min-height: 20px;
    border-radius: 5px;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    background-color: #1C2A3A;
    height: 10px;
}

QScrollBar::handle:horizontal {
    background-color: #2C3A4A;
    min-width: 20px;
    border-radius: 5px;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
    width: 0px;
}