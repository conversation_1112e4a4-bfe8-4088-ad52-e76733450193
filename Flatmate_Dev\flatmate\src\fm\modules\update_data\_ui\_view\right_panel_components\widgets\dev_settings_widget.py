"""
Developer Settings Widget for Update Data Module

Provides a UI component for developer settings in the Update Data module.
"""

from PySide6.QtCore import Signal
from PySide6.QtWidgets import QVBoxLayout, QWidget

from fm.gui.components.dev_settings_panel import DevSettingsPanelWithButton


class UdDevSettingsWidget(QWidget):
    """
    Widget for developer settings in the Update Data module.
    
    This widget provides a UI for configuring developer options
    while maintaining clean architectural boundaries.
    
    Signals:
        option_changed: Emitted when a developer option changes
    """
    
    option_changed = Signal(str, bool)
    
    def __init__(self, parent=None):
        """Initialize the developer settings widget."""
        super().__init__(parent)
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components."""
        # Main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Dev settings panel with toggle button
        self.dev_settings_panel = DevSettingsPanelWithButton(self)
        layout.addWidget(self.dev_settings_panel)
        
        # Add spacer
        layout.addStretch()
    
    def _connect_signals(self):
        """Connect widget signals."""
        self.dev_settings_panel.option_changed.connect(self.option_changed)
    
    def get_option(self, option_id):
        """
        Get the current state of a developer option.
        
        Args:
            option_id: Identifier of the option
            
        Returns:
            bool: Current state of the option
        """
        return self.dev_settings_panel.get_option(option_id)
    
    def set_option(self, option_id, checked):
        """
        Set the state of a developer option.
        
        Args:
            option_id: Identifier of the option
            checked: New state of the option
        """
        self.dev_settings_panel.set_option(option_id, checked)
