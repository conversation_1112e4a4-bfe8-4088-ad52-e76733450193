# Component Recipe: Smart Widget

This recipe provides a step-by-step guide for creating a self-contained "Smart Widget" in the Flatmate application. Smart Widgets follow a clean Model-View-Presenter (MVP) pattern, have zero dependencies on other parts of the application, and expose a clean, high-level API.

---
# Note: provisional yet to be approved

### 1. Purpose

Use the Smart Widget pattern when you need to create a complex, reusable UI component that has its own internal state and logic (e.g., a file list with add/remove buttons, a data table with sorting and filtering).

### 2. File Structure

A Smart Widget should be organised into its own directory within the `fm.gui` module. The structure should be as follows:

```
/fm/gui/components/[widget_name]/
    ├── __init__.py
    ├── config.py
    ├── models.py
    ├── view.py
    └── presenter.py
```

-   **`__init__.py`**: Exposes the main `[WidgetName]View` class.
-   **`config.py`**: Contains a dataclass for the widget's configuration options.
-   **`models.py`**: Defines any data structures (like dataclasses or a `QAbstractItemModel`) needed by the widget.
-   **`view.py`**: The main Qt widget. It handles all UI setup and exposes a clean public API.
-   **`presenter.py`**: The presenter connects the view's signals to the application's logic. It contains no Qt code.

### 3. Key Principles & Rules

1.  **Zero Qt in Presenter**: The presenter **must not** import `PyQt5` or `PyQt6`. It should be pure Python.
2.  **Clean View API**: The `view.py` class is the public face of the widget. It should expose high-level methods (e.g., `set_files()`, `clear_selection()`) rather than exposing internal Qt widgets directly.
3.  **High-Level Signals**: The view should emit high-level, domain-specific signals (e.g., `file_added`, `item_selected`), not raw Qt signals (e.g., `clicked`, `textChanged`).
4.  **Configuration via Dataclass**: All configurable aspects of the widget (e.g., max items, display text) should be defined in a `config.py` dataclass.
5.  **Self-Contained**: The widget should not have any dependencies on other application services or managers. It is a standalone component.

### 4. Boilerplate Code Example

#### `view.py`

```python
from PyQt5.QtWidgets import QWidget
from PyQt5.QtCore import pyqtSignal
# >> # ! Note, we use PySide6, not PyQt5

from .presenter import MyWidgetPresenter
from .config import MyWidgetConfig

class MyWidgetView(QWidget):
    # Define high-level signals
    item_added = pyqtSignal(str)

    def __init__(self, config: MyWidgetConfig, parent=None):
        super().__init__(parent)
        self.config = config
        self.presenter = MyWidgetPresenter(self, self.config)

        self._setup_ui()
        self._connect_signals()

    def _setup_ui(self):
        # All UI widget creation and layout management happens here
        pass

    def _connect_signals(self):
        # Connect internal Qt widget signals to presenter slots
        # e.g., self.add_button.clicked.connect(self.presenter.on_add_clicked)
        pass

    # --- Public API ---

    def add_item(self, item_text: str):
        # Public method to interact with the widget
        # (Implementation would likely update a model)
        self.item_added.emit(item_text)

    def clear(self):
        # Public method to clear the widget's state
        pass
```

#### `presenter.py`

```python
class MyWidgetPresenter:
    def __init__(self, view, config):
        self.view = view
        self.config = config

    def on_add_clicked(self):
        # This is a slot connected to a signal from the view
        # It contains the application logic, with no Qt code.
        new_item = "A new item"
        self.view.add_item(new_item)
```

### 5. Example Usage

```python
# In your main application window

from fm.gui.components.my_widget import MyWidgetView, MyWidgetConfig

# 1. Create a configuration object
config = MyWidgetConfig(max_items=10)

# 2. Instantiate the widget
my_widget = MyWidgetView(config)

# 3. Connect to its high-level signals
my_widget.item_added.connect(self.handle_item_added)

# 4. Add it to a layout
self.main_layout.addWidget(my_widget)

# 5. Call its public API methods
my_widget.add_item("Initial item")
```
