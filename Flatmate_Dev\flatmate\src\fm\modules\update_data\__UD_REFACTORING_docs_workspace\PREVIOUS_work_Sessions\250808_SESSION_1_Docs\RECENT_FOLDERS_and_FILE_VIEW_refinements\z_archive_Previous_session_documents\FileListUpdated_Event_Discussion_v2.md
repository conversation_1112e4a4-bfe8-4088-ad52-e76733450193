# File List System - Discussion & Proposed Fix (v2)

**Last Updated:** 2025-08-06

This document captures the discussion about fixing the file list functionality in the Update Data module. It replaces jargon with plain English and outlines a pragmatic path forward, based on the existing architecture.

---

## The Goal: A Simple, Reliable File List

In plain English, this is how the application should work:

1.  **The File List is the Core:** The list of files shown on the screen can be populated in several ways (selecting a folder, adding individual files, folder monitoring). This list is the one that gets processed.
2.  **Show Useful Information:** The list must display details about each file in a "File Info" column (e.g., "Kiwibank | Basic CSV"). If a file isn't recognized, it should just show its type (e.g., ".csv").
3.  **Quick Access Folders:** The side panel should remember the last folders you've used for selecting source files and archives, making them easy to select again. If a folder is deleted from the disk, it should be removed from this quick access list.

---

## Architectural Insights & Current Problems

- **Existing Structure:** The application uses a solid event-driven pattern with a central `FileListManager` that controls the file list. This is good.
- **Unclear Responsibilities:** The roles of different 'manager' components are not always obvious. For example, `file_management.py` seems to handle the Left Panel's state, which is not clear from its name.

Based on this, we've identified two main gaps:

1.  **The "File Info" is Missing:** The `FileListManager` correctly manages *which* files are in the list, but it doesn't figure out the *details* about them (bank, type, etc.). It sends a plain list of file paths to the UI, forcing the UI to do extra work. This is inefficient and the root cause of the missing information in the file table.

2.  **The "Quick Access" List Doesn't Exist:** The logic to remember and manage the list of recently used folders for the side panel hasn't been implemented.

---

## The Solution: A Pragmatic, Two-Step Fix

Instead of a major architectural overhaul, we will take a direct approach to fix what's broken within the existing structure.

### Step 1: Make the `FileListManager` Smarter (Immediate Priority)

The `FileListManager` will become the single source of truth for both the file list **and** the file details.

1.  **Enrich the Data:** When the file list is changed (files are added or removed), the `FileListManager` will immediately use the existing `file_info_service` to get the details for each file.
2.  **Send Complete Information:** It will then send out its `FileListUpdatedEvent` with a complete, ready-to-use list of file information objects (containing path, bank, type, etc.), not just plain file paths.
3.  **Simplify the UI:** The UI's only job will be to display the information it receives. It won't need to do any lookups or data processing itself.

**Benefit:** This is a direct fix for the missing "File Info" column. It's a small change in one place that makes the whole system more efficient and robust.

### Step 2: Address the Quick Access List (Next)

After fixing the file info display, we will address the "quick access" folder list. This logic will likely be added to `file_management.py`, and we should consider renaming that file to something more explicit, like `left_panel_manager.py`, to improve clarity.


-----
PO /user response - `>>`

# >> 
Regards the proposed naming convention and devision of labour - processing handles the actual job sheet, so to speak, what gets sent to the pipeline director (dw_director) 
State handles state...
(which is some what nebulous to me personally Im still wrapping my head aorund it ) 
file list handles a bit more than just a list of files 
.
For building a conceptual model ... for a relative novice like myself .. naming conventions and logical devisions of labour, dir structure.... these things are very important .


-----
# gem 2.5 : 

---

### **Proposal: A Clearer Division of Labour**

Based on our discussion, here is a proposed clarification of roles for the key 'manager' components. This aims to make the architecture more logical and easier to understand.

*   **`file_info_manager.py` (The Librarian)**
    *   **Role:** The single source of truth for the file list and all associated information (bank, type, format, etc.).
    *   **Responsibilities:**
        *   Holds the master list of `FileInfoData` objects.
        *   Uses the `file_info_service` to enrich data when the list changes.
        *   Publishes the `FileListUpdatedEvent` with the complete, rich data.
    *   **Does NOT:** Handle user clicks, show dialogs, or start processing. It only manages the data.

*   **`file_config_manager.py` (The Gatekeeper)**
    *   **Final Name:** `file_config_manager.py`.
    *   **Role:** Handles all user interactions related to selecting source files.
    *   **Responsibilities:**
        *   Listens for user actions from the Left Panel (e.g., "Select Files" button click).
        *   Opens the file/folder selection dialogs.
        *   Receives the selected paths and tells the `FileListManager` to update its list.
        *   Manages the "quick access" list of recent folders.

*   **`ProcessingManager` (The Foreman)**
    *   **Role:** Manages the actual processing job.
    *   **Responsibilities:**
        *   Listens for the "Process Files" action.
        *   Gets the final, definitive list of files from the `FileListManager`.
        *   Passes this list to the `dw_director` to begin the pipeline.
        *   Publishes events about the processing status (Started, Completed, Error).

This structure creates clear boundaries. If there's a problem:
- **Choosing files?** Check the `file_config_manager.py`.
- **File info wrong in the table?** Check the `file_info_manager.py`.
- **Processing fails?** Check the `ProcessingManager`.

---

## **Implementation Plan: Fix the File Info Display**

This section provides a detailed, step-by-step guide for a developer to implement the required changes. 

### **Part 1: Modify `.../_ui/ui_events.py`**

**Objective:** Standardise the event name and create a data structure for rich file information.

1.  **Locate and Open the File:**
    `flatmate/src/fm/modules/update_data/_ui/ui_events.py`

2.  **Define `FileInfoData`:**
    Above the `FileDisplayUpdateEvent` class, add the following new dataclass. This will be the standard container for file details.
    ```python
    from typing import Dict, List, Optional

    @dataclass
    class FileInfoData:
        """Dataclass to hold enriched information for a single file."""
        path: str
        bank_type: str
        format_type: str
        handler: Optional[str]
        size_str: str
        # This can be extended with more fields like created_date later.
    ```

3.  **Rename and Update the Event:**
    - Find the `FileDisplayUpdateEvent` class.
    - Rename it to `FileListUpdatedEvent`.
    - Change its `files` attribute to expect a list of the new `FileInfoData` objects.

    **Change this:**
    ```python
    @dataclass
    class FileDisplayUpdateEvent:
        """Event data for file display updates."""
        files: List[str]
        source_path: str
        timestamp: str = field(default_factory=_now_iso)
    ```

    **To this:**
    ```python
    @dataclass
    class FileListUpdatedEvent:
        """Event data for file display updates, carrying enriched file info."""
        files: List[FileInfoData]  # Changed from List[str]
        source_path: str
        timestamp: str = field(default_factory=_now_iso)
    ```

### **Part 2: Modify `.../_presenter/file_info_manager.py` (after renaming)**

**Objective:** Make the `FileListManager` use the new event structure and enrich the file data.

1.  **Locate and Open the File:**
    `flatmate/src/fm/modules/update_data/_ui/_presenter/file_info_manager.py`

2.  **Update Imports:**
    Add `FileInfoData` to the import from `..ui_events`.

    **Change this:**
    `from ..ui_events import FileListUpdatedEvent, FileAddedEvent, FileRemovedEvent`

    **To this:**
    `from ..ui_events import FileListUpdatedEvent, FileAddedEvent, FileRemovedEvent, FileInfoData`

3.  **Update `__init__`:**
    Add a new attribute to store the list of enriched file info objects.

    **Add this line:**
    ```python
    # After self.file_paths_list
    self.file_info_list: List[FileInfoData] = []
    ```

4.  **Update `set_files` Method:**
    Modify this method to fetch and store the enriched data.

    **Change this block:**
    ```python
    # Store canonical list
    self.file_paths_list = file_paths.copy() if file_paths else []
    
    log.debug(f"[FILE_LIST_MANAGER] Set file list: {len(self.file_paths_list)} files")
    ```

    **To this:**
    ```python
    # Store canonical list of paths
    self.file_paths_list = file_paths.copy() if file_paths else []
    
    # Enrich file info and store it
    if self.file_paths_list:
        enriched_data = self.file_info_service.discover_files(self.file_paths_list)
        self.file_info_list = [FileInfoData(**info) for info in enriched_data]
    else:
        self.file_info_list = []

    log.debug(f"[FILE_LIST_MANAGER] Set file list: {len(self.file_paths_list)} files")
    ```

5.  **Update `add_files` Method:**
    Modify this method to enrich and add new files.

    **Change this block:**
    ```python
    if unique_files:
        self.file_paths_list.extend(unique_files)
        self._emit_list_updated()
    ```

    **To this:**
    ```python
    if unique_files:
        # Add new paths to the path list
        self.file_paths_list.extend(unique_files)
        
        # Enrich the new files and add them to the info list
        enriched_data = self.file_info_service.discover_files(unique_files)
        new_info_list = [FileInfoData(**info) for info in enriched_data]
        self.file_info_list.extend(new_info_list)

        self._emit_list_updated()
    ```

6.  **Update `remove_file` Method:**
    Modify this method to remove from both lists.

    **Change this line:**
    `self.file_paths_list.remove(file_path)`

    **To this:**
    ```python
    self.file_paths_list.remove(file_path)
    # Also remove the corresponding info object
    self.file_info_list = [info for info in self.file_info_list if info.path != file_path]
    ```

7.  **Update `clear_files` Method:**
    Modify this method to clear both lists.

    **Change this:**
    `self.file_paths_list.clear()`

    **To this:**
    ```python
    self.file_paths_list.clear()
    self.file_info_list.clear()
    ```

8.  **Update `_emit_list_updated` Method:**
    Modify this method to send the enriched list.

    **Change this line in the `event_data` creation:**
    `files=self.file_paths_list.copy(),`

    **To this:**
    `files=self.file_info_list.copy(),`

---

## **Implementation Plan: Refactor to `file_config_manager.py`**

This section provides a detailed guide for refactoring `file_management.py` and implementing the "Quick Access" recent folders feature.

### **Part 1: Rename and Refactor**

**Objective:** Rename the file and class, and remove redundant responsibilities.

1.  **Rename File:**
    - In the directory `.../_ui/_presenter/`, rename `file_management.py` to `file_config_manager.py`.

2.  **Rename Class:**
    - Open the new `file_config_manager.py`.
    - Rename the class `FileManager` to `FileConfigManager`.

3.  **Update Imports:**
    - Search the codebase for any imports of `FileManager` (e.g., in the main presenter) and update them to import `FileConfigManager` from the new module name.

4.  **Remove Redundant Code:**
    - In `FileConfigManager`, delete the entire `enrich_file_info` method. This is now handled by `file_info_manager.py`.
    - In `FileConfigManager.__init__`, delete the line `self.file_info_service = FileInfoService()`. The associated import can also be removed.
    - In `FileConfigManager._process_selected_files`, find and delete the lines that call the enrichment service. The method should now just pass the raw file paths to the `file_list_manager`.

### **Part 2: Implement "Quick Access" Recent Folders**

**Objective:** Add logic to manage and persist a list of recently used source folders.

1.  **Update Configuration (`.../config/ud_config.py`):**
    - Add a new config key for storing the recent folders.
    ```python
    # In the ud_config dictionary
    'recent_source_folders': [],
    'max_recent_folders': 5,
    ```

2.  **Update State (`.../_presenter/state_coordinator.py`):**
    - In the `UpdateDataState` dataclass, add a field to hold the list in memory.
    ```python
    # In UpdateDataState
    recent_source_folders: List[str] = field(default_factory=list)
    ```

3.  **Implement Logic in `FileConfigManager`:**
    - **`__init__`:** Load the recent folders from the config into the state at startup.
        ```python
        # In FileConfigManager.__init__
        self.state.recent_source_folders = ud_config.get('recent_source_folders', [])
        ```
    - **Create `_update_recent_folders` method:** Add this new private method to the class.
        ```python
        def _update_recent_folders(self, folder_path: str):
            """Adds a folder to the top of the recent list and persists it."""
            if not folder_path or not os.path.isdir(folder_path):
                return

            recent_list = self.state.recent_source_folders
            # Remove if already present to avoid duplicates and move to top
            if folder_path in recent_list:
                recent_list.remove(folder_path)
            
            # Add to the top
            recent_list.insert(0, folder_path)

            # Trim the list to the max size
            max_folders = ud_config.get('max_recent_folders', 5)
            self.state.recent_source_folders = recent_list[:max_folders]

            # Persist to config
            ud_config.set('recent_source_folders', self.state.recent_source_folders)

            # Sync state to view to update UI
            self.state_manager.sync_state_to_view()
        ```
    - **Update `_select_folder`:** Call the new method after a folder is successfully selected.
        ```python
        # In _select_folder, after a folder_path is confirmed
        if folder_path:
            # ... existing code ...
            self._update_recent_folders(folder_path)
        ```

### **Part 3: Unit Tests**

**Objective:** Create simple tests to verify the "Quick Access" logic. These can be added to a new test file like `.../tests/test_file_config_manager.py`.

```python
# Example Test Snippet

def test_update_recent_folders_add_new():
    # Setup mock config_manager and state
    manager = setup_mock_manager()
    manager.state.recent_source_folders = ['/path/c', '/path/b']
    
    manager._update_recent_folders('/path/a')
    
    assert manager.state.recent_source_folders == ['/path/a', '/path/c', '/path/b']

def test_update_recent_folders_move_existing_to_top():
    # Setup
    manager = setup_mock_manager()
    manager.state.recent_source_folders = ['/path/c', '/path/b', '/path/a']
    
    manager._update_recent_folders('/path/a')
    
    assert manager.state.recent_source_folders == ['/path/a', '/path/c', '/path/b']

def test_update_recent_folders_trims_list():
    # Setup
    manager = setup_mock_manager()
    # Assume max_recent_folders is 3
    manager.state.recent_source_folders = ['/path/c', '/path/b', '/path/a']
    
    manager._update_recent_folders('/path/d')
    
    assert manager.state.recent_source_folders == ['/path/d', '/path/c', '/path/b']
    assert len(manager.state.recent_source_folders) == 3
```


# >> ud_file_view currently manages getting the file_info from file_info_service.py 
this will need to be changed 
we should have a file_info_model
This may exist in the file_info_service already, and it may be a logical place for it
However 
We have various data models to consider 
these are 
the file paths list and relvant file_info
form which can be gleaned:
recent source folders
recent archive folders (if any - not same as source)
we also have @option_types.py 
The main reference table for the file_info_manager
(in _presenter folder)
will need the file_paths and the relevant info foe each file

Where should we keep these types of files theay are currently just in the _ui folder
is this optimal or should we have a `types` folder
or some other name?
clarity is key

I have also have a @update-dat-ui-deepdive-guide.md which should be farely up to date and may prove useful for conetxt... 

I am also wondering about teh utility or purpose of a "file_list_updated" event vs more specific add/remove/clear events...

