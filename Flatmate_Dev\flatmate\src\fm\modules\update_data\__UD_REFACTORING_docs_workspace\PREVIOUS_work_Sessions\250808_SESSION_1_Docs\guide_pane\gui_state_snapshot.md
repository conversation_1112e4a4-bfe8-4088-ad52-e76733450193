# Update Data – GUI State Snapshot (Current Baseline)

Concise snapshot of the UI and state for reference during guide pane refinements. UK spelling; minimal, practical.

## Panels & Key Widgets
- **Source Controls**: source dropdown (with MRU), "Choose Folder…" action opens native folder dialog at MRU path; currently a folder is selected.
- **File List View**: shows queued files with metadata (incl. created date), supports removal; currently N > 0 files queued.
- **Guide Pane**:
  - Primary status message: "Ready to process N files" (info/ready state)
  - Archive summary line: "Archive: Same as Source"
  - Contextual options group: single checkbox `[ ] Auto‑queue new files` (per‑folder; default from prefs)
- **Archive/Save Options**: options include "Same as Source" (selected) and MRU entries (open dialog for new selection). Compact labels for MRU.
- **Actions/Buttons**: Process button enabled, conveys count (e.g., "Process N Files"); clear/remove selected; refresh/rescan if present.

## State & Preferences
- **Source**: `current_source_folder = <abs_path>` (folder chosen)
- **Files**: `queued_files = N` (N > 0), enriched via `FileInfoService`; unsupported files are filtered/ignored.
- **Archive**: `save_option = SAME_AS_SOURCE` (summary shows accordingly)
- **Per‑folder option**: `auto_queue_by_folder[current_source_folder] = <bool>` (default False if unset)
- **Global prefs (excerpt)**:
  - `update_data.quick_select_recent_source: <bool>`
  - `update_data.auto_queue_new_files: <bool>` (fallback default)
  - `update_data.auto_queue_by_folder: { "<folder_path>": true|false }`

## Event & Flow Snapshot
- **Presenter ↔ View** (interface):
  - `set_guide_archive_summary("Archive: Same as Source")`
  - `show_guide_source_options(auto_queue_enabled=<per-folder>)`
  - Status update via `display(...)`/`set_status('ready', count=N)`
- **Local Bus**:
  - `AUTO_QUEUE_TOGGLED { folder_path, enabled }` (single toggle; monitoring implied)
- **Services**:
  - On auto‑queue enabled: monitoring service starts for `current_source_folder`; new files → queue.
  - On disabled: monitoring stops for that folder.

## Visual Text (example)
- Status: "Ready to process 10 files"
- Archive summary: "Archive: Same as Source"
- Options: `[ ] Auto‑queue new files`

## Notes
- Use UK spelling in UI text.
- Presenter contains no Qt; view maps interface to widget.
- Keep guide pane uncluttered: one clear status, one summary line, one per‑folder option.
