# Auto‑Queue (Per‑Folder) — Implementation Guide & Checklist (Session 2)

Last updated: 2025-08-08
Scope: Minimal, production‑usable MVP for auto‑queue toggle with clean ownership and testable seams
Audience: Update Data engineers and AI agents (Architect rules apply)

---

## 1) Architectural Snapshot (authoritative)

- **Intent**: `ViewEvents.AUTO_QUEUE_TOGGLED` (payload: `enabled: bool`)
- **UI Surface**: Guide Pane under Source path
  - `GuidePaneWidget.show_source_context_options(auto_queue_enabled=bool)`
  - Compact row: checkbox “Enable auto‑queue for this folder” + inline (i) tooltip
  - Emits `publish_toggle_auto_queue_requested(bool)` → bridged by `ud_view.py` to local bus
- **Presenter**: subscribes to `AUTO_QUEUE_TOGGLED`, resolves folder, delegates logic
- **Manager**: `AutoQueueManager` (new) — persist + reconcile monitoring
- **Rendering**: `state_coordinator.py` reads from `ud_config` and calls `view.show_guide_source_options(...)`
- **Source of Truth**: `ud_config.is/set_auto_queue_enabled(folder)` (never read monitoring runtime for UI)
- **Services**: Keep `RecentFoldersService` (MRU) and `FolderMonitorService` (watchers) separate

References: `Auto-Queue_Toggle_Architecture_Overview.md`, `Folder_Handling_Discussion.md`, `DOCS/_GUIDES/Update-Data-UI-Deep-Dive.md` (§5.x)

---

## 2) Ownership & Files

- `src/fm/modules/update_data/_ui/_view/center_panel_components/guide_pane.py`
  - Renders checkbox + info button; emits `publish_toggle_auto_queue_requested`
- `src/fm/modules/update_data/_ui/ud_view.py`
  - Bridges guide pane signal → `ViewEvents.AUTO_QUEUE_TOGGLED`
  - Provides `show_guide_source_options(monitor_enabled=False, auto_queue_enabled=False)`
- `src/fm/modules/update_data/ud_presenter.py`
  - Subscribes to `AUTO_QUEUE_TOGGLED`; resolves current folder
  - Delegates to `AutoQueueManager.set_auto_queue(folder, enabled)`; triggers state sync
- `src/fm/modules/update_data/_ui/_presenter/auto_queue_manager.py` (new)
  - Implements `set_auto_queue` and `get_auto_queue`
- `src/fm/modules/update_data/_ui/_presenter/state_coordinator.py`
  - Renders from `ud_config.is_auto_queue_enabled(folder)` on source change/init

---

## 3) Minimal Implementation Steps (MVP)

1) **Guide Pane UI** (done)
- Add checkbox row + (i) button and wire to `publish_toggle_auto_queue_requested`.

2) **View wiring** (exists)
- In `ud_view._create_guide_pane()`, connect signal to local bus: `AUTO_QUEUE_TOGGLED`.

3) **Presenter handling** (thin‑out)
- Subscribe to `AUTO_QUEUE_TOGGLED`.
- Resolve folder via existing `_resolve_current_folder()` or `FileConfigManager.get_current_folder()`.
- Delegate to `AutoQueueManager.set_auto_queue(folder, enabled)`.
- Call `state_manager.sync_state_to_view()`.

4) **Manager** (add)
- `set_auto_queue(folder, enabled)`:
  - `ud_config.set_auto_queue_enabled(folder, enabled)`
  - `folder_monitor_service.set_folder_monitored(folder, enabled)`
- `get_auto_queue(folder) -> bool`:
  - `return ud_config.is_auto_queue_enabled(folder)`

5) **State rendering**
- Ensure `state_coordinator.py` calls `view.show_guide_source_options(auto_queue_enabled=ud_config.is_auto_queue_enabled(folder))` wherever source context changes.

---

## 4) Naming & Conventions

- Intent: `AUTO_QUEUE_TOGGLED`
- Manager: `AutoQueueManager` in `_ui/_presenter/auto_queue_manager.py`
- Methods: `set_auto_queue(folder, enabled)`, `get_auto_queue(folder)`
- Config: `ud_config.is_auto_queue_enabled(folder)`, `ud_config.set_auto_queue_enabled(folder, enabled)`
- UK spelling, explicit readable code, minimal surface area

---

## 5) Developer Checklist (Implementation)

- [ ] Guide Pane shows checkbox + (i) under Source path
- [ ] Checkbox state initialises from `ud_config.is_auto_queue_enabled(current_folder)`
- [ ] Toggling emits `AUTO_QUEUE_TOGGLED` via local bus
- [ ] Presenter only acts when a single folder context is resolved; otherwise no-op (no inference/multi-folder handling in MVP)
- [ ] Presenter delegates to `AutoQueueManager` (no inline persistence/monitoring code)
- [ ] Manager persists → reconciles monitoring without UI access
- [ ] Presenter triggers `state_manager.sync_state_to_view()` post‑toggle
- [ ] `state_coordinator.py` never reads monitoring runtime state for UI
- [ ] MRU code untouched; no coupling with monitoring

---

## 6) QA Checklist (Behaviour)

- [ ] With a source folder selected, checkbox reflects saved state on module start
- [ ] Toggling updates config; restarting preserves state
- [ ] Toggling starts/stops monitoring via `FolderMonitorService` reliably
- [ ] Selecting a different source updates checkbox from that folder’s preference
- [ ] Checkbox shown/enabled only when a single folder context is resolved; hidden/disabled otherwise
- [ ] No duplicate dialogs or conflicting channels during Add Files vs Source Select
- [ ] Tooltip present and clear: describes per‑folder persistence and monitoring

---

## 7) Future Configuration Pane (deferred)

- Central management of monitored folders list (from `ud_config`) and MRU
- Preferences (e.g., `quick_select_recent_source`)
- Requires main window layout changes; out of MVP scope
- Out of scope for MVP: multi-folder inference and bulk enable/disable; manage later via configuration pane

---

## 8) Risks & Mitigations

- **Presenter bloat** → Extract `AutoQueueManager`
- **UI coupled to runtime monitoring** → Render from `ud_config` only
- **Service conflation (MRU vs monitoring)** → Keep separate services and APIs

---

## 9) References (clickable)

* [Update-Data UI Deep Dive](../../../DOCS/_GUIDES/Update-Data-UI-Deep-Dive.md)
* [Auto-Queue Toggle Architecture Overview](./Auto-Queue_Toggle_Architecture_Overview.md)
* [Folder Handling Discussion](./Folder_Handling_Discussion.md)
* [Auto-Queue Toggle Payload Decision](./Auto-Queue_Toggle_Payload_Decision.md)
* [Handover: Auto-Queue Toggle MVP](./Handover_Auto-Queue_Toggle_MVP.md)
* [Session Log (2025-08-08)](./Session_Log_250808.md)
