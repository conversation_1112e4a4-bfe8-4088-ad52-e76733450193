# File Dialogue System PRD

**Date:** 2025-08-03  
**Author:** <PERSON> (Product Manager)  
**Status:** Ready for Implementation  
**Priority:** P0 - Critical

## Executive Summary

This PRD addresses critical architectural violations in the file dialogue system introduced by unauthorised changes. The solution restores proper MVP separation, completes the original refactoring plan, and fixes critical path errors while ensuring clean interface compliance.

## Problem Definition

### Current Issues

1. **Architectural Violations**
   - Presenter directly accesses Qt widgets via `self.view.get_main_window()`
   - Interface contract breaches (calling undefined methods)
   - Inconsistent component naming (`get_files.py` vs planned `file_selector.py`)

2. **Technical Issues**
   - Hardcoded last directory path in `file_management.py`
   - `main_window`/`get_main_window()` mismatch causing errors
   - Incomplete refactoring creating technical debt

3. **Root Causes**
   - Junior engineer bypassed planned refactoring approach
   - Lack of code review process to catch violations
   - Quick fixes applied without understanding system design

### Impact Assessment

1. **Architectural Integrity**
   - MVP pattern violations create tight coupling
   - Testing becomes difficult or impossible
   - Future refactoring is significantly more complex

2. **Maintenance Burden**
   - Inconsistent component naming increases cognitive load
   - Interface mismatches create subtle bugs
   - Band-aid fixes accumulate technical debt

3. **Development Velocity**
   - New features built on unstable foundation
   - Increased bug rate due to architectural inconsistencies
   - Onboarding new developers becomes more difficult

## Success Criteria

1. **Architectural Compliance**
   - Zero Qt imports in presenter layer
   - All dialogue operations through view interface
   - Clean component naming and structure
   - Proper separation of concerns

2. **Functional Requirements**
   - File selection works across all platforms
   - Folder selection discovers files correctly
   - Last directory persistence works correctly
   - Error handling provides clear feedback

3. **Quality Metrics**
   - All existing functionality preserved
   - No performance regressions
   - Clean code review approval

## Solution Design

### Component Structure

```
shared_components/
├── file_selector.py          # Main file selection component
│   ├── FileSelector          # Static methods for file operations
│   ├── FolderDialogue        # Folder selection dialogue
│   ├── FileDialogue          # File selection dialogue
│   └── FileUtils             # File discovery utilities
└── [deprecated] get_files.py # To be removed after migration
```

### Interface Design

```python
# IUpdateDataView interface methods (already exist)
def show_files_dialog(self, title: str, initial_dir: str) -> List[str]
def show_folder_dialog(self, title: str, initial_dir: str) -> str

# Presenter usage (clean, testable)
file_paths = self.view.show_files_dialog("Select Files", last_dir)
folder_path = self.view.show_folder_dialog("Select Folder", last_dir)
```

### Integration Points

1. **FileManager Integration**
   - Update imports to use new `file_selector.py`
   - Ensure all dialogue operations use view interface
   - Remove any direct Qt widget access

2. **ud_file_view Integration**
   - File view component should access `FileSelector` directly
   - Update view with appropriate event after selection
   - Maintain clean separation of concerns

## Implementation Phases

### Phase 1: Critical Architectural Fixes

**Priority:** P0 - Critical  
**Timeline:** Immediate (30 minutes)

1. **Rename and Refactor Component**
   - Rename `get_files.py` → `file_selector.py`
   - Refactor class names for clarity
   - Convert global function to static method
   - Update imports in `file_management.py`

2. **Fix MVP Violations**
   - Remove all `get_main_window()` references
   - Ensure presenter uses only interface methods
   - Remove Qt imports from presenter layer

3. **Validate Interface Compliance**
   - Confirm view interface methods exist and work
   - Test presenter-view communication
   - Verify no direct Qt widget access

### Phase 2: System Optimisation

**Priority:** P1 - High  
**Timeline:** After Phase 1 (30 minutes)

1. **Enhance Error Handling**
   - Improve validation for folder existence
   - Handle permission errors gracefully
   - Provide meaningful error messages

2. **Configuration Management**
   - Centralise last-directory storage
   - Implement proper config validation
   - Handle edge cases properly

### Phase 3: Testing & Documentation

**Priority:** P2 - Medium  
**Timeline:** After Phase 2 (15 minutes)

1. **Testing**
   - Test file selection workflows
   - Test folder selection workflows
   - Validate error handling

2. **Documentation**
   - Update architecture documentation
   - Document interface contract
   - Add clear usage examples

## Risk Assessment

### High Risk Areas

1. **Breaking Changes**
   - Renaming `get_files.py` will require import updates
   - Interface changes may affect other modules
   - Testing required to ensure no regressions

2. **Integration Points**
   - Other modules may depend on current structure
   - Configuration changes may affect user workflows
   - Platform-specific dialogue behaviour variations

### Mitigation Strategies

1. **Gradual Migration**
   - Keep old component during transition
   - Update imports module by module
   - Test each change before proceeding

2. **Comprehensive Testing**
   - Test all dialogue workflows before deployment
   - Validate cross-platform behaviour
   - User acceptance testing for UX changes

## Implementation Checklist

- [ ] Rename `get_files.py` to `file_selector.py`
- [ ] Refactor class names (`FolderDialogue`, `FileDialogue`)
- [ ] Create `FileSelector` class with static methods
- [ ] Update imports in `file_management.py`
- [ ] Remove all Qt coupling from presenter
- [ ] Validate interface method usage
- [ ] Test basic file/folder selection
- [ ] Enhance error handling in `FileUtils`
- [ ] Centralise directory configuration
- [ ] Add configuration validation
- [ ] Test error scenarios
- [ ] Update architecture documentation

## Conclusion

The file dialogue system requires immediate architectural correction to restore MVP integrity and fix critical issues. This PRD outlines a concrete, actionable plan that can be executed quickly to address these concerns while maintaining all existing functionality.

The recommended approach will:
- Restore architectural integrity
- Improve code maintainability
- Enable proper testing and mocking
- Provide a foundation for future enhancements

**Next Action:** Execute Phase 1 implementation to restore architectural compliance before proceeding with any new features.
