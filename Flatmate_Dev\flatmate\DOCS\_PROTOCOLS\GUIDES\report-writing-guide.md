# Report Writing Guide

Comprehensive guide for creating analysis reports, protocol evaluations, and technical assessments.

---

## Report Types & Templates

### **Protocol Analysis Report Template**

Use this template for evaluating protocols, workflows, or system assessments:

```markdown
# [Subject] - Analysis Report

**Date**: YYYY-MM-DD  
**Analysis Type**: [Protocol Evaluation|System Assessment|Technical Analysis]  
**Target**: [What was analyzed]  
**Status**: [COMPLETE|INCOMPLETE|NEEDS_FOLLOWUP]  
**Analyst**: [Who performed the analysis]  

---

## Executive Summary

[2-3 sentences summarizing key findings and recommendations]

---

## Analysis Scope

### **What Was Evaluated**
- [Specific component/system/protocol examined]
- [Boundaries of the analysis]
- [Success criteria used]

### **Methodology**
- [How the analysis was conducted]
- [Tools and techniques used]
- [Evidence collection approach]

---

## Findings

### ✅ **What Worked Well**
1. **[Category]**: [Specific finding with evidence]
2. **[Category]**: [Specific finding with evidence]

### ❌ **Issues Identified**
1. **[Issue Type]**: [Problem description with impact]
2. **[Issue Type]**: [Problem description with impact]

### ⚠️ **Areas for Improvement**
1. **[Area]**: [Current state vs desired state]
2. **[Area]**: [Current state vs desired state]

---

## Evidence Collected

- **[Evidence Type]**: [Description and location]
- **[Evidence Type]**: [Description and location]
- **[Evidence Type]**: [Description and location]

---

## Recommendations

### **Priority 1 - Critical (Immediate)**
1. **[Action]**: [What needs to be done and why]
2. **[Action]**: [What needs to be done and why]

### **Priority 2 - Important (This Week)**
1. **[Action]**: [What needs to be done and why]
2. **[Action]**: [What needs to be done and why]

### **Priority 3 - Enhancement (Future)**
1. **[Action]**: [What needs to be done and why]
2. **[Action]**: [What needs to be done and why]

---

## Next Steps

1. **Immediate**: [What should happen right now]
2. **Short-term**: [What should happen soon]
3. **Long-term**: [What should happen eventually]

---

**Analysis Completed**: YYYY-MM-DD  
**Status**: [Final status]  
**Confidence Level**: [High|Medium|Low] - [Justification]  
**Follow-up Required**: [Yes/No - what kind]
```

---

## Usage Guidelines

### **When to Create Reports**

1. **Protocol Evaluations**: When testing or analyzing workflows
2. **System Assessments**: When evaluating technical components
3. **Problem Analysis**: When investigating complex issues
4. **Architecture Reviews**: When examining system design
5. **Performance Analysis**: When evaluating system performance

### **Report Quality Standards**

#### **Structure Requirements**
- **Clear Executive Summary**: Key findings in 2-3 sentences
- **Specific Evidence**: Concrete examples and data points
- **Actionable Recommendations**: Clear next steps with priorities
- **Proper Classification**: Issues vs improvements vs successes

#### **Content Standards**
- **Be Specific**: Include file paths, line numbers, exact errors
- **Be Objective**: Base findings on evidence, not assumptions
- **Be Complete**: Address all aspects of the analysis scope
- **Be Forward-Looking**: Include future considerations

#### **Evidence Standards**
- **Document Sources**: Where information came from
- **Preserve Context**: Why evidence is relevant
- **Organize Logically**: Group related evidence together
- **Archive Properly**: Store supporting materials appropriately

---

## Report Naming Conventions

### **File Naming Pattern**
```
YYMMDD_<report_type>_<subject>_report.md
```

### **Examples**
- `25722_protocol_analysis_unified_workflow_report.md`
- `25722_system_assessment_gui_components_report.md`
- `25722_performance_analysis_database_queries_report.md`
- `25722_architecture_review_module_structure_report.md`

---

## Integration with Workflows

### **During Analysis Sessions**
1. **Collect Evidence**: Save logs, screenshots, code samples
2. **Document Findings**: Record observations in real-time
3. **Note Decisions**: Capture rationale for conclusions
4. **Track Sources**: Maintain links to supporting information

### **Report Creation Process**
1. **Gather Materials**: Collect all evidence and notes
2. **Structure Findings**: Organize into categories (works/issues/improvements)
3. **Prioritize Recommendations**: Rank by impact and urgency
4. **Review for Completeness**: Ensure all analysis scope is covered
5. **Archive Evidence**: Store supporting materials properly

### **Post-Report Actions**
1. **Share with Stakeholders**: Distribute to relevant parties
2. **Track Implementation**: Monitor progress on recommendations
3. **Update Documentation**: Reflect findings in relevant docs
4. **Schedule Follow-up**: Plan reassessment if needed

---

## Quality Checklist

### **Before Publishing**
- [ ] Executive summary captures key findings
- [ ] All findings are supported by evidence
- [ ] Recommendations are specific and actionable
- [ ] Priority levels are clearly justified
- [ ] Evidence is properly archived
- [ ] Next steps are clearly defined
- [ ] Report follows naming conventions
- [ ] All sections are complete (no empty sections)

### **Content Validation**
- [ ] Another person could understand the findings
- [ ] Conclusions are logical based on evidence
- [ ] Recommendations address identified issues
- [ ] Report scope matches original objectives
- [ ] Technical details are accurate
- [ ] Timeline and priorities are realistic

---

## Advanced Report Types

### **Comparative Analysis Template**
For comparing multiple options or approaches:

```markdown
## Comparison Matrix

| Criteria | Option A | Option B | Option C | Recommendation |
|----------|----------|----------|----------|----------------|
| [Criterion 1] | [Rating/Score] | [Rating/Score] | [Rating/Score] | [Best choice] |
| [Criterion 2] | [Rating/Score] | [Rating/Score] | [Rating/Score] | [Best choice] |

## Decision Rationale
[Why the recommended option was chosen]
```

### **Root Cause Analysis Template**
For investigating problems:

```markdown
## Problem Statement
[Clear description of the issue]

## Investigation Timeline
- **[Time]**: [What was discovered]
- **[Time]**: [What was tried]
- **[Time]**: [What was learned]

## Root Cause Analysis
1. **Immediate Cause**: [What directly caused the problem]
2. **Contributing Factors**: [What made it possible/likely]
3. **Root Cause**: [Underlying system issue]

## Prevention Measures
[How to prevent recurrence]
```

---

**This guide ensures consistent, high-quality reports that provide clear value and actionable insights for decision-making.**
