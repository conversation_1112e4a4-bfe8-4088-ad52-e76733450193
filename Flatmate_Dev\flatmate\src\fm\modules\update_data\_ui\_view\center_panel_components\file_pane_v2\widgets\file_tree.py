"""
File tree widget for displaying file information in a folder/file hierarchy.

Refactored from table to tree to support:
- Tree-like folder/file display with icons
- Default visible columns: Selected Files, File Info, Size, Created
- Optional columns: Type (toggleable via header context menu)
- Resizable columns with sensible defaults; long names elided
- Column width persistence via config system
"""

from PySide6.QtWidgets import (
    QTreeWidget, QTreeWidgetItem, QHeaderView, QAbstractItemView, QMenu
)
from PySide6.QtCore import Qt, Signal, QPoint
from PySide6.QtGui import QIcon
from typing import List, Optional
from ..models import FileInfo, FileViewModel
from ..config import FileConfig
from ..utils import format_file_size, group_files_by_directory
from fm.core.services.logger import log
import os
from pathlib import Path


class FileTree(QTreeWidget):
    """Tree widget for displaying files grouped by directory."""

    # Signals
    file_selected = Signal(str)       # file_path
    file_double_clicked = Signal(str) # file_path

    def __init__(self, model: FileViewModel, config: FileConfig, parent=None):
        super().__init__(parent)
        self._model = model
        self._config = config
        self._columns: List[str] = []
        self._folder_items: dict[str, QTreeWidgetItem] = {}
        self._focused_mode = True  # TEMPORARY: Start in focused mode for testing
        # Ensure this widget picks up QSS theme styles (theme.qss uses #file_tree selectors)
        self.setObjectName("file_tree")
        # Optional: enable alternating row colors so future QSS can style ::item:alternate
        self.setAlternatingRowColors(True)
        self._setup_tree()
        self._connect_signals()

    def _setup_tree(self) -> None:
        """Initialize tree widget and columns."""
        # Updated column order per user requirements: Selected Files, File Info, Size, Created
        self._columns = ["Selected Files", "File Info", "Size", "Created"]

        # Type column removed by default per spec (redundant); guard old configs
        # Force-hide regardless of any legacy config flag
        if hasattr(self._config, "show_file_type") and self._config.show_file_type:
            # Ignore legacy flag to comply with "Type is gone"
            pass

        self.setColumnCount(len(self._columns))
        self.setHeaderLabels(self._columns)

        # Selection behavior
        self.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)

        # Header behavior and sizing
        header = self.header()
        header.setSectionsClickable(True)
        header.setHighlightSections(False)
        header.setStretchLastSection(False)
        # Center header text for all columns
        try:
            from PySide6.QtCore import Qt
            header.setDefaultAlignment(Qt.AlignmentFlag.AlignCenter)
        except Exception:
            pass

        # Ensure the tree itself expands to fill available layout space
        # and responds to window resizes
        self.setSizeAdjustPolicy(QAbstractItemView.SizeAdjustPolicy.AdjustToContentsOnFirstShow)
        self.setExpandsOnDoubleClick(True)
        self.setUniformRowHeights(False)

        # Selected Files column: Interactive at init (so users can resize),
        # final policy will Stretch in _apply_column_width_policy
        selected_files_idx = self._columns.index("Selected Files")
        header.setSectionResizeMode(selected_files_idx, QHeaderView.ResizeMode.Interactive)
        self.setColumnWidth(selected_files_idx, 200)

        # File Info column (shows statement handler info) - make resizable for user
        file_info_idx = self._columns.index("File Info")
        header.setSectionResizeMode(file_info_idx, QHeaderView.ResizeMode.Interactive)
        self.setColumnWidth(file_info_idx, 220)

        # Size column fit to contents (non-resizable by user)
        size_idx = self._columns.index("Size")
        header.setSectionResizeMode(size_idx, QHeaderView.ResizeMode.ResizeToContents)

        # Created column fit to contents (non-resizable by user)
        created_idx = self._columns.index("Created")
        header.setSectionResizeMode(created_idx, QHeaderView.ResizeMode.ResizeToContents)

        # Context menu to toggle optional columns (Type removed; no toggles for core cols)
        self.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.customContextMenuRequested.connect(self._show_header_context_menu)

    def _connect_signals(self) -> None:
        """Connect internal signals."""
        self.itemSelectionChanged.connect(self._on_selection_changed)
        self.itemDoubleClicked.connect(self._on_item_double_clicked)
        # Connect column resize signal for persistence
        self.header().sectionResized.connect(self._on_column_resized)

        # Also react to header geometry changes (window resize/move) to re-apply stretch logic
        self.header().geometriesChanged.connect(self._apply_column_width_policy)

    def refresh_data(self) -> None:
        """Rebuild tree from model files."""
        try:
            # Branch on focused mode
            if self._focused_mode:
                self._refresh_data_focused()
            else:
                # Original full hierarchy mode
                self.clear()
                self._folder_items.clear()

                files = self._model.files
                # Group by parent directory
                groups = group_files_by_directory(files)

                for folder_path, file_infos in groups.items():
                    folder_item = self._ensure_folder_item(folder_path)
                    for fi in file_infos:
                        self._add_file_item(folder_item, fi)

                self.expandAll()

                # Restore selection if possible
                if self._model.selected_file:
                    self._select_file_by_path(self._model.selected_file)

            # Apply column width policy after tree is built
            self._apply_column_width_policy()

        except Exception as e:
            log.error(f"Error refreshing file tree data: {e}")

    def set_focused_mode(self, enabled: bool) -> None:
        """Set focused mode to show only file, parent, and grandparent levels.

        Args:
            enabled: If True, shows only 3 levels (file, parent, grandparent).
                    If False, shows full directory hierarchy.
        """
        if self._focused_mode != enabled:
            self._focused_mode = enabled
            self.refresh_data()  # Rebuild tree with new mode

    def _refresh_data_focused(self, depth: int = 2) -> None:
        """Rebuild tree in focused mode showing only limited hierarchy.

        Args:
            depth: Number of parent levels to show (default 2 = file, parent, grandparent)
        """
        try:
            self.clear()
            self._folder_items.clear()

            files = self._model.files
            if not files:
                return

            def get_parent_chain(path: str, depth: int = 2) -> list[str]:
                """Get parent chain for a file path.

                Returns:
                    List of [file_path, parent_dir, grandparent_dir, ...]
                """
                from pathlib import Path
                p = Path(path)
                chain = [str(p)]
                for _ in range(depth):
                    p = p.parent
                    if p == p.parent:  # Reached root
                        break
                    chain.append(str(p))
                return chain

            # Build minimal tree across all files
            for fi in files:
                chain = get_parent_chain(fi.path, depth)
                if len(chain) < 2:
                    # File at root level, create a simple parent
                    parent_item = self._ensure_specific_folder_item(str(Path(fi.path).parent), allow_recurse=False)
                    self._add_file_item(parent_item, fi)
                else:
                    # Normal case: file, parent, grandparent
                    file_path = chain[0]
                    parent_dir = chain[1]
                    grandparent_dir = chain[2] if len(chain) > 2 else parent_dir

                    # Ensure grandparent node (top-level)
                    gp_item = self._ensure_specific_folder_item(grandparent_dir, allow_recurse=False)

                    # Ensure parent under grandparent (only if different)
                    if parent_dir != grandparent_dir:
                        parent_item = self._ensure_specific_folder_item(parent_dir, parent_override=gp_item, allow_recurse=False)
                    else:
                        parent_item = gp_item

                    # Add file under parent
                    self._add_file_item(parent_item, fi)

            self.expandAll()

            # Restore selection if possible
            if self._model.selected_file:
                self._select_file_by_path(self._model.selected_file)

            # Apply column width policy after tree is built
            self._apply_column_width_policy()

        except Exception as e:
            print(f"Error refreshing focused file tree: {e}")

    def _ensure_specific_folder_item(self, folder_path: str, parent_override: Optional[QTreeWidgetItem] = None, allow_recurse: bool = False) -> QTreeWidgetItem:
        """Create or get folder item with controlled recursion.

        Args:
            folder_path: Path to the folder
            parent_override: Specific parent item to use (overrides recursion)
            allow_recurse: Whether to allow recursive parent creation

        Returns:
            QTreeWidgetItem for the folder
        """
        if folder_path in self._folder_items:
            return self._folder_items[folder_path]

        # Optionally block recursion unless explicitly requested
        parent_item = parent_override
        if parent_item is None and allow_recurse:
            parent_dir = str(Path(folder_path).parent)
            if parent_dir and parent_dir != folder_path:
                parent_item = self._ensure_specific_folder_item(parent_dir, None, allow_recurse=True)

        item = QTreeWidgetItem()
        item.setText(self._columns.index("Selected Files"), self._get_display_folder_name(folder_path))
        item.setData(self._columns.index("Selected Files"), Qt.ItemDataRole.UserRole, None)  # no file path on folder

        # Set folder icon to distinguish from files
        try:
            folder_icon = self.style().standardIcon(self.style().StandardPixmap.SP_DirIcon)
            item.setIcon(self._columns.index("Selected Files"), folder_icon)
        except Exception:
            pass  # Graceful fallback if icon not available

        if parent_item:
            parent_item.addChild(item)
        else:
            self.addTopLevelItem(item)

        self._folder_items[folder_path] = item
        return item

    def _apply_column_width_policy(self) -> None:
        """Apply column width policy based on current mode."""
        header = self.header()
        header.setMinimumSectionSize(60)

        selected_files_idx = self._columns.index("Selected Files")
        file_info_idx = self._columns.index("File Info")
        size_idx = self._columns.index("Size")
        created_idx = self._columns.index("Created")

        # Restore saved column widths if available
        self._restore_column_widths()

        # Make 'Selected Files' user-resizable (Interactive) while still filling remaining space.
        # Strategy:
        # - Use Interactive so the user can resize.
        # - After setting modes, compute remaining width and, if needed, expand this column to fill.
        header.setSectionResizeMode(selected_files_idx, QHeaderView.ResizeMode.Interactive)

        # File Info column uses interactive mode with appropriate width
        header.setSectionResizeMode(file_info_idx, QHeaderView.ResizeMode.Interactive)

        if self._focused_mode:
            # Focused: wider info column since we have more space
            self.setColumnWidth(file_info_idx, 240)
        else:
            # Default: standard info column width
            self.setColumnWidth(file_info_idx, 180)

        # Keep crisp autosizing for size and created
        header.setSectionResizeMode(size_idx, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(created_idx, QHeaderView.ResizeMode.ResizeToContents)

        # After modes are set, expand Selected Files to consume any remaining space.
        try:
            total = self.viewport().width()
            used = 0
            # Sum widths of all columns except Selected Files
            for i, name in enumerate(self._columns):
                if i == selected_files_idx:
                    continue
                used += self.columnWidth(i)
            remaining = max(0, total - used)
            # Respect a minimum width to avoid collapsing after autosize
            min_sel = max(180, self.columnWidth(selected_files_idx))
            self.setColumnWidth(selected_files_idx, max(min_sel, remaining))
        except Exception:
            pass

        # Ensure no stale "Type" column remains (defensive)
        # If any prior state injected it, hide it.
        if "Type" in self._columns:
            type_idx = self._columns.index("Type")
            self.setColumnHidden(type_idx, True)

    def _ensure_folder_item(self, folder_path: str) -> QTreeWidgetItem:
        """Ensure a folder node exists."""
        if folder_path in self._folder_items:
            return self._folder_items[folder_path]

        # Build parent folders recursively
        parent_dir = str(Path(folder_path).parent)
        parent_item = None
        if parent_dir and parent_dir != folder_path:
            if parent_dir in self._folder_items:
                parent_item = self._folder_items[parent_dir]
            else:
                parent_item = self._ensure_folder_item(parent_dir)

        # Create user-friendly folder display name
        folder_name = self._get_display_folder_name(folder_path)
        item = QTreeWidgetItem()
        item.setText(self._columns.index("Selected Files"), folder_name)
        item.setData(self._columns.index("Selected Files"), Qt.ItemDataRole.UserRole, None)  # no file path on folder

        # Set folder icon to distinguish from files
        try:
            # Use system folder icon if available
            folder_icon = self.style().standardIcon(self.style().StandardPixmap.SP_DirIcon)
            item.setIcon(self._columns.index("Selected Files"), folder_icon)
        except Exception:
            pass  # Graceful fallback if icon not available

        if parent_item:
            parent_item.addChild(item)
        else:
            self.addTopLevelItem(item)

        self._folder_items[folder_path] = item
        return item

    def _get_display_folder_name(self, folder_path: str) -> str:
        """Get user-friendly folder display name.

        Implements user preference for showing path relative to home directory (~)
        when possible, otherwise shows the full folder name for context.
        Uses forward slashes for consistent display across platforms.
        """
        try:
            path_obj = Path(folder_path)
            home_path = Path.home()

            # Try to show path relative to home directory
            try:
                relative_path = path_obj.relative_to(home_path)
                return f"~/{relative_path.as_posix()}"
            except ValueError:
                # If not under home directory, show a more contextual path
                # Show last 2 parts of the path for better context
                parts = path_obj.parts
                if len(parts) >= 2:
                    return f"…/{parts[-2]}/{parts[-1]}"
                else:
                    return path_obj.name or str(path_obj)

        except Exception:
            # Fallback to simple folder name
            return Path(folder_path).name or folder_path

    def _add_file_item(self, parent_item: QTreeWidgetItem, fi: FileInfo) -> None:
        """Add a file row under given folder node."""
        it = QTreeWidgetItem(parent_item)
        # Selected Files column (filename)
        filename = Path(fi.path).name
        it.setText(self._columns.index("Selected Files"), filename)
        it.setData(self._columns.index("Selected Files"), Qt.ItemDataRole.UserRole, fi.path)

        # Set file icon to distinguish from folders
        try:
            # Use system file icon if available
            file_icon = self.style().standardIcon(self.style().StandardPixmap.SP_FileIcon)
            it.setIcon(self._columns.index("Selected Files"), file_icon)
        except Exception:
            pass  # Graceful fallback if icon not available
        # File Info must reflect EXACT handler-provided fields without added punctuation or case changes.
        # Use model literal display and DO NOT append any extension or separators.
        file_info_text = fi.file_info_display or "Unknown"
        it.setText(self._columns.index("File Info"), file_info_text)

        # Size - use size_str from FileInfoData
        it.setText(self._columns.index("Size"), fi.size_str or "Unknown")
        # Created - use created from FileInfoData (format if datetime)
        created_text = fi.created.strftime("%d/%m/%Y %H:%M") if fi.created else "Unknown"
        it.setText(self._columns.index("Created"), created_text)
        # Ensure Type column is not used (spec removed). Hide defensively if present.
        if "Type" in self._columns:
            self.setColumnHidden(self._columns.index("Type"), True)

    def _select_file_by_path(self, file_path: str) -> None:
        """Select a file by its path."""
        def walk(item: QTreeWidgetItem):
            fp = item.data(self._columns.index("Selected Files"), Qt.ItemDataRole.UserRole)
            if fp == file_path:
                self.setCurrentItem(item)
                return True
            for i in range(item.childCount()):
                if walk(item.child(i)):
                    return True
            return False

        for i in range(self.topLevelItemCount()):
            if walk(self.topLevelItem(i)):
                break

    def _on_selection_changed(self) -> None:
        """Handle selection change."""
        item = self.currentItem()
        if not item:
            self._model.set_selected_file(None)
            return
        file_path = item.data(self._columns.index("Selected Files"), Qt.ItemDataRole.UserRole)
        if file_path:
            self._model.set_selected_file(file_path)
            self.file_selected.emit(file_path)
        else:
            self._model.set_selected_file(None)

    def _on_item_double_clicked(self, item: QTreeWidgetItem, column: int) -> None:
        """Handle item double click."""
        file_path = item.data(self._columns.index("Selected Files"), Qt.ItemDataRole.UserRole)
        if file_path:
            self.file_double_clicked.emit(file_path)

    def _show_header_context_menu(self, pos: QPoint) -> None:
        """Context menu to toggle optional columns (none at present)."""
        # Type column removed by spec; no header toggles for core columns.
        menu = QMenu(self)
        # No optional columns -> no actions
        # Keep hook for future options
        if menu.actions():
            menu.exec(self.mapToGlobal(pos))

    def get_selected_file_path(self) -> Optional[str]:
        """Get the currently selected file path."""
        return self._model.selected_file

    def _on_column_resized(self, logical_index: int, old_size: int, new_size: int) -> None:
        """Handle column resize to persist user preferences."""
        if logical_index < len(self._columns):
            column_name = self._columns[logical_index]
            # Only persist interactive columns (not stretch or resize-to-contents)
            header = self.header()
            resize_mode = header.sectionResizeMode(logical_index)
            if resize_mode == QHeaderView.ResizeMode.Interactive:
                self._save_column_width(column_name, new_size)
                # If the user resized 'Selected Files', reflow the remaining space politely:
                if column_name == "Selected Files":
                    # Re-run width policy to keep overall layout sane without overriding user width
                    self._apply_column_width_policy()

    def _save_column_width(self, column_name: str, width: int) -> None:
        """Save column width to config."""
        try:
            from ....config.ud_config import ud_config
            key = f"file_tree.column_width.{column_name}"
            ud_config.set_value(key, width)
        except Exception as e:
            # Graceful fallback if config is not available
            pass

    def _restore_column_widths(self) -> None:
        """Restore saved column widths from config."""
        try:
            from ....config.ud_config import ud_config
            for i, column_name in enumerate(self._columns):
                key = f"file_tree.column_width.{column_name}"
                saved_width = ud_config.get_value(key)
                if saved_width is not None:
                    # Only restore for interactive columns
                    header = self.header()
                    if header.sectionResizeMode(i) == QHeaderView.ResizeMode.Interactive:
                        self.setColumnWidth(i, saved_width)
            # After restoring interactive widths, ensure Selected Files still fills leftover space
            try:
                sel_idx = self._columns.index("Selected Files")
                total = self.viewport().width()
                used = 0
                for i in range(len(self._columns)):
                    if i == sel_idx:
                        continue
                    used += self.columnWidth(i)
                remaining = max(0, total - used)
                current = self.columnWidth(sel_idx)
                if remaining > current:
                    self.setColumnWidth(sel_idx, remaining)
            except Exception:
                pass
        except Exception as e:
            # Graceful fallback if config is not available
            pass
