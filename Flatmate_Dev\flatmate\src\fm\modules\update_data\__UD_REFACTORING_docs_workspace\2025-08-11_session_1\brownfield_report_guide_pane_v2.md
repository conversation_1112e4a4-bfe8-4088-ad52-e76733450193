---
description: <PERSON><PERSON> report – Guide Pane V2 (sections/slots, layout, API)
---

# Brownfield Report: Guide Pane V2

Date: 2025-08-11
Scope: `guide_pane_v2.py` and its section/slot components

## Context
- Goal: Make Guide Pane V2 predictable, gap-free, and stupid-simple to maintain.
- Constraints: MVP, presenter Qt-free, explicit section APIs, empty slots must not consume space, preserve existing formatting where relevant, remove irrelevant styling from parent API.
- Files:
  - `.../guide_pane_v2.py`
  - `.../guide_pane_components/source_section.py`
  - `.../guide_pane_components/archive_section.py`
  - `.../guide_pane_components/base_components/section.py`
  - `.../guide_pane_components/base_components/slot_row.py`
  - `.../guide_pane_components/base_components/folder_path_row.py` (present but not wired in v2 code)
  - Docs read: `handover_session_2.md`, `guide_pane_handover_layout.md`, `guide_pane_observations.md`, `handover.md`

## Current Architecture (observed)
- `GuidePaneV2` instantiates two fixed sections: `SourceSection`, `ArchiveSection`. No registries.
- Sections own their content: header, collapsible `info`, body containing `SlotRow` rows.
- `SlotRow` collapses to zero height when hidden (`set_visible(False)`), zero vertical padding, configurable horizontal padding.
- `SourceSection` slots:
  - `message` row (placeholder)
  - `options` row ("File discovery" checkbox + info button). Toggle wired to pane via `connect_enable_toggled`.
- `ArchiveSection` slots:
  - `path` row (single-line label) with `set_path_html`, collapses when empty.
- Path formatting lives in `GuidePaneV2._format_path_for_label()` and is applied to section info labels, and to archive path row when explicitly set via `set_archive_path` or detected in `set_archive_summary`.
- Context menus on source/archive info labels allow “Open in Explorer”.

## What Works
- Explicit fixed sections and slots; no dynamic registry complexity.
- `SlotRow` collapse mechanics look correct and predictable; zero-height when hidden.
- Section `info` collapses fully when blank (no residual gap).
- Path formatting: `~` for home, bold basename, monospace dim parent, tooltip full path with restored casing.
- Per-folder discovery row API is simple; event emission constrained to a single signal (`publish_toggle_auto_queue_requested`).

## Issues / Mismatches vs Intent
- Source placeholder visibility logic exists but may still be inconsistent if caller forgets to pair `set_section_info("source", ...)` with `set_source_slot_message("")`. Risk: empty space or wrong row visible.
- Archive summary vs path row duplication: `set_section_info("archive", ...)` can set summary text while `ArchiveSection` also has a dedicated `path` slot. The pane sometimes displays the path in the summary line instead of the `path` slot (inconsistent with the “sections handle their own layout” principle).
- Residual try/except used to nudge layout (`adjustSize()/updateGeometry()`), which may obscure errors (contrary to fail-fast rule).
- Inter-section spacing not explicitly decided (6px currently) and can contribute to perceived “gaps”.
- `FolderPathRow` exists but is not actually used by `GuidePaneV2`/`ArchiveSection` wiring; currently archive uses a bare QLabel inside a `SlotRow`.

## Root Causes
- Split responsibility for archive display (summary line vs dedicated path slot) invites duplication and drift.
- Parent (`GuidePaneV2`) still performs some presentation choices (where to show a value) that should be encapsulated by the section’s own API.
- Visibility defaults rely on caller discipline rather than internal invariants (e.g., placeholder auto-hide).

## Recommendations (concrete, minimal)
1. Section-owned display rules (single source of truth)
   - Move the decision of where archive text appears entirely into `ArchiveSection`.
   - API:
     - `set_summary(text: str)` for non-path text only
     - `set_path_html(html: str, tooltip: str = "")` for paths
     - `clear_path()` collapses the row
   - In `GuidePaneV2.set_archive_summary(text)`: detect path → call `ArchiveSection.set_path_html(...)` and clear summary; otherwise call `set_summary(text)` and collapse path.

2. Source placeholder invariants
   - On `set_section_info("source", text)`: if a real path is detected, always call `self.section_source.show_message(False)` internally (do not rely on separate `set_source_slot_message("")` from caller).
   - On non-path or empty text: show placeholder only when text is empty; hide placeholder when non-empty explanatory text is provided.

3. Eliminate broad try/except around layout nudges
   - Remove silent `try/except` around `adjustSize()`/`updateGeometry()` in `GuidePaneV2`. If it errors, we should see it.
   - If nudge is still needed, keep calls but without suppressing exceptions.

4. Inter-section spacing decision (explicit)
   - Set `root.setSpacing(0 or 2)` in `GuidePaneV2._build_ui()` per final spec. Current 6 exaggerates vertical gaps.

5. Use `FolderPathRow` or keep consistent `SlotRow`+QLabel
   - Option A (preferred minimal): Keep `ArchiveSection` as is with `_path_row: SlotRow` and `_path_label` but ensure all archive paths flow through `set_path_html(...)` only.
   - Option B: Replace `_path_row` internals with `FolderPathRow` to standardise label formatting wrapper. If you pick B, wire `set_formatter(self._format_path_for_label)` at construction to centralise formatting.

6. API surface (parent)
   - Keep `GuidePaneV2` API minimal, data-driven and section-oriented:
     - `set_section_info(section_id: Literal["source","archive"], text: str)`
     - `set_archive_summary(text: str)`
     - `set_archive_path(path: Optional[str])`
     - `show_source_context_options(monitor_enabled: bool=False, auto_queue_enabled: bool=False, folder: Optional[str]=None)`
     - `set_source_slot_message(text: str)` (still available but no longer required when a real source path is set)
   - All layout/visibility decisions performed by the sections themselves.

## Acceptance Criteria
- Empty slots do not create any visible gaps anywhere in the pane.
- Source:
  - When a path is set → summary shows formatted path; placeholder is hidden automatically.
  - When empty → summary is collapsed; placeholder text is visible.
- Archive:
  - When a path is provided or “Same as Source” with known source → path row shows formatted path; summary line is empty.
  - When non-path text is provided → path row collapsed; summary line shows that text.
- Inter-section spacing set to agreed value (0 or 2) with titles still readable.
- No broad try/except in layout code.

## Risk & Debt
- Dual-path display logic (summary vs row) causes regressions if both are updated from different call sites. Centralising in `ArchiveSection` mitigates this.
- Styling ambiguity (header prominence) remains until explicit spec is provided. We can add a minimal local override or leave as-is.

## Step-by-step Refactor Plan (surgical)
1. `guide_pane_v2.py`
   - In `_build_ui()`: change `root.setSpacing(6)` → `root.setSpacing(2)` (or `0`).
   - In `set_section_info("source", ...)`: ensure placeholder hidden when a path is detected; show only when empty text.
   - In `set_archive_summary(text)`: route all path cases to `ArchiveSection.set_path_html(...)` and clear `info`; for non-path, clear path and set summary.
   - Remove silent try/except around `adjustSize()/updateGeometry()`.

2. `archive_section.py`
   - Keep single responsibility: `set_summary()`, `clear_summary()`, `set_path_html()`, `clear_path()`. Ensure these fully own visibility of `_path_row` and `info`.

3. (Optional) `folder_path_row.py`
   - If adopted, inject formatter from pane: `row.set_formatter(self._format_path_for_label)` and replace `_path_label` with `FolderPathRow` to simplify path display.

## Test Checklist (manual)
- No source path → Source placeholder visible; summary hidden; no gaps.
- Set source path → Source summary shows formatted path; placeholder hidden; archive mirrors when “Same as Source”.
- Set archive to explicit path → Archive summary empty; path row shows formatted path.
- Set archive to non-path summary → Archive path row hidden; summary shows text.
- Toggle per-folder discovery when `folder` is None does not emit; when set, emits with correct folder and state.
- Right-click → Open in Explorer works for both sections when paths exist.

## Notes
- MVP preserved, presenter remains Qt-free.
- Use `from fm.core.services.logger import log` only for targeted diagnostics; otherwise fail fast.
- UK spelling retained throughout.
