"""
Guide Pane Widget for Update Data Module.

State-driven contextual guidance system with event-driven updates
and visual state management.
"""

from typing import Dict, Any, Optional, List
from PySide6.QtCore import Qt, Signal, QObject
from PySide6.QtGui import QFont, QPalette, QTextCharFormat, QTextCursor
from PySide6.QtWidgets import QFrame, QLabel, QVBoxLayout, QTextEdit, QPushButton, QHBoxLayout, QGroupBox, QCheckBox

from fm.gui._shared_components.base.base_pane import BasePane
from fm.core.services.logger import log

class GuidePaneWidget(BasePane):
    """
    State-driven contextual guidance widget.
    
    Manages UI state transitions and provides contextual messaging
    based on user actions and system state changes.
    """
    
    # Signals
    message_changed = Signal(str)
    publish_toggle_folder_monitoring_requested = Signal(bool)  # Signal to toggle folder monitoring
    publish_toggle_auto_queue_requested = Signal(str, bool)  # Emit (folder_path, enabled)
    
    # Message templates for different states
    MESSAGE_TEMPLATES = {
        'initial': "Select a source folder or files to begin.",
        'folder_selected': "Found {count} CSV files ready for processing",
        'files_selected': "Selected {count} files for processing",
        'archive_same': "Files will be moved to 'Archive' subfolder in source location",
        'archive_custom': "Choose where to create 'Archive' folder for processed files",
        'ready': "Ready to process {count} files",
        'processing': "Processing file {current} of {total}...",
        'success': "Successfully processed {count} files",
        'error': "Error processing {filename}: {error}",
        'warning': "No compatible files found in selected location"
    }
    
    # Visual state colors
    STATE_COLORS = {
        'inactive': '#888888',
        'info': '#DDDDDD',       # neutral, readable on dark bg
        'warning': '#FFB74D',
        'error': '#EF5350',
        'processing': '#64B5F6',
        'success': '#81C784'
    }

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_state = 'initial'
        self.context_data = {}
        self._current_folder: Optional[str] = None
        self._setup_ui()
        self._setup_event_handlers()

    def _setup_ui(self):
        """Set up the UI components with tighter styling."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(2, 2, 2, 2)  # Guide pane owns its external padding (match file view)

        # Guide frame with dynamic styling
        self.guide_frame = QFrame()
        # Use a subtle, theme-consistent border via stylesheet
        self.guide_frame.setFrameShape(QFrame.Shape.NoFrame)
        self.guide_frame.setObjectName("guide_frame")
        self.guide_frame.setStyleSheet(
            """
            QFrame#guide_frame {
                /* Explicit green, thin border without blocky background */
                border: 1px solid #4CAF50;
                border-radius: 6px;
                background-color: transparent;
            }
            """
        )
        self._update_frame_style('inactive')

        frame_layout = QVBoxLayout(self.guide_frame)
        frame_layout.setContentsMargins(2, 2, 2, 2)  # Minimal inner padding, avoid beveled look
        frame_layout.setSpacing(2)

        # Compact status label (replaces QTextEdit)
        self.message_display = QLabel("")
        self.message_display.setObjectName("guide_message")
        self.message_display.setWordWrap(True)
        self.message_display.setStyleSheet(
            "QLabel#guide_message { border: none; color: #cccccc; padding: 0px; margin: 0px; }"
        )
        self.message_display.setVisible(False)

        # Persistent archive summary line
        self.archive_summary_label = QLabel("")
        self.archive_summary_label.setObjectName("archive_summary")
        self.archive_summary_label.setStyleSheet("color: #9ec89f;")
        self.archive_summary_label.setVisible(False)  # avoid empty blocky area by default
        
        # Options container for interactive elements
        self.options_container = QGroupBox("Options")
        self.options_container.setObjectName("options_container")
        self.options_container.setStyleSheet(
            """
            /* Flatten the options group for a cleaner, less crowded look */
            QGroupBox#options_container {
                border: none;
                margin-top: 4px;  /* small gap to avoid crowded feel */
                padding-top: 0px;
                padding-bottom: 0px;
            }
            QGroupBox#options_container::title {
                subcontrol-origin: margin;
                left: 0px;
                padding: 0 0px;
                color: #cfcfcf;
                font-weight: 600;
            }
            /* Subtle green theming for checkbox indicator */
            QGroupBox#options_container QCheckBox {
                spacing: 6px;
            }
            QGroupBox#options_container QCheckBox::indicator {
                width: 14px; height: 14px;
                border: 1px solid rgba(129,199,132,0.6);
                background: transparent;
                margin-right: 6px;
            }
            QGroupBox#options_container QCheckBox::indicator:checked {
                background: rgba(129,199,132,0.25);
                border: 1px solid rgba(129,199,132,0.9);
            }
            """
        )
        self.options_container.setVisible(False)
        
        options_layout = QVBoxLayout(self.options_container)
        self.options_layout = options_layout
        self.options_layout.setContentsMargins(4, 2, 4, 4)  # tighter, less crowding
        self.options_layout.setSpacing(2)
        
        # Order: status -> options -> archive summary (keeps discovery close to source info)
        frame_layout.addWidget(self.message_display)
        frame_layout.addWidget(self.options_container)
        frame_layout.addWidget(self.archive_summary_label)
        layout.addWidget(self.guide_frame)

    def _setup_event_handlers(self):
        """Set up signal connections for state changes."""
        self.message_changed.connect(self._on_message_changed)

    def _update_frame_style(self, state: str):
        """Update frame styling with a consistent thin green border (no background/padding)."""
        # Keep border always green for a steady visual identity; states affect text, not frame
        self.guide_frame.setStyleSheet(
            """
            QFrame#guide_frame {
                border: 1px solid #4CAF50;
                border-radius: 6px;
                background-color: transparent;
            }
            """
        )

    def _on_message_changed(self, message: str):
        """Handle message change events."""
        # Could trigger additional UI updates here
        pass

    def set_state(self, state: str, context: Optional[Dict[str, Any]] = None):
        """
        Set the current state and update message accordingly.
        
        Args:
            state: The new state identifier
            context: Additional context data for message formatting
        """
        self.current_state = state
        self.context_data = context or {}
        
        if state in self.MESSAGE_TEMPLATES:
            template = self.MESSAGE_TEMPLATES[state]
            message = template.format(**self.context_data)
            self.display(message)
            
        # Update visual styling based on state
        self._update_frame_style(state)

    def display(self, message: str, state: str = 'info', format_type: str = 'plain'):
        """Display a compact status message with state-based styling."""
        text = message or ""
        if format_type == 'html':
            self.message_display.setTextFormat(Qt.TextFormat.RichText)
            self.message_display.setText(text)
        else:
            self.message_display.setTextFormat(Qt.TextFormat.PlainText)
            self.message_display.setText(text)

        self.message_display.setVisible(bool(text.strip()))
        self.message_changed.emit(text)

        # Update colour/weight based on state
        color = self.STATE_COLORS.get(state, '#cccccc')
        weight = 'bold' if state in ['error', 'warning'] else 'normal'
        self.message_display.setStyleSheet(
            f"QLabel#guide_message {{ border: none; color: {color}; font-weight: {weight}; padding: 0px; margin: 0px; }}"
        )

        # Update frame styling
        self._update_frame_style(state)

    # ============================
    # Convenience API
    # ============================

    def set_archive_summary(self, text: str):
        """Set the persistent archive summary line without affecting the main message."""
        self.archive_summary_label.setText(text or "")

    def set_status(self, state: str, **context):
        """Set status by state key using templates, e.g., 'ready', 'processing'."""
        self.set_state(state, context)

    def show_source_context_options(self, monitor_enabled: bool = False, auto_queue_enabled: bool = False, folder: Optional[str] = None):
        """Show contextual options relevant to source handling (single auto-queue option).

        Args:
            monitor_enabled: Reserved (not used in MVP)
            auto_queue_enabled: Initial checkbox state
            folder: The resolved folder path for this context (required for emitting toggle payload)
        """
        self.clear_options()
        self._current_folder = folder
        # Single compact row: checkbox + inline info button
        row = QHBoxLayout()
        checkbox = QCheckBox("Enable file discovery")
        checkbox.setChecked(auto_queue_enabled)
        checkbox.setStyleSheet("QCheckBox { color: #dddddd; }")
        # Wire directly to the published auto-queue toggle intent
        def _emit_toggle(state: int):
            enabled = bool(state)
            if self._current_folder:
                self.publish_toggle_auto_queue_requested.emit(self._current_folder, enabled)
            else:
                # Defensive: in MVP this pane is only shown when a single folder is resolved
                log.warning("[GuidePane] Toggle emitted without a folder context; ignoring")
        checkbox.stateChanged.connect(_emit_toggle)

        info_btn = QPushButton("i")
        info_btn.setFlat(True)
        info_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        info_btn.setToolTip("Automatically discover files in the selected folder.\nThis preference is saved per folder.")
        info_btn.setStyleSheet("QPushButton { color: #cccccc; background: transparent; border: none; font-weight: bold; }"
                               "QPushButton:hover { color: #ffffff; }")

        row.addWidget(checkbox)
        row.addWidget(info_btn)
        row.addStretch(1)

        # Add the row layout to the options container
        self.options_layout.addLayout(row)
        has_children = self.options_layout.count() > 0
        self.options_container.setVisible(has_children)
        log.debug(f"[GuidePane] show_source_context_options -> auto_queue_enabled={auto_queue_enabled}, folder={self._current_folder}, has_children={has_children}")

    def update_context(self, **kwargs):
        """Update context data and refresh current message."""
        self.context_data.update(kwargs)
        if self.current_state in self.MESSAGE_TEMPLATES:
            self.set_state(self.current_state, self.context_data)

    def show_processing_progress(self, current: int, total: int):
        """Show processing progress with formatted message."""
        self.set_state('processing', {'current': current, 'total': total})

    def show_success_summary(self, count: int):
        """Show success summary with file count and options."""
        self.set_state('success', {'count': count})
        self.show_options([
            {"text": "View Results", "action": "view_results"},
            {"text": "Process More Files", "action": "reset"}
        ])

    def show_error_details(self, filename: str, error: str):
        """Show error details with filename and error message."""
        self.set_state('error', {'filename': filename, 'error': error})

    def reset_to_initial(self):
        """Reset to initial state with enhanced welcome."""
        welcome_message = """<h3>Welcome to Update Data</h3>
        <p>This module helps you process and update your data files.</p>
        <p><b>Steps:</b></p>
        <ol>
            <li>Select your source files or folder</li>
            <li>Choose a save location</li>
            <li>Click Process to begin</li>
        </ol>"""
        self.display(welcome_message, 'info', 'html')
        self.clear_options()
        self.set_state('initial')
        
    def show_folder_monitoring_option(self, enabled: bool = False):
        """Deprecated: use single auto-queue option. Kept for compatibility."""
        # Delegate to the unified option, treating monitoring as auto-queue
        self.show_source_context_options(auto_queue_enabled=enabled)

    def get_current_context(self) -> Dict[str, Any]:
        """Get current context data for external use."""
        return self.context_data.copy()

    def is_ready_for_processing(self) -> bool:
        """Check if guide indicates ready state."""
        return self.current_state == 'ready'

    def get_state(self) -> str:
        """Get current state identifier."""
        return self.current_state

    def show_options(self, options: List[Dict[str, str]]):
        """Display interactive options."""
        self.clear_options()
        for option in options:
            button = QPushButton(option['text'])
            button.clicked.connect(
                lambda checked, action=option['action']: self._on_option_selected(action)
            )
            self.options_layout.addWidget(button)
        self.options_container.setVisible(True)

    def clear_options(self):
        """Clear all option buttons."""
        def _clear_layout(layout):
            while layout.count():
                item = layout.takeAt(0)
                w = item.widget()
                l = item.layout()
                if w is not None:
                    w.deleteLater()
                elif l is not None:
                    _clear_layout(l)
                    # delete the child layout object by removing its parent
                    # letting Qt GC handle it when unreferenced
        _clear_layout(self.options_layout)
        self.options_container.setVisible(False)

    def _on_option_selected(self, action: str):
        """Handle option selection."""
        # Emit signal or call appropriate handler
        self.message_changed.emit(f"option_selected:{action}")

    def add_checkbox_option(self, label: str, checked: bool = False, key: str = None):
        """Add a checkbox option."""
        checkbox = QCheckBox(label)
        checkbox.setChecked(checked)
        # Ensure visibility against dark background
        checkbox.setStyleSheet("QCheckBox { color: #dddddd; }")
        if key:
            checkbox.stateChanged.connect(
                lambda state, k=key: self._on_checkbox_changed(k, state)
            )
        self.options_layout.addWidget(checkbox)
        # Do not force visibility here; caller decides based on children

    def _on_checkbox_changed(self, key: str, state: int):
        """Handle checkbox state changes."""
        self.context_data[key] = bool(state)
        self.message_changed.emit(f"checkbox_changed:{key}:{state}")
        
        # Special handling for folder monitoring toggle
        if key == "monitor_folder":
            enabled = bool(state)
            log.debug(f"Guide pane folder monitoring toggle: {enabled}")
            self.publish_toggle_folder_monitoring_requested.emit(enabled)
        elif key == "auto_queue":
            enabled = bool(state)
            log.debug(f"Guide pane auto-queue toggle: {enabled}")
            # Emit the folder-aware payload expected by downstream handlers
            if getattr(self, "_current_folder", None):
                self.publish_toggle_auto_queue_requested.emit(self._current_folder, enabled)
            else:
                # Defensive: without a folder context we cannot persist per-folder preference
                log.warning("[GuidePane] Auto-queue toggle emitted without a folder context; ignoring")
