"""
File Discovery Manager

Persist per-folder file discovery preference and reconcile FolderMonitorService.

MVP: two methods only.
"""
from __future__ import annotations

from typing import Protocol

from fm.core.services.logger import log


class _ConfigProto(Protocol):
    def set_discovery_enabled(self, folder: str, enabled: bool) -> None: ...
    def is_discovery_enabled(self, folder: str) -> bool: ...


class _FolderMonitorProto(Protocol):
    def set_folder_monitored(self, folder: str, enabled: bool) -> None: ...


class FileDiscoveryManager:
    def __init__(self, config: _ConfigProto, folder_monitor_service: _FolderMonitorProto) -> None:
        self._config = config
        self._monitor = folder_monitor_service

    # ---------------------------------------------------------------------
    # API
    # ---------------------------------------------------------------------
    def set_discovery(self, folder: str, enabled: bool) -> None:
        if not folder:
            raise ValueError("folder must be a non-empty path")
        # Persist first (single source of truth)
        self._config.set_discovery_enabled(folder, bool(enabled))
        log.debug(f"[FileDiscoveryManager] Persisted discovery: folder='{folder}', enabled={enabled}")
        # Reconcile monitoring to match preference
        try:
            self._monitor.set_folder_monitored(folder, bool(enabled))
            log.debug(f"[FileDiscoveryManager] Reconciled monitor for '{folder}' => {enabled}")
        except Exception as e:
            # Loud but non-fatal to UI flow; state persists even if monitoring fails
            log.error(f"[FileDiscoveryManager] Failed to set monitor for '{folder}': {e}")

    def get_discovery(self, folder: str) -> bool:
        if not folder:
            return False
        try:
            val = bool(self._config.is_discovery_enabled(folder))
            log.debug(f"[FileDiscoveryManager] Read discovery: folder='{folder}', enabled={val}")
            return val
        except Exception as e:
            log.error(f"[FileDiscoveryManager] Error reading discovery for '{folder}': {e}")
            return False

# Back-compat alias until callers are updated; remove after refactor completes
AutoQueueManager = FileDiscoveryManager
