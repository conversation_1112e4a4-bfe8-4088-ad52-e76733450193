import os
import sys
from pathlib import Path
from typing import List, Optional, Set

from PySide6.QtWidgets import QFileDialog
from ....config.ud_keys import UpdateDataKeys as ud_keys
from ....config.ud_config import ud_config as config
from fm.core.services.logger import log
from fm.core.directory.utils.extensions import normalise_ext, parse_supported_extensions
from fm.core.directory.utils.file_scan import scan_root_files

# Default directory if no last used directory is available
default_dir = os.path.expanduser('~')


class FileSelector:
    """Main interface for file selection operations."""

    @staticmethod
    def get_file_paths(title: str = "Select File(s)", parent=None, start_dir: Optional[str] = None) -> List[str]:
        """Get multiple file paths using a file dialog."""
        log.debug(f"File selection requested: title={title}")
        dialog = FileDialogue(title=title, parent=parent, start_dir=start_dir)
        return dialog.selected_files

    @staticmethod
    def get_folder_path(title: str = "Select Folder", parent=None, start_dir: Optional[str] = None) -> str:
        """Get a single folder path using a folder dialog."""
        log.debug(f"Folder selection requested: title={title}")
        dialog = FolderDialogue(title=title, parent=parent, start_dir=start_dir)
        return dialog.selected_folder

    @staticmethod
    def discover_files(folder_path: str) -> List[str]:
        """Discover supported files in a given folder path."""
        if not folder_path:
            return []
        return FileUtils.discover_files_in_folder(folder_path)

    @staticmethod
    def get_paths(selection_type: str, initial_dir: str = None, **kwargs) -> List[str]:
        """
        Unified method for getting file paths from either file or folder selection.

        Args:
            selection_type: Either 'files' or 'folder'
            initial_dir: Starting directory for the dialog
            **kwargs: Additional options for the dialog (title, parent)

        Returns:
            List[str]: List of file paths

        Raises:
            ValueError: If selection_type is not 'files' or 'folder'
        """
        if selection_type == 'files':
            return FileSelector.get_file_paths(
                title=kwargs.get('title', 'Select Files'),
                parent=kwargs.get('parent'),
                start_dir=initial_dir
            )
        elif selection_type == 'folder':
            folder_path = FileSelector.get_folder_path(
                title=kwargs.get('title', 'Select Folder'),
                parent=kwargs.get('parent'),
                start_dir=initial_dir
            )
            if folder_path:
                return FileUtils.discover_files_in_folder(folder_path)
            return []
        else:
            raise ValueError(f"Unknown selection type: {selection_type}")


class FolderDialogue:
    """Dialog for selecting a folder with platform-specific handling."""
    
    def __init__(self, title: str = "Select Folder", parent=None, start_dir: Optional[str] = None):
        """Initialize the folder selection dialog.
        
        Args:
            title: Dialog window title
            parent: Parent widget
            start_dir: Starting directory (uses last used dir if None)
        """
        self.selected_folder = ""
        
        try:
            initial_dir = start_dir or FileUtils.get_last_used_dir()
            
            # Validate initial directory
            if not os.path.isdir(initial_dir):
                log.warning(f"Initial directory invalid: {initial_dir}, using default")
                initial_dir = default_dir

            if sys.platform == 'win32':
                # On Windows, the native folder dialog is poor. Use the file dialog workaround.
                instructive_title = f"{title} (select any file in the desired folder)"
                dialog = FileDialogue(title=instructive_title, parent=parent, start_dir=initial_dir)
                if dialog.selected_files:
                    self.selected_folder = os.path.dirname(dialog.selected_files[0])
            else:
                # On macOS and Linux, use the native folder dialog.
                folder = QFileDialog.getExistingDirectory(
                    parent=parent,
                    caption=title,
                    dir=initial_dir
                )
                self.selected_folder = folder

            if self.selected_folder:
                # Validate selected folder before saving
                if os.path.isdir(self.selected_folder) and os.access(self.selected_folder, os.R_OK):
                    FileUtils.set_last_used_dir(self.selected_folder)
                    log.debug(f"Selected folder: {self.selected_folder}")
                else:
                    log.warning(f"Selected folder invalid or inaccessible: {self.selected_folder}")
                    self.selected_folder = ""
        except Exception as e:
            log.error(f"Error in folder selection dialog: {e}")
            self.selected_folder = ""


class FileDialogue:
    """Dialog for selecting one or more files."""
    
    def __init__(self, title: str = "Select File(s)", parent=None, start_dir: Optional[str] = None):
        """Initialize the file selection dialog.
        
        Args:
            title: Dialog window title
            parent: Parent widget
            start_dir: Starting directory (uses last used dir if None)
        """
        self.selected_files = []
        
        try:
            initial_dir = start_dir or FileUtils.get_last_used_dir()
            
            # Validate initial directory
            if not os.path.isdir(initial_dir):
                log.warning(f"Initial directory invalid: {initial_dir}, using default")
                initial_dir = default_dir
            
            supported_types = FileUtils._get_supported_file_types()
            filter_string = f"Supported Files ({' '.join(['*.' + ext for ext in supported_types])});;All Files (*)"

            files, _ = QFileDialog.getOpenFileNames(
                parent=parent,
                caption=title,
                dir=initial_dir,
                filter=filter_string
            )

            if files:
                # Validate selected files exist and are accessible
                valid_files = []
                for file_path in files:
                    if os.path.isfile(file_path) and os.access(file_path, os.R_OK):
                        valid_files.append(file_path)
                    else:
                        log.warning(f"Selected file invalid or inaccessible: {file_path}")
                
                if valid_files:
                    # Save the directory of the first valid selected file
                    FileUtils.set_last_used_dir(os.path.dirname(valid_files[0]))
                    self.selected_files = valid_files
                    log.debug(f"Selected {len(valid_files)} file(s): {valid_files[0]}{' and more' if len(valid_files) > 1 else ''}")
                else:
                    log.warning("No valid files selected")
        except Exception as e:
            log.error(f"Error in file selection dialog: {e}")
            self.selected_files = []


class FileUtils:
    """Utility class for file operations and configuration."""
    
    @staticmethod
    def _normalise_extension(filename: str) -> str:
        """Return the file extension in lower case, without the leading dot."""
        return normalise_ext(filename)
    
    @staticmethod
    def _get_supported_file_types() -> List[str]:
        """Get and parse supported file types from config, ensuring correct format."""
        # Default to a safe, known list
        default_types = ['csv', 'ofx', 'pdf']
        supported_from_config = config.get_value(ud_keys.Files.SUPPORTED, default=default_types)
        return parse_supported_extensions(supported_from_config, default=default_types)

    @staticmethod
    def get_last_used_dir() -> str:
        """Return the last used directory from config, or the user's home dir."""
        try:
            last_dir = config.get_value(ud_keys.Paths.LAST_SOURCE_DIR, default=default_dir)
            # Validate directory exists and is accessible
            if last_dir and os.path.isdir(last_dir) and os.access(last_dir, os.R_OK):
                return last_dir
            else:
                log.warning(f"Last used directory invalid or inaccessible: {last_dir}, using default")
                return default_dir
        except Exception as e:
            log.error(f"Error retrieving last used directory: {e}, using default")
            return default_dir

    @staticmethod
    def set_last_used_dir(directory: str) -> None:
        """Save the last used directory to config with validation."""
        try:
            if not directory:
                log.warning("Attempted to set empty directory path")
                return
            
            if not os.path.isdir(directory):
                log.warning(f"Directory does not exist: {directory}")
                return
            
            if not os.access(directory, os.R_OK):
                log.warning(f"Directory not accessible: {directory}")
                return
            
            config.set_value(ud_keys.Paths.LAST_SOURCE_DIR, directory)
            log.debug(f"Last used directory updated: {directory}")
        except Exception as e:
            log.error(f"Error setting last used directory: {e}")

    @staticmethod
    def discover_files_in_folder(folder_path: str) -> List[str]:
        """Discover supported files in the selected folder.
        
        Args:
            folder_path: Path to the folder to scan for files
            
        Returns:
            List of paths to supported files found in the folder
        """
        if not folder_path or not os.path.isdir(folder_path):
            log.debug(f"Invalid folder path: {folder_path}")
            return []

        folder = Path(folder_path)
        # Use a set for efficient O(1) average time complexity lookups
        supported_exts_set: Set[str] = set(ext.lstrip('.') for ext in FileUtils._get_supported_file_types())

        # Delegate scanning to core utility (non-recursive)
        discovered_paths = scan_root_files(folder, supported_exts_set)
        discovered_files = [str(p) for p in discovered_paths]
        
        log.debug(f"Discovered {len(discovered_files)} supported files in {folder_path}")
        return discovered_files
