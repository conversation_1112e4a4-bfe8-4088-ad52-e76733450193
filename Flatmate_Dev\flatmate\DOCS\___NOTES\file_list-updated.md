# file_list-updated

# 
SHOuld the 
FIle list updated event 

class methods
files added (file_paths)
files removed(file_paths)
clear all


another small tweak to the file_view file tree  although columns are currently resable and this persists .. i'm not sure how rational this is - I think should be remain resazable per isntance, but should fit to content on each run 

I now think we could possibly lose the grandfather in the file tree 