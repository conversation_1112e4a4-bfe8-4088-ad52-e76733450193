# Update Data – Guide Pane Refactor Handover (Session 2)

Date: 2025-08-11
Owner: <PERSON>
Scope: Guide Pane layout/visibility refactor and reusable slot components

## Summary
- Implemented `FolderPathRow` (`_ui/_view/center_panel_components/guide_pane_components/folder_path_row.py`) to display archive folder paths.
- Refactored `GuidePaneV2` to use `FolderPathRow` for the Archive folder slot.
- Standardised value-driven visibility via `SlotRow.set_visible(...)`.
- `Section` info label now fully collapses when empty (zero height), preventing stray gaps.
- Preserved existing path formatting and tooltips. Rendering uses QLabel’s built-in rich text subset (as before). No QSS changes.

## Files Touched
- `guide_pane_components/slot_row.py`
  - Earlier: zero vertical padding; configurable horizontal padding; unified `set_visible()`.
- `guide_pane_components/section.py`
  - NEW: Info label collapses to zero height when empty; natural size when non-empty.
- `guide_pane_components/folder_path_row.py` (NEW)
  - Encapsulates folder path display using `KeyValueRow("Folder:")`.
  - API: `set_path(str)`, `clear_path()`, `has_path()`, optional `set_formatter(callable)`.
- `guide_pane_components/key_value_row.py`
  - Added `set_value_html(...)` to support existing path formatting output (keeps behaviour unchanged).
- `guide_pane_v2.py`
  - Integrated `FolderPathRow` for Archive.
  - Archive folder row visibility is driven by content, not mode.
  - Source placeholder row shown only when Source path is empty; hidden when a valid path is set.
  - No broad try/except; fail-fast kept.

## Behavioural Guarantees
- Empty rows collapse to zero height (no layout gaps).
- Inter-row spacing is owned by `Section`; `SlotRow` controls only horizontal padding.
- Archive folder path now lives in its own row; summary line is empty when a real path is shown (so no duplication).
- Path formatting and tooltip are unchanged from prior behaviour.

## What to Verify (Smoke Test)
- Source
  - When Source is empty: placeholder row is visible; section info is empty/collapsed.
  - When Source is a valid path: placeholder row hidden; section info shows formatted path with tooltip.
- Archive
  - When Archive is a path (or Same as Source with known source): `FolderPathRow` visible with correct formatting; archive info line empty/collapsed.
  - When Archive is non-path text: folder row hidden; archive info shows that text.
- Layout
  - No stray spacing when rows/labels are hidden; sections resize naturally.

## Notes on Formatting
- We preserved existing formatting logic (QLabel rich text subset: bold for basename, dim monospace parent). QSS remains authoritative for styling. No web stack, no stylesheet changes.

## Architectural Alignment
- MVP preserved. Presenter remains Qt-free.
- View exposes explicit, minimal interface methods; events reserved for multi-listener or async flows (per agreed rules).

## Next Steps (if needed)
- Consider moving the path formatting to pure plain text + segmented labels if we decide to avoid rich text entirely (not urgent; would be more code).
- Add small unit tests for path casing restoration.
- Quick UX pass on `Section` spacing defaults if design tweaks are desired.

## Run/Check
- From workspace root: `cd flatmate` then activate: `venv` alias, then run: `fm` (as usual).
- Open Update Data and sanity-check Source/Archive behaviour per above.

## Known Constraints
- `set_archive_mode(...)` kept for API compatibility; visibility for the archive folder row is content-driven for predictability.
