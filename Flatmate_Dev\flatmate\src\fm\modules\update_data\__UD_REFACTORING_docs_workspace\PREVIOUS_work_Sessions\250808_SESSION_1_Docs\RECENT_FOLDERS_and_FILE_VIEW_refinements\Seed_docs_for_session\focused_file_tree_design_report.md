# Focused File Tree: Show Only File, Its Parent, and Grandparent

Purpose
- Implement a focused file tree display that shows:
  - The selected file
  - Its parent directory
  - The grandparent directory (as the root shown in the tree)
- Keep current UX (icons, columns, signals) and integrate with Update Data file view models.

Current Context
- Widget in use: [`python.class FileTree(QTreeWidget)`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/file_tree.py:25)
- Data model: [`python.class FileViewModel`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/models.py:14)
- Legacy reference: [`python.class FileDisplayWidget(QWidget)`](flatmate/src/fm/modules/update_data/file_browser_original.py.bak:19)

Key Observations
- Current FileTree builds a full hierarchical view by grouping files via [`python.def group_files_by_directory(...)`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/file_tree.py:19), recursively ensuring parent folders with [`python.def _ensure_folder_item(self, folder_path)`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/file_tree.py:124).
- It already supports recursion from arbitrary folders up to top-level through parent resolution. This can be constrained to only two ancestor levels for a “focused” tree.

Design Options

1) Constrain the existing QTreeWidget population logic (recommended)
- Concept:
  - Keep QTreeWidget, but modify the data building to only include the necessary chain(s):
    - For each file in model.files, compute:
      - file_path
      - parent_dir = dirname(file_path)
      - grandparent_dir = dirname(parent_dir)
    - Only create nodes for: grandparent_dir → parent_dir → file
  - Optionally, deduplicate if multiple target files share the same parent/grandparent.
- Pros:
  - Minimal change footprint, retains all UI (columns, icons, context menu, signals).
  - No dependency on QFileSystemModel; purely view-model driven as today.
- Cons:
  - If model.files contains many files from different trees, you will still build multiple 3-level chains (acceptable if that’s intended).

Implementation Plan
- Add a “focused” mode toggle and target depth to FileTree (config-driven).
- Populate tree using the constrained ancestry set instead of full group-by.
- Maintain existing columns, icons, selection, and signals.

Proposed API
- FileConfig extension (optional): a boolean flag `focused_tree_enabled` and an enum/depth config.
- FileTree additions:
  - [`python.def set_focused_mode(self, enabled: bool, depth: int = 2)`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/file_tree.py:1)
  - [`python.def refresh_data(self)`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/file_tree.py:100) updated to branch between full vs focused builds.
  - Focused build helper:
    - [`python.def _refresh_data_focused(self)`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/file_tree.py:1)

Focused Build Algorithm
- Inputs: model.files (list of FileInfo), depth=2 (file, parent, grandparent).
- Steps:
  1) Compute chain for each file:
     - p0 = file
     - p1 = parent(p0)
     - p2 = parent(p1)
  2) Track only these directories p1 and p2 (skip above p2).
  3) Create or reuse folder items strictly for p2 then p1 under p2.
  4) Add the file item under p1.
  5) Expand all; if model.selected_file is set, select it and ensure it’s visible.

Pseudocode Sketch
- To be inserted into FileTree with minimal disruption.

[`python.def _refresh_data_focused(self)`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/file_tree.py:1)
    self.clear()
    self._folder_items.clear()
    files = self._model.files
    if not files:
        return

    def gp_chain(path: str, depth: int = 2):
        from pathlib import Path
        p = Path(path)
        chain = [p]
        for _ in range(depth):
            p = p.parent
            chain.append(p)
        return [str(x) for x in chain]  # [file, parent, grandparent]

    # Build a minimal tree across all files
    for fi in files:
        file_path, parent_dir, grandparent_dir = gp_chain(fi.path, depth=2)
        # Ensure grandparent node (top-level)
        gp_item = self._ensure_specific_folder_item(grandparent_dir, allow_recurse=False)
        # Ensure parent under grandparent
        parent_item = self._ensure_specific_folder_item(parent_dir, parent_override=gp_item, allow_recurse=False)
        # Add only this file under parent
        self._add_file_item(parent_item, fi)

    self.expandAll()
    if self._model.selected_file:
        self._select_file_by_path(self._model.selected_file)

[`python.def _ensure_specific_folder_item(self, folder_path: str, parent_override: Optional[QTreeWidgetItem] = None, allow_recurse: bool = False)`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/file_tree.py:1)
    if folder_path in self._folder_items:
        return self._folder_items[folder_path]

    # Optionally block recursion unless explicitly requested
    parent_item = parent_override
    if parent_item is None and allow_recurse:
        parent_dir = str(Path(folder_path).parent)
        if parent_dir and parent_dir != folder_path:
            parent_item = self._ensure_specific_folder_item(parent_dir, None, allow_recurse=True)

    item = QTreeWidgetItem()
    item.setText(self._columns.index("Name"), self._get_display_folder_name(folder_path))
    try:
        item.setIcon(self._columns.index("Name"), self.style().standardIcon(self.style().StandardPixmap.SP_DirIcon))
    except Exception:
        pass

    if parent_item:
        parent_item.addChild(item)
    else:
        self.addTopLevelItem(item)

    self._folder_items[folder_path] = item
    return item

Column Width Control in Focused Mode (QTreeWidget)
- Focused mode does not change column APIs; you retain full control via QTreeWidget/QHeaderView.
- Current behavior in [`python.def _setup_tree(self)`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/file_tree.py:45):
  - Name: Interactive + initial width set via setColumnWidth
  - Size: ResizeToContents
  - File Info: Interactive + initial width set via setColumnWidth
  - Created: ResizeToContents
  - Type (optional): ResizeToContents
- Controls available:
  - header.setSectionResizeMode(col, QHeaderView.ResizeMode.[Interactive|ResizeToContents|Stretch|Fixed])
  - self.setColumnWidth(col, px), header.resizeSection(col, px), header.setMinimumSectionSize(px)
  - Persist widths: connect header.sectionResized and store per-column size in ud_config/QSettings; restore on init/refresh
  - Contextual widths in focused mode: tighten Name, widen File Info due to shallower tree and elided folder labels from [`python.def _get_display_folder_name`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/file_tree.py:160)

Suggested width policy helper
- Add a helper and call it after tree population (end of refresh_data and end of _refresh_data_focused):

[`python.def _apply_column_width_policy(self)`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/file_tree.py:1)
    header = self.header()
    header.setMinimumSectionSize(60)
    name_idx = self._columns.index("Name")
    size_idx = self._columns.index("Size")
    info_idx = self._columns.index("File Info")
    created_idx = self._columns.index("Created")

    # Keep crisp autosizing for size and created
    header.setSectionResizeMode(size_idx, QHeaderView.ResizeMode.ResizeToContents)
    header.setSectionResizeMode(created_idx, QHeaderView.ResizeMode.ResizeToContents)

    # Deterministic interactive sizing for main text columns
    header.setSectionResizeMode(name_idx, QHeaderView.ResizeMode.Interactive)
    header.setSectionResizeMode(info_idx, QHeaderView.ResizeMode.Interactive)

    if self._focused_mode:
        # Focused: tighter name, wider info
        self.setColumnWidth(name_idx, 180)
        self.setColumnWidth(info_idx, 240)
    else:
        # Defaults similar to _setup_tree
        self.setColumnWidth(name_idx, 200)
        self.setColumnWidth(info_idx, 180)

    if "Type" in self._columns:
        type_idx = self._columns.index("Type")
        header.setSectionResizeMode(type_idx, QHeaderView.ResizeMode.ResizeToContents)

Call sites
- End of [`python.def refresh_data(self)`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/file_tree.py:100):
      self._apply_column_width_policy()
- End of [`python.def _refresh_data_focused(self, depth: int = 2)`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/file_tree.py:1):
      self._apply_column_width_policy()

Persisting user-resized widths (optional)
- Connect header.sectionResized to capture sizes:
  - [`python.def _connect_signals(self)`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/file_tree.py:95)
        self.header().sectionResized.connect(self._on_column_resized)
  - [`python.def _on_column_resized(self, logicalIndex: int, oldSize: int, newSize: int)`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/file_tree.py:1)
        # Persist to ud_config under a key per column name
        # e.g., ud_config.set_value(f"ud.file_tree.col_width.{self._columns[logicalIndex]}", newSize)
- On init/refresh, restore sizes before applying policy; policy should only set widths if not previously saved.

Responsive adjustment (optional)
- Override resizeEvent to nudge Name/File Info when viewport width changes, keeping min sizes and leaving Size/Created as autosized; avoid Stretch for predictability.

Configuration Considerations
- Where to toggle:
  - Add a flag to FileConfig (if present in Update Data config) to enable focused mode for the UD file panel.
  - Or drive mode based on source selection type: when selecting specific files, show focused; when selecting a folder, show full.
- Depth constraint:
  - Default to 2 for file, parent, grandparent.
  - Make configurable to 1 (file + parent only) or 3 if ever needed.

Integration Points
- UD Presenter/State:
  - The selection and recent folder features live in [`python.class FileConfigManager`](flatmate/src/fm/modules/update_data/_ui/_presenter/file_config_manager.py:31). No change needed here beyond optionally setting a UI flag when selection type is “files”.
- View:
  - If there is a higher-level UD view that constructs FileTree, add a setter for focused mode there.

Alternative Options

2) Proxy-based filter with QFileSystemModel
- Replace QTreeWidget with QTreeView + QFileSystemModel and use a QSortFilterProxyModel to whitelist only the ancestor chain of the selected file.
- Pros:
  - Native FS model with automatic updates.
- Cons:
  - Requires replacing current QTreeWidget and custom columns; harder to keep the enriched FileInfo display and multi-column formatting. Larger refactor.

3) Hybrid: Keep QTreeWidget but feed it a “sparse” view-model
- Build a small in-memory tree model that only contains grandparent → parent → file chains for all selected files and render it via QTreeWidget as now.
- This is effectively what Option 1 does, so Option 1 already covers this with minimal churn.

4) Reuse older simple implementation structure
- The legacy [`python.class FileDisplayWidget`](flatmate/src/fm/modules/update_data/file_browser_original.py.bak:19) already does recursive folder ensuring, but shows the full path from a chosen source. You could adapt its approach to stop at grandparent.
- However, the current FileTree adds richer columns (File Info, Created, Type), so copying the legacy widget would regress capability. Better to adapt FileTree as per Option 1.

Recommendations
- Adopt Option 1. It requires the least refactoring, preserves current UI behavior, and is easy to toggle.
- Add a small utility to compute parent/grandparent chain and reuse the existing folder icon/name logic.
- Gate the focused view behind a feature flag so you can A/B quickly.

Proposed Minimal Patch Points
- In [`python.def refresh_data(self)`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/file_tree.py:100):
  - Branch on a new boolean: self._focused_mode.
- Add:
  - self._focused_mode = False in __init__.
  - [`python.def set_focused_mode(self, enabled: bool)`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/file_tree.py:1)
  - New helpers from the pseudocode above.

Testing Checklist
- With multiple files from same parent:
  - Ensure only a single grandparent and parent are created; multiple files appear under the same parent node.
- With files from different parents sharing a grandparent:
  - Two parent nodes under the same grandparent.
- With files from different grandparent trees:
  - Multiple top-level grandparent nodes.
- Selection restore logic:
  - When selected_file set in model, ensure it selects the file item correctly.
- Column widths, icons, and signals remain unchanged.

Future Enhancements
- Expand-to-chain only:
  - Expand only the chain of the current selection; collapse other chains for focus.
- Toggle at runtime:
  - Expose a toolbar/menu action to switch focused vs full view without rebuilding the whole pane model.

Appendix: Where to place changes
- All changes can be localized to:
  - [`python.class FileTree`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/file_tree.py:25)
- Optional: Add a config flag in FileConfig if you want persistence.

Summary
- Keep QTreeWidget, implement a constrained population strategy that builds only grandparent → parent → file chains for each file in the model. This preserves UI and minimizes risk while delivering the focused display requirement.