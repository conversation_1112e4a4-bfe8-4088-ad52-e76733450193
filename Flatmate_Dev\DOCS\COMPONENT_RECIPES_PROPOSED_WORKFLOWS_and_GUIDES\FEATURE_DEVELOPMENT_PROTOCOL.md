# The Feature Development Protocol

This protocol is the standard operating procedure for implementing new features or undertaking significant refactoring in the Flatmate project. Following these steps ensures that all work is predictable, documented, and of high quality, minimising regressions and rework.

---

## The Six Steps of Feature Development

### 1. Define (The 'What' and 'Why')

Before writing any code, clearly define the feature.

-   **Feature Description:** Write a concise, one-paragraph summary of the feature.
-   **Problem Statement:** Describe the user problem this feature solves.
-   **Success Criteria:** List 3-5 bullet points that define what a successful implementation looks like.

*Output: A new section in the feature's design document or a new task in the project tracker.*

### 2. Plan (The 'How')

Outline the technical implementation.

-   **Affected Components:** List all files and modules that will be created or modified.
-   **Implementation Steps:** Write a high-level, step-by-step plan of the coding work required.
-   **Identify Recipes:** If building a new component, identify the relevant **Component Recipe** from the Project Bible to use.

*Output: A technical plan, either in a design document or as a checklist in a task.*

### 3. Design (The 'Look and Feel')

For any feature with a user interface, describe the visual design and user interaction.

-   **UI Components:** List the new widgets or UI elements required.
-   **User Interaction Flow:** Describe how the user will interact with the feature, step by step.
-   **Visual Mockups (Optional):** For complex UIs, a simple wireframe or mockup is recommended.

*Output: A UI/UX design specification.*

### 4. Document (The 'Rules')

Update the project's documentation *before* or *during* implementation.

-   **Update Project Bible:** If the feature introduces a new architectural pattern, component, or rule, add it to the Project Bible.
-   **Create/Update Recipes:** If a new reusable component pattern is created, document it as a new **Component Recipe**.

*Output: Updated markdown files in the project's `docs` directory or the `PROJECT_BIBLE.md`.*

### 5. Implement (The 'Code')

Write the code, following the plan and documentation precisely.

-   **Follow the Plan:** Adhere to the implementation steps defined in Step 2.
-   **Adhere to Patterns:** Use the specified Component Recipes and follow all rules in the Project Bible.
-   **Write Clean Code:** Ensure code is readable, well-commented, and follows project conventions.

*Output: The implemented feature in the codebase.*

### 6. Test (The 'Proof')

Verify that the feature works as expected and has not introduced any regressions.

-   **Unit/Integration Tests:** Write new tests that cover the feature's functionality.
-   **Run All Tests:** Execute the full test suite using the established **Testing Protocol** to check for regressions.
-   **Manual Test:** Perform a manual run-through of the "Golden Path" user flow for the new feature.

*Output: Passing tests and a confirmed, working feature.*
