import os
import shutil
from datetime import datetime

DEST_ROOT = r"C:\Users\<USER>\_DEV\__PROJECTS\Z_Archive"

import fnmatch

def find_z_archives(search_root):
    for dirpath, dirnames, _ in os.walk(search_root):
        for dirname in dirnames:
            if fnmatch.fnmatch(dirname, '*z_archive*'):
                yield os.path.join(dirpath, dirname)

def main():
    search_root = input("Enter the directory to search for z_archive folders: ").strip()
    search_root = os.path.expanduser(os.path.expandvars(search_root))
    search_root = os.path.abspath(search_root)
    if not os.path.isdir(search_root):
        print(f"Directory does not exist: {search_root}")
        return
    os.makedirs(DEST_ROOT, exist_ok=True)
    for archive in find_z_archives(search_root):
        parent = os.path.basename(os.path.dirname(archive))
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        dest_base = os.path.join(DEST_ROOT, f"{parent}_{timestamp}")
        dest = dest_base
        counter = 1
        while os.path.exists(dest):
            dest = f"{dest_base}_{counter}"
            counter += 1
        shutil.copytree(archive, dest)
        print(f"Copied {archive} -> {dest}")
        # Remove all contents of the original archive folder
        for filename in os.listdir(archive):
            file_path = os.path.join(archive, filename)
            try:
                if os.path.isfile(file_path) or os.path.islink(file_path):
                    os.unlink(file_path)
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)
            except Exception as e:
                print(f"Failed to delete {file_path}: {e}")

if __name__ == "__main__":
    main()
