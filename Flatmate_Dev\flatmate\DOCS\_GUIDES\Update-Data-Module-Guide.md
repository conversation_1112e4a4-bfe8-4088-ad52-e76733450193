# Update Data Module - Developer Onboarding Guide

Last Updated: Sunday, August 3, 2025 @ 9:07:00 PM
Module Version: Event-First MVP (Post-Processing & File-Selection Refactor)
Target Audience: AI Agents & Developers

Overview

The Update Data module handles financial statement processing and database updates. It has been refactored from a monolithic presenter into a decomposed, event-first MVP architecture with specialized managers and a local event bus. The View owns all Qt wiring and connects directly to sub-widget Qt signals; View does not emit new events from sub-widgets themselves — instead, the View translates those Qt signals into module-level intents on the Local Event Bus. Presenter/Managers coordinate actions and emit typed state events; the View renders from state events and responds to dialog-request events.

Architecture Summary

Core Pattern: Event-First MVP (Decomposed)
- Model: Database services, processing pipeline
- View: Component-based UI with interface abstraction, translates Qt signals to local bus intents, renders from typed events
- Presenter: Thin coordinator + specialized managers, subscribes to intents, emits typed state events via managers

Key Principles
- View-only Qt wiring: All Qt connections are owned by the View; the View subscribes to sub-widget Qt signals and translates them to module intents. Sub-widgets do not call interface methods.
- Event-first intents: User actions are translated by the View into Local Event Bus intents; managers coordinate outcomes.
- Typed state events: Managers publish typed dataclass events; View renders state and shows dialogs on dialog-request events.
- Single rendering path: View renders from canonical state events (e.g., FileListUpdatedEvent).

Directory Structure

update_data/
├── ud_presenter.py                         # Main coordinator
├── _ui/
│   ├── ud_view.py                          # View implementation + bus wiring
│   ├── interface/                          # IUpdateDataView protocol
│   ├── _presenter/                         # Specialized managers (Presenter layer)
│   │   ├── state_coordinator.py            # Consolidated state + UI sync
│   │   ├── file_management.py              # File/folder selection + save location
│   │   ├── file_list_manager.py            # Canonical file list
│   │   └── processing_manager.py           # Processing events/lifecycle
│   └── _view/                              # UI components
│       └── shared_components/
│           └── file_selector.py            # Unified file selection API
├── services/
│   ├── events.py                           # Global/cross-module events
│   ├── local_event_bus.py                  # Local bus + ViewEvents
│   └── file_info_service.py                # File enrichment
├── config/
│   ├── ud_config.py                        # Config wrapper
│   ├── ud_keys.py                          # Keys
│   └── defaults.yaml                       # Defaults
└── pipeline/
    ├── dw_director.py
    └── dw_pipeline.py

Presenter/Managers Architecture (Decomposed)

1) UpdateDataPresenter (Coordinator)
Purpose: Orchestrate managers; subscribe to local intents; bridge to global events.
Responsibilities:
- Instantiate managers with DI (view, state manager, file list manager, local bus)
- Subscribe to local bus intents and delegate to managers
- Bridge select events to global bus where appropriate

Critical Notes:
- Presenter never opens dialogs directly for runtime processing; it can route source selection intents to FileManager which may open dialogs via the unified FileSelector.
- Presenter may call limited view interface methods for initial morphing only (text/enablement handoffs are now event-driven by ProcessingStarted/Completed).

2) State Coordinator (Consolidated State + UI Sync)
Purpose: Centralized state management; compute can_process; synchronize state to View.
Example state (abridged):
- Source: source_type, selected_files, selected_folder
- Destination: save_option, save_location, update_database
- Processing: processing, can_process, process_button_text
- UI: status_message
Key method: sync_state_to_view()

3) FileManager (File/Folder Selection, Save Location)
Purpose: Handle file/folder dialog selection, enrich info, update canonical file list via FileListManager, manage "same as source" save logic.
Key flows:
- handle_source_select(selection_type): routes to _select_files() or _select_folder()
- add-files channel: subscribes to add_files_requested (from UDFileView) and opens only files dialog via _select_files()
- _process_selected_files(): enriches, delegates to FileListManager, updates state, emits ViewEvents.SOURCE_DISCOVERED

Important: Managers (including FileManager) are allowed to call view interface methods where appropriate for dialogs, as part of intent handling.

4) FileListManager (Canonical File List)
Purpose: Owns the canonical list; handles add/remove; emits FileListUpdatedEvent; integrates folder monitoring where applicable.

5) ProcessingManager (Processing Lifecycle)
Purpose: Validate and run processing; emit typed lifecycle events:
- ViewEvents.PROCESSING_STARTED
- ViewEvents.PROCESSING_COMPLETED
- DialogRequest events: ERROR_DIALOG_REQUESTED, SUCCESS_DIALOG_REQUESTED

View Architecture

Interface Abstraction (IUpdateDataView)
Pattern: Protocol-like interface. Presenter depends on the interface, not concrete View.

View Responsibilities
- Own all Qt wiring: Connect directly to sub-widget Qt signals in the View.
- Translate sub-widget Qt signals into Local Event Bus intents (no direct interface calls from sub-widgets).
- Subscribe to typed state events and render UI.
- Subscribe to dialog-request events and show OS dialogs (error/success) accordingly.
- Do NOT open file/folder dialogs from sub-widgets directly; these are coordinated by managers in response to intents.

Event System

Local Event Bus (Transport Only)
- Enumerates local keys in ViewEvents
- Carries intents from View and state/dialog events from Managers
- Does not redefine canonical dataclasses or global event names

Typed UI Events (Dataclasses)
- FileListUpdatedEvent: canonical list changes
- ProcessingStartedEvent/ProcessingCompletedEvent: lifecycle
- DialogRequestEvent (Error/Success): user feedback

Global Event Bus Integration
- For cross-module comms; local bus bridges selectively where needed

Canonical Intents and Channels

Left Panel (View element, event-first only)
- The View connects to Left Panel Qt signals and translates them to SOURCE_SELECT_REQUESTED with value:
  - SELECT_FILES
  - SELECT_FOLDER
- Intent consumer: Presenter/Managers route to FileManager which opens dialogs via unified FileSelector.

File Pane (UDFileView “Add files” button)
- The View connects to UDFileView.add_files_requested Qt signal and translates it to add_files_requested (distinct channel).
- Consumer: FileManager subscribes and calls _select_files()
- Rationale: Avoids collision with SOURCE_SELECT_REQUESTED and keeps sub-widget decoupled.

Processing and Dialogs
- Processing: Presenter delegates to ProcessingManager; ProcessingManager emits PROCESSING_STARTED/COMPLETED; View updates enablement/state based on these.
- Dialogs: Managers emit ERROR_DIALOG_REQUESTED/SUCCESS_DIALOG_REQUESTED events with typed payloads; View shows dialogs accordingly. Presenter must not show dialogs directly.

Unified File Selection API

Location: _ui/_view/shared_components/file_selector.py
Usage in FileManager:
def _select_files(self):
    last_dir = ud_config.get_value(ud_keys.Paths.LAST_SOURCE_DIR, default=str(Path.home()))
    file_paths = FileSelector.get_paths(
        selection_type='files',
        initial_dir=last_dir,
        title="Select CSV Files to Process",
        parent=self.view
    )
    if file_paths:
        self._process_selected_files(file_paths, 'files')

def _select_folder(self):
    last_dir = ud_config.get_value(ud_keys.Paths.LAST_SOURCE_DIR, default=str(Path.home()))
    file_paths = FileSelector.get_paths(
        selection_type='folder',
        initial_dir=last_dir,
        title="Select Folder Containing CSV Files",
        parent=self.view
    )
    if file_paths:
        folder_path = os.path.dirname(file_paths[0])
        ud_config.set_value(ud_keys.Paths.LAST_SOURCE_DIR, folder_path)
        self._process_selected_files(file_paths, 'folder', folder_path)

Verification Checklist

- Left Panel “Select files/folder”:
  - View behavior: Connects to Left Panel Qt signals and translates to SOURCE_SELECT_REQUESTED with SELECT_FILES/SELECT_FOLDER (no direct interface calls from the View).
  - Path: Presenter/Managers → FileManager → dialog via FileSelector
  - Result: Exactly one dialog; canonical FileListUpdatedEvent emitted once

- File Pane “Add files”:
  - View behavior: Connects to UDFileView.add_files_requested Qt signal and translates to add_files_requested local intent.
  - Path: FileManager subscriber → _select_files → _process_selected_files
  - Result: Exactly one dialog; canonical FileListUpdatedEvent emitted once

- Processing:
  - PROCESS_REQUESTED intent triggers ProcessingManager
  - View sees PROCESSING_STARTED/COMPLETED and updates enablement
  - Errors/success emitted as dialog-request events; View shows QMessageBox

Recent Improvements (August 2025)

- Event-first intent handling with strict View ownership of Qt wiring
- Distinct add_files_requested channel to prevent dialog collisions
- Canonicalized FileListUpdatedEvent for single rendering path
- Presenters/managers no longer open user feedback dialogs; dialog requests are event-driven
- Normalized option payloads to enums/strings as needed for compatibility

Protocols and Rules (Authoritative)

1) View Rules
- Connect directly to sub-widget Qt signals in the View.
- Never open file/folder dialogs directly from sub-widgets.
- Translate Qt signals to Local Event Bus intents and render from typed events.
- Show user feedback dialogs only in response to dialog-request events.

2) Presenter Rules
- Subscribe to intent channels and delegate to managers.
- Allowed to call limited interface methods for initial morphing only; runtime processing enablement/text is driven by events.

3) Manager Rules
- Handle source selection and add-files intents; may open file/folder dialogs via FileSelector.
- Publish typed state events (e.g., FileListUpdatedEvent, ProcessingStarted/Completed).
- Emit dialog-request events for user feedback.

4) Event Channels (Local)
- SOURCE_SELECT_REQUESTED: "SELECT_FILES" | "SELECT_FOLDER" (from Left Panel Qt signals via View)
- add_files_requested: File Pane add files (from UDFileView Qt signal via View)
- FILE_LIST_UPDATED: FileListUpdatedEvent
- PROCESSING_STARTED | PROCESSING_COMPLETED
- ERROR_DIALOG_REQUESTED | SUCCESS_DIALOG_REQUESTED
- FILE_REMOVED and other module-specific events as applicable

Development Guidelines

Adding New Features
1. Identify responsibility: choose the correct manager.
2. Extend state in State Coordinator if needed.
3. Add/subscribe to local intent channels in View/Manager.
4. Ensure single rendering path via typed events.
5. Add documentation for new channels/events and update this guide.

Testing Strategy
- Unit tests for managers
- Integration tests for intent → manager → event flows
- UI verification for View rendering on typed events

Architecture Status

Current Implementation
- State Coordinator: Centralized state + sync
- FileManager: File selection flows with unified API
- FileListManager: Canonical file list + events
- ProcessingManager: Lifecycle + dialog-request events

Known Follow-Ups
- Remove legacy/unused interface methods/events
- Continue migrating ad-hoc calls to typed events where applicable
- Strengthen DI for services

Appendix A: Quick Reference

Key Files
- Presenter: ud_presenter.py
- View: _ui/ud_view.py
- Managers: _ui/_presenter/*.py
- Events: services/local_event_bus.py, _ui/ui_events.py
- File selection: _ui/_view/shared_components/file_selector.py

Key Local Channels
- SOURCE_SELECT_REQUESTED
- add_files_requested
- FILE_LIST_UPDATED
- PROCESSING_STARTED / PROCESSING_COMPLETED
- ERROR_DIALOG_REQUESTED / SUCCESS_DIALOG_REQUESTED

Planned Document: Update Data UI Deep-Dive (Onboarding)
- Purpose: In-depth onboarding for Update Data UI
- Contents:
  - Detailed event topology diagrams (intents, state, dialog-request flows)
  - Left Panel vs File Pane behaviors with sequence diagrams
  - State model and enablement rules
  - Extensibility patterns and examples
- Status: To be created next; this guide serves as the high-level foundation.

Status: Functional, event-first MVP alignment complete for selection and processing
Next Phase: Documentation deep-dive; remove dead code/legacy pathways; expand tests around intent→event pipelines
