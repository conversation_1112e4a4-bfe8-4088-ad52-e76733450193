# File Selection System Migration Guide

**Author:** <PERSON> (Product Manager)  
**Date:** 2025-08-03  
**Status:** DRAFT  
**Target Audience:** Development Team

---

## 1. Migration Overview

This guide outlines the step-by-step process for migrating the current fragmented file selection system to the new unified architecture. The migration focuses on maintaining a clean MVP pattern while simplifying the file selection API.

### 1.1 Migration Goals

- Eliminate circular dependencies between components
- Establish a single entry point for file selection
- Ensure proper separation between UI and business logic
- Maintain backward compatibility during transition
- Improve testability and maintainability

### 1.2 Success Criteria

- Zero application crashes during file selection operations
- Clear component responsibilities with no MVP violations
- Complete unit test coverage for the new file selection logic
- Simplified codebase with reduced technical debt

## 2. Technical Migration Path

### 2.1 Phase 1: FileSelector Refactoring

| Step | Description | Acceptance Criteria |
|------|-------------|---------------------|
| 1.1 | Create new `get_paths(selection_type: str)` method in `FileSelector` | Method returns `List[str]` of file paths based on selection type |
| 1.2 | Refactor internal methods to support the new unified API | Internal methods properly handle both file and folder selection |
| 1.3 | Add comprehensive error handling | All error cases are caught and reported clearly |
| 1.4 | Add unit tests for the new method | 90%+ test coverage for the new method |

**Implementation Notes:**
```python
@staticmethod
def get_paths(selection_type: str, initial_dir: str = None, **kwargs) -> List[str]:
    """
    Unified method for getting file paths from either file or folder selection.
    
    Args:
        selection_type: Either 'files' or 'folder'
        initial_dir: Starting directory for the dialog
        **kwargs: Additional options for the dialog
        
    Returns:
        List[str]: List of file paths
    """
    if selection_type == 'files':
        return FileSelector._get_files(initial_dir, **kwargs)
    elif selection_type == 'folder':
        return FileSelector._get_files_from_folder(initial_dir, **kwargs)
    else:
        raise ValueError(f"Unknown selection type: {selection_type}")
```

### 2.2 Phase 2: FileManager Updates

| Step | Description | Acceptance Criteria |
|------|-------------|---------------------|
| 2.1 | Update `_select_files()` to use new `FileSelector.get_paths()` | Method uses new API and handles errors properly |
| 2.2 | Update `_select_folder()` to use new `FileSelector.get_paths()` | Method uses new API and handles errors properly |
| 2.3 | Ensure proper file info enrichment | All files are enriched with metadata before passing to View |
| 2.4 | Fix event emission parameters | Events use correct parameter names and types |

**Implementation Notes:**
```python
def _select_files(self):
    """Select individual files using file dialog."""
    try:
        # Get last used directory from config
        last_dir = ud_config.get_value(ud_keys.Paths.LAST_SOURCE_DIR, default=str(Path.home()))
        
        # Use the new unified API
        file_paths = FileSelector.get_paths(
            selection_type='files',
            initial_dir=last_dir,
            title="Select CSV Files to Process"
        )
        
        if file_paths:
            # Process the selected files
            self._process_selected_files(file_paths, source_type='files')
    except Exception as e:
        log.error(f"[FILE_MANAGER] Error selecting files: {e}")
        self.view.show_error(f"Error selecting files: {str(e)}")
```

### 2.3 Phase 3: UpdateDataView Simplification

| Step | Description | Acceptance Criteria |
|------|-------------|---------------------|
| 3.1 | Remove file discovery logic from View | View only displays data, doesn't fetch it |
| 3.2 | Implement passive data display pattern | View accepts `List[FileInfo]` objects for display |
| 3.3 | Update interface methods | Interface methods clearly define data passing contract |
| 3.4 | Remove direct Qt widget access | No direct widget access from Presenter |

**Implementation Notes:**
```python
# In IUpdateDataView interface
def display_files(self, file_info_list: List[FileInfo]) -> None:
    """
    Display the list of files in the UI.
    
    Args:
        file_info_list: List of FileInfo objects to display
    """
    pass

# In UpdateDataView implementation
def display_files(self, file_info_list: List[FileInfo]) -> None:
    """
    Display the list of files in the UI.
    
    Args:
        file_info_list: List of FileInfo objects to display
    """
    self.center_display.file_table.clear()
    for file_info in file_info_list:
        self.center_display.file_table.add_file(file_info)
```

## 3. Testing Strategy

### 3.1 Unit Testing

| Component | Test Focus | Test Cases |
|-----------|------------|------------|
| `FileSelector` | `get_paths()` method | Valid file selection, valid folder selection, error cases |
| `FileManager` | File processing logic | Proper enrichment, event emission, error handling |
| `UpdateDataView` | Display logic | Correct rendering of file info objects |

### 3.2 Integration Testing

- Test the complete file selection flow from UI action to display
- Verify proper event propagation between components
- Ensure no circular dependencies or MVP violations

### 3.3 Manual Testing Scenarios

1. **Basic File Selection:**
   - Select individual CSV files
   - Verify files appear in the file table
   - Check file metadata is correct

2. **Folder Selection:**
   - Select folder with CSV files
   - Verify all files are discovered
   - Check sorting and filtering works

3. **Error Handling:**
   - Select empty folder
   - Select folder with no CSV files
   - Cancel selection dialog

## 4. Rollout Plan

### 4.1 Deployment Phases

1. **Phase 1: Developer Preview**
   - Implement changes in a feature branch
   - Run comprehensive tests
   - Get developer feedback

2. **Phase 2: Limited User Testing**
   - Deploy to test environment
   - Conduct user acceptance testing
   - Gather feedback on usability

3. **Phase 3: Full Rollout**
   - Merge to main branch
   - Deploy to production
   - Monitor for issues

### 4.2 Rollback Plan

In case of critical issues:

1. Revert to previous version of file selection components
2. Apply emergency fixes for any critical bugs
3. Re-test and redeploy when stable

## 5. Documentation Updates

### 5.1 Code Documentation

- Update docstrings for all modified methods
- Add architecture overview comments
- Document the data flow between components

### 5.2 Developer Documentation

- Update architecture diagrams
- Document the new file selection API
- Provide examples of proper usage

## 6. Timeline and Resources

### 6.1 Estimated Timeline

| Phase | Duration | Dependencies |
|-------|----------|--------------|
| FileSelector Refactoring | 2 days | None |
| FileManager Updates | 2 days | FileSelector Refactoring |
| UpdateDataView Simplification | 3 days | FileManager Updates |
| Testing | 2 days | All implementation phases |
| Documentation | 1 day | All implementation phases |

### 6.2 Resource Requirements

- 1 Senior Developer for architecture and implementation
- 1 QA Engineer for testing
- 1 Technical Writer for documentation

## 7. Post-Migration Validation

### 7.1 Code Quality Metrics

- Reduced cyclomatic complexity
- Decreased coupling between components
- Increased test coverage

### 7.2 Performance Metrics

- File selection response time
- Memory usage during file operations
- UI responsiveness during file discovery

### 7.3 User Experience Validation

- User satisfaction surveys
- Support ticket volume related to file selection
- Feature usage analytics
