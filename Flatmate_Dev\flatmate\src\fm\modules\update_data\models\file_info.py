"""
Unified file information model for Update Data module.

This module provides the FileInfoData dataclass that bridges the gap between
FileInfoService output and UI display needs, consolidating all file-related
data structures into a single, consistent model.
"""

from dataclasses import dataclass
from typing import Optional
from datetime import datetime
from pathlib import Path


@dataclass
class FileInfoData:
    """
    Unified file information model bridging FileInfoService and UI needs.
    
    This dataclass consolidates file information from multiple sources:
    - FileInfoService provides: path, bank_type, format_type, handler, size_bytes, size_str
    - UI needs: modified, created, is_valid, is_processed for display and state management
    
    Attributes:
        path: Full file path
        bank_type: Bank name from handler detection (exact from handler)
        format_type: Format variant from handler (exact from handler)
        file_type: File type from handler (exact from handler, e.g., "csv")
        handler: Handler class name if detected, None otherwise
        size_bytes: File size in bytes
        size_str: Human-readable size string (e.g., "1.2 MB")
        modified: File modification timestamp
        created: File creation timestamp
        is_valid: Whether file is valid for processing
        is_processed: Whether file has been processed
    """
    # From FileInfoService (all as-passed from handlers without normalization)
    path: str
    bank_type: str
    format_type: str
    file_type: Optional[str]
    handler: Optional[str]
    size_bytes: int
    size_str: str
    
    # For UI compatibility and state management
    modified: Optional[datetime] = None
    created: Optional[datetime] = None
    is_valid: bool = True
    is_processed: bool = False
    
    @property
    def name(self) -> str:
        """Get the filename without path."""
        return Path(self.path).name
    
    @property
    def size_formatted(self) -> str:
        """Get human-readable file size (uses FileInfoService formatting)."""
        return self.size_str
    
    @property
    def file_info_display(self) -> str:
        """
        EXACT literal statement type display: "bank_name variant file_type" from handlers.
        - Uses handler-defined values with their exact case and spelling.
        - If any part is missing/unknown, omit it.
        - If all unknown, fall back to "<ext> file" (lower-case ext from filename).
        """
        ext = Path(self.path).suffix.lower().lstrip(".")

        def _clean(s: Optional[str]) -> str:
            if not s:
                return ""
            # Normalize separators
            s = s.replace(" | ", " ").replace("|", " ")
            for dash in (" – ", " — ", "–", "—"):
                s = s.replace(dash, " ")
            # Collapse whitespace
            s = " ".join(s.split())
            return s

        # Use values exactly as declared by handlers; only trim whitespace
        bank = (self.bank_type or "").strip()
        fmt = (self.format_type or "").strip()
        ftype = (self.file_type or "").strip()

        parts = []
        if bank and bank != "Unknown":
            parts.append(bank)
        if fmt and fmt != "Unrecognized":
            parts.append(fmt)
        if ftype:
            parts.append(ftype)

        if parts:
            return " ".join(parts)

        # Fallback when nothing recognized
        return f"{ext} file" if ext else "unknown file"
    
    @property
    def created_formatted(self) -> str:
        """NZ-style two-digit year and 12-hour time with am/pm: DD/MM/YY hh:mm am/pm"""
        dt = self.created or self.modified
        if dt:
            try:
                return dt.strftime("%d/%m/%y %I:%M %p").lower()
            except Exception:
                return ""
        return ""
    
    @classmethod
    def from_service_data(cls, service_data: dict, **kwargs):
        """
        Create FileInfoData from FileInfoService.get_file_info() output.
        
        Args:
            service_data: Dictionary from FileInfoService containing file info
            **kwargs: Additional fields to set (modified, created, is_valid, etc.)
            
        Returns:
            FileInfoData instance with service data and any additional fields
        """
        # Prefer explicit kwargs if provided, otherwise fall back to service_data
        created = kwargs.pop('created', service_data.get('created'))
        modified = kwargs.pop('modified', service_data.get('modified'))

        return cls(
            path=service_data['path'],
            bank_type=service_data['bank_type'],
            format_type=service_data['format_type'],
            file_type=service_data.get('file_type'),
            handler=service_data['handler'],
            size_bytes=service_data['size_bytes'],
            size_str=service_data['size_str'],
            created=created,
            modified=modified,
            **kwargs
        )
    
    def to_dict(self) -> dict:
        """Convert to dictionary for serialization or compatibility."""
        return {
            'path': self.path,
            'bank_type': self.bank_type,
            'format_type': self.format_type,
            'file_type': self.file_type,
            'handler': self.handler,
            'size_bytes': self.size_bytes,
            'size_str': self.size_str,
            'modified': self.modified,
            'created': self.created,
            'is_valid': self.is_valid,
            'is_processed': self.is_processed,
            'name': self.name,
            'size_formatted': self.size_formatted,
            'file_info_display': self.file_info_display,
            'created_formatted': self.created_formatted
        }
