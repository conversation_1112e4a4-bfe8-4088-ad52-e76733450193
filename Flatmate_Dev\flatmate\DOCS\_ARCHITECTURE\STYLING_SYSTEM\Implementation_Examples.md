# QProxyStyle Implementation Examples

**Date**: 2025-01-30  
**Purpose**: Concrete code examples for QProxyStyle migration  
**Target**: Development team implementing the migration

## Complete Implementation Examples

### 1. Core FlatmateStyle Class

```python
# flatmate/src/fm/gui/styles/flatmate_style.py
from PySide6.QtWidgets import QProxyStyle, QStyleOption, QStyle
from PySide6.QtCore import QRect, Qt
from PySide6.QtGui import QPainter, QPalette, QColor, QBrush, QPen
from ...core.config import config
from ...core.config.keys import ConfigKeys

class FlatmateStyle(QProxyStyle):
    """Custom style for Flatmate application replacing QSS system."""
    
    def __init__(self, base_style=None):
        super().__init__(base_style)
        self._colors = self._load_color_palette()
        self._font_size = config.get_value(ConfigKeys.App.BASE_FONT_SIZE, 14)
        self._current_theme = 'dark'
    
    def _load_color_palette(self) -> dict:
        """Load color palette matching current QSS colors."""
        return {
            # Primary colors (from palette.qss)
            'primary': QColor('#3B8A45'),
            'primary_hover': QColor('#4BA357'),
            'primary_pressed': QColor('#2E6E37'),
            
            # Secondary colors
            'secondary': QColor('#3B7443'),
            'secondary_hover': QColor('#488E52'),
            'secondary_pressed': QColor('#2E5A35'),
            
            # Background colors
            'bg_dark': QColor('#1E1E1E'),
            'nav_bg': QColor('#1a382f'),  # Dark green nav background
            'panel_bg': QColor('#242424'),
            'file_display_bg': QColor('#202020'),
            
            # Text colors
            'text_primary': QColor('#FFFFFF'),
            'text_secondary': QColor('#B0B0B0'),
            'calm_white': QColor('#CCCCCC'),
            
            # UI elements
            'border': QColor('#333333'),
            'accent': QColor('#4A90E2'),
            'error': QColor('#E74C3C'),
            'success': QColor('#2ECC71'),
        }
    
    def drawPrimitive(self, element, option, painter, widget=None):
        """Override primitive drawing for custom appearance."""
        if element == QStyle.PE_PanelButtonCommand:
            self._draw_button(option, painter, widget)
        elif element == QStyle.PE_PanelItemViewItem:
            self._draw_tree_item(option, painter, widget)
        elif element == QStyle.PE_Frame and widget and widget.objectName() == 'file_tree':
            self._draw_file_tree_frame(option, painter, widget)
        else:
            super().drawPrimitive(element, option, painter, widget)
    
    def drawControl(self, element, option, painter, widget=None):
        """Override control drawing for panels and complex widgets."""
        if element == QStyle.CE_ToolBar:
            self._draw_toolbar(option, painter, widget)
        elif widget and widget.objectName() in ['left_panel', 'right_panel', 'right_side_bar']:
            self._draw_navigation_panel(option, painter, widget)
        else:
            super().drawControl(element, option, painter, widget)
    
    def _draw_button(self, option, painter, widget):
        """Draw custom button styling matching QSS theme."""
        rect = option.rect
        
        # Check for button type attribute
        button_type = None
        if widget and hasattr(widget, 'property'):
            button_type = widget.property('type')
        
        # Determine colors based on button type and state
        if button_type == 'primary':
            base_color = self._colors['primary']
            hover_color = self._colors['primary_hover']
            pressed_color = self._colors['primary_pressed']
        elif button_type == 'secondary':
            base_color = self._colors['secondary']
            hover_color = self._colors['secondary_hover']
            pressed_color = self._colors['secondary_pressed']
        else:
            base_color = self._colors['panel_bg']
            hover_color = self._colors['secondary']
            pressed_color = self._colors['secondary_pressed']
        
        # Select color based on state
        if option.state & QStyle.State_Pressed:
            color = pressed_color
        elif option.state & QStyle.State_MouseOver:
            color = hover_color
        else:
            color = base_color
        
        # Draw button background with rounded corners
        painter.setRenderHint(QPainter.Antialiasing)
        painter.setBrush(QBrush(color))
        painter.setPen(Qt.NoPen)
        painter.drawRoundedRect(rect, 6, 6)
        
        # Draw border for non-primary buttons
        if button_type != 'primary':
            painter.setPen(QPen(self._colors['border'], 1))
            painter.setBrush(Qt.NoBrush)
            painter.drawRoundedRect(rect, 6, 6)
    
    def _draw_navigation_panel(self, option, painter, widget):
        """Draw navigation panel with custom styling."""
        rect = option.rect
        painter.fillRect(rect, self._colors['nav_bg'])
        
        # Add subtle right border for left panel
        if widget and widget.objectName() == 'left_panel':
            painter.setPen(self._colors['border'])
            painter.drawLine(rect.topRight(), rect.bottomRight())
    
    def _draw_toolbar(self, option, painter, widget):
        """Draw toolbar with custom styling."""
        rect = option.rect
        
        # Check if this is the TableViewToolbar
        if widget and widget.objectName() == 'TableViewToolbar':
            # Fill background
            painter.fillRect(rect, self._colors['bg_dark'])
            
            # Draw border matching QSS: border: 1px solid #2A5A3A; border-radius: 4px;
            painter.setRenderHint(QPainter.Antialiasing)
            painter.setPen(QPen(QColor('#2A5A3A'), 1))
            painter.setBrush(Qt.NoBrush)
            painter.drawRoundedRect(rect.adjusted(0, 0, -1, -1), 4, 4)
        else:
            # Default toolbar styling
            painter.fillRect(rect, self._colors['bg_dark'])
            painter.setPen(self._colors['border'])
            painter.drawRect(rect.adjusted(0, 0, -1, -1))
    
    def _draw_tree_item(self, option, painter, widget):
        """Draw file tree item with custom styling."""
        rect = option.rect
        
        # Selection highlighting
        if option.state & QStyle.State_Selected:
            painter.fillRect(rect, self._colors['secondary'])  # #3B7443 Green selection
        elif option.state & QStyle.State_MouseOver:
            painter.fillRect(rect, QColor('#242424'))  # Hover color
        
        # Alternating row colors
        if hasattr(option, 'features') and option.features & QStyleOption.Alternate:
            painter.fillRect(rect, QColor('#242424'))  # Subtle alternate
    
    def _draw_file_tree_frame(self, option, painter, widget):
        """Draw file tree frame matching QSS styling."""
        rect = option.rect
        
        # Fill background
        painter.fillRect(rect, self._colors['file_display_bg'])  # #202020
        
        # Draw border: 1px solid #333333; border-radius: 4px;
        painter.setRenderHint(QPainter.Antialiasing)
        painter.setPen(QPen(self._colors['border'], 1))
        painter.setBrush(Qt.NoBrush)
        painter.drawRoundedRect(rect.adjusted(0, 0, -1, -1), 4, 4)
    
    def polish(self, widget):
        """Polish widgets after creation - set up custom properties."""
        super().polish(widget)
        
        # Set up button types based on QSS selectors
        if isinstance(widget, QPushButton):
            # Check for type property that would have been set via QSS
            if not widget.property('type'):
                # Default button styling
                widget.setProperty('type', 'default')
    
    def standardPalette(self):
        """Return standard palette for the application."""
        palette = QPalette()
        
        # Window colors
        palette.setColor(QPalette.Window, self._colors['bg_dark'])
        palette.setColor(QPalette.WindowText, self._colors['text_primary'])
        
        # Base colors (for input fields, etc.)
        palette.setColor(QPalette.Base, self._colors['file_display_bg'])
        palette.setColor(QPalette.AlternateBase, self._colors['panel_bg'])
        palette.setColor(QPalette.Text, self._colors['text_primary'])
        
        # Button colors
        palette.setColor(QPalette.Button, self._colors['primary'])
        palette.setColor(QPalette.ButtonText, self._colors['text_primary'])
        
        # Selection colors
        palette.setColor(QPalette.Highlight, self._colors['secondary'])
        palette.setColor(QPalette.HighlightedText, self._colors['text_primary'])
        
        # Disabled colors
        palette.setColor(QPalette.Disabled, QPalette.WindowText, self._colors['text_secondary'])
        palette.setColor(QPalette.Disabled, QPalette.Text, self._colors['text_secondary'])
        
        return palette
    
    # Dynamic update methods
    def update_colors(self, color_scheme: dict):
        """Update color scheme dynamically."""
        self._colors.update(color_scheme)
        # Note: Caller should trigger widget updates
    
    def update_font_size(self, size: int):
        """Update base font size."""
        self._font_size = size
        config.set_value(ConfigKeys.App.BASE_FONT_SIZE, size)
    
    def switch_theme(self, theme_name: str):
        """Switch between light/dark themes."""
        self._current_theme = theme_name
        
        if theme_name == 'light':
            # Light theme colors
            self._colors.update({
                'bg_dark': QColor('#FFFFFF'),
                'text_primary': QColor('#000000'),
                'nav_bg': QColor('#E8F5E8'),
                'panel_bg': QColor('#F5F5F5'),
                'file_display_bg': QColor('#FAFAFA'),
                'border': QColor('#CCCCCC'),
            })
        else:  # dark theme (default)
            # Restore dark theme colors
            self._colors.update(self._load_color_palette())
```

### 2. Updated Style Application

```python
# flatmate/src/fm/gui/styles/__init__.py (Updated)
"""
Style management for the application.
Migrated from QSS to QProxyStyle for better performance and maintainability.
"""

from pathlib import Path
from typing import Optional
from PySide6.QtWidgets import QApplication
from PySide6.QtGui import QPalette
from ...core.config import config
from ...core.config.keys import ConfigKeys
from .flatmate_style import FlatmateStyle

# Global style instance for dynamic updates
_current_style: Optional[FlatmateStyle] = None

def apply_styles(app: QApplication) -> None:
    """Apply custom QProxyStyle to the application.
    
    Args:
        app: The QApplication instance
    """
    global _current_style
    
    # Create and apply custom style
    _current_style = FlatmateStyle()
    app.setStyle(_current_style)
    
    # Set application palette for consistent colors
    palette = _current_style.standardPalette()
    app.setPalette(palette)
    
    # Set application font
    font_size = config.get_value(ConfigKeys.App.BASE_FONT_SIZE, 14)
    font = app.font()
    font.setPointSize(font_size)
    app.setFont(font)

def update_font_size(app: QApplication, size: int) -> None:
    """Update application font size.
    
    Args:
        app: The QApplication instance
        size: New base font size
    """
    global _current_style
    
    if _current_style:
        _current_style.update_font_size(size)
        
        # Update application font
        font = app.font()
        font.setPointSize(size)
        app.setFont(font)
        
        # Trigger repaint of all widgets
        for widget in app.allWidgets():
            widget.update()

def switch_theme(app: QApplication, theme: str) -> None:
    """Switch application theme.
    
    Args:
        app: The QApplication instance
        theme: Theme name ('light' or 'dark')
    """
    global _current_style
    
    if _current_style:
        _current_style.switch_theme(theme)
        
        # Update application palette
        palette = _current_style.standardPalette()
        app.setPalette(palette)
        
        # Trigger repaint of all widgets
        for widget in app.allWidgets():
            widget.update()

# Legacy functions for backward compatibility during migration
def load_styles() -> str:
    """Legacy function - returns empty string as QSS is no longer used."""
    return ""

def get_current_style() -> Optional[FlatmateStyle]:
    """Get the current style instance for advanced operations."""
    return _current_style
```

### 3. Widget Integration Examples

```python
# Example: Setting button types to match QSS selectors
# In your widget creation code:

from PySide6.QtWidgets import QPushButton

# Primary button (was QPushButton[type="primary"] in QSS)
primary_btn = QPushButton("Save")
primary_btn.setProperty('type', 'primary')

# Secondary button (was QPushButton[type="secondary"] in QSS)
secondary_btn = QPushButton("Cancel")
secondary_btn.setProperty('type', 'secondary')

# Default button (no special styling)
default_btn = QPushButton("Help")
# No property needed - defaults to standard styling
```

### 4. Main Window Integration

```python
# flatmate/src/fm/gui/main_window.py (Updated sections)

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self._setup_ui()
        # Remove old QSS styling calls
        # self._apply_window_styles()  # Remove this
    
    def _apply_window_styles(self):
        """Legacy method - no longer needed with QProxyStyle."""
        # This method can be removed or kept empty for compatibility
        pass
    
    def _on_font_size_changed(self, size_name):
        """Handle font size changes via new style system."""
        from .styles import update_font_size
        
        # Convert size name to actual size
        size_map = {
            'small': 12,
            'medium': 14,
            'large': 16,
            'extra_large': 18
        }
        
        size = size_map.get(size_name.lower().replace(" ", "_"), 14)
        
        # Update via style system
        app = QApplication.instance()
        update_font_size(app, size)
```

This implementation provides a complete replacement for your current QSS system while maintaining the same visual appearance and adding better performance and maintainability.
