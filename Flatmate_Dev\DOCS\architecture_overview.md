# Flatmate Architecture Overview

Scope: Full project (hard overwrite). Style: Hybrid documentation with targeted code snippets and inline path references for critical components.

## System Context
Flatmate is a Python desktop application focused on personal finance data ingestion, processing, and categorization. It uses PySide6 for the GUI, a core layer for services (config, logging, events, data services), and feature modules (e.g., update_data). Documentation protocols and architecture guides live under flatmate/DOCS.

High-level layers:
- Core (configuration, logging, services, data standards)
- GUI (main window, shared components, table view system, toolbars)
- Feature Modules (e.g., update_data)
- Data Services (database, query processing, standards)
- Protocols/Docs (workflows, reports, guides)

## Project Roots
- Source: flatmate/src/fm/
- Tests: tests/
- Docs: DOCS/ and flatmate/DOCS/
- Venv: flatmate/.venv_fm313/

## Critical Components (with targeted snippets)
1) Core configuration
   - Path: flatmate/src/fm/core/config/config.py
   - Purpose: global configuration access (defaults, user prefs), keys/enums, paths utilities.
   - Snippet (indicative reference):
     - See config load/run usage in CLI or main entry points where config ensures defaults and reads persistent preferences.

2) Event bus
   - Path: flatmate/src/fm/core/services/event_bus.py
   - Purpose: Pub/sub for decoupled communication across GUI and modules.
   - Notes: Navigation/event-driven refactors recommend using unified bus for module transitions.

3) Module coordinator
   - Path: flatmate/src/fm/module_coordinator.py
   - Purpose: Central module transition/orchestration across the application runtime.

4) GUI main window
   - Path: flatmate/src/fm/gui/main_window.py (or components referenced in project docs)
   - Purpose: Top-level window composing panes, nav, status/info bars, and module surfaces.

5) Update Data module
   - Path: flatmate/src/fm/modules/update_data/
   - Purpose: Ingest statements, transform into standard forms, update DB, provide UI for file operations and feedback.

## Data Architecture Overview
- Core standards under flatmate/src/fm/core/data_services/standards/ guide uniform column schemas, naming, and processing expectations.
- Database layer under flatmate/src/fm/core/database/ with migrations, repository, and SQL utilities.
- Query processing patterns documented under flatmate/DOCS/_ARCHITECTURE/_DATABASE and flatmate/src/fm/core/services/query_processing/.

## GUI Architecture Overview
- Shared components under flatmate/src/fm/gui/_shared_components/ include table view v2, toolbars, and widgets.
- Toolbars and search/filter are evolving with dedicated documents in flatmate/DOCS/_ARCHITECTURE/_TABLE_VIEW_SYSTEM and related reports.

## Protocols and Documentation System
- Unified workflows and protocols housed under flatmate/DOCS/_PROTOCOLS, guiding coding sessions and documentation standards.
- Reports and analyses provide implementation summaries and onboarding references in flatmate/DOCS/_REPORTS.

## Key Architectural Principles
- Event-first, decoupled communication through a central event bus
- Standardized data columns and query processing patterns
- Reusable GUI components and table view system
- Documentation-driven development with repeatable protocols