from __future__ import annotations

from typing import Iterable, List, Union


def normalise_ext(filename: str) -> str:
    """Return the file extension in lower case without the leading dot.

    Examples:
    - "A.CSV" -> "csv"
    - "report.ofx" -> "ofx"
    - "noext" -> ""
    """
    if not filename:
        return ""
    return filename.rsplit('.', 1)[-1].lower() if '.' in filename else ''


def parse_supported_extensions(raw: Union[str, Iterable[str], None], default: Iterable[str] | None = None) -> List[str]:
    """Parse supported extensions from config input to a normalised list without dots.

    Accepts:
    - Comma-separated string (e.g., "*.csv, *.xlsx, .pdf")
    - Iterable[str] (e.g., ["csv", ".ofx"]) 
    - None -> returns default or []
    """
    default_list = list(default) if default is not None else []

    if raw is None:
        return default_list

    if isinstance(raw, str):
        parts = [p.strip().lstrip('*').lstrip('.').lower() for p in raw.split(',')]
        return [p for p in parts if p]

    try:
        return [str(ext).lstrip('.').lower() for ext in raw if str(ext).strip()]
    except TypeError:
        # Not iterable
        return default_list
