# File Dialogue System Execution Plan

**Date:** 2025-08-03  
**Author:** <PERSON> (Product Manager)  
**Status:** Ready for Immediate Execution  
**Priority:** P0 - Critical

## Overview

This execution plan provides concrete, actionable steps to implement the file dialogue system refactoring as outlined in the PRD. The plan is designed for immediate execution with minimal disruption to existing functionality.

## Phase 1: Critical Architectural Fixes

**Timeline:** Immediate (30 minutes)

### Step 1: Create File Selector Component

1. **Create new file_selector.py**
   ```python
   import os
   import sys
   from pathlib import Path
   from typing import List, Optional, Set

   from PySide6.QtWidgets import QFileDialog
   from ....config.ud_keys import UpdateDataKeys as ud_keys
   from ....config.ud_config import ud_config as config
   from fm.core.services.logger import log

   # Default directory if no last used directory is available
   default_dir = os.path.expanduser('~')


   class FileSelector:
       """Main interface for file selection operations."""
       
       @staticmethod
       def get_file_paths(method: str, title: str = "Select Source", 
                         parent=None, start_dir: Optional[str] = None) -> List[str]:
           """
           Convenience function to get file paths using a dialog.

           Args:
               method: 'select_files' or 'select_folder'.
               title: The title for the dialog window.
               parent: The parent widget for the dialog.
               start_dir: An optional directory to start in.

           Returns:
               A list of selected file paths.
           """
           log.debug(f"File selection requested: method={method}, title={title}")
           
           if method == 'select_files':
               dialog = FileDialogue(title=title, parent=parent, start_dir=start_dir)
               return dialog.selected_files
           
           elif method == 'select_folder':
               dialog = FolderDialogue(title=title, parent=parent, start_dir=start_dir)
               folder_path = dialog.selected_folder
               if folder_path:
                   return FileUtils.discover_files_in_folder(folder_path)
               return []
               
           else:
               raise ValueError(f"Invalid method '{method}'. Use 'select_files' or 'select_folder'.")


   class FolderDialogue:
       """Dialog for selecting a folder with platform-specific handling."""
       
       def __init__(self, title: str = "Select Folder", parent=None, start_dir: Optional[str] = None):
           """Initialize the folder selection dialog.
           
           Args:
               title: Dialog window title
               parent: Parent widget
               start_dir: Starting directory (uses last used dir if None)
           """
           self.selected_folder = ""
           initial_dir = start_dir or FileUtils.get_last_used_dir()

           if sys.platform == 'win32':
               # On Windows, the native folder dialog is poor. Use the file dialog workaround.
               instructive_title = f"{title} (select any file in the desired folder)"
               dialog = FileDialogue(title=instructive_title, parent=parent, start_dir=initial_dir)
               if dialog.selected_files:
                   self.selected_folder = os.path.dirname(dialog.selected_files[0])
           else:
               # On macOS and Linux, use the native folder dialog.
               folder = QFileDialog.getExistingDirectory(
                   parent=parent,
                   caption=title,
                   dir=initial_dir
               )
               self.selected_folder = folder

           if self.selected_folder:
               FileUtils.set_last_used_dir(self.selected_folder)
               log.debug(f"Selected folder: {self.selected_folder}")


   class FileDialogue:
       """Dialog for selecting one or more files."""
       
       def __init__(self, title: str = "Select File(s)", parent=None, start_dir: Optional[str] = None):
           """Initialize the file selection dialog.
           
           Args:
               title: Dialog window title
               parent: Parent widget
               start_dir: Starting directory (uses last used dir if None)
           """
           initial_dir = start_dir or FileUtils.get_last_used_dir()
           supported_types = FileUtils._get_supported_file_types()
           filter_string = f"Supported Files ({' '.join(['*.' + ext for ext in supported_types])});;All Files (*)"

           files, _ = QFileDialog.getOpenFileNames(
               parent=parent,
               caption=title,
               dir=initial_dir,
               filter=filter_string
           )

           if files:
               # Save the directory of the first selected file
               FileUtils.set_last_used_dir(os.path.dirname(files[0]))
           
           self.selected_files = files
           if files:
               log.debug(f"Selected {len(files)} file(s): {files[0]}{' and more' if len(files) > 1 else ''}")


   class FileUtils:
       """Utility class for file operations and configuration."""
       
       @staticmethod
       def _normalise_extension(filename: str) -> str:
           """Return the file extension in lower case, without the leading dot."""
           return filename.rsplit('.', 1)[-1].lower() if '.' in filename else ''
       
       @staticmethod
       def _get_supported_file_types(file_types: List[str] = ('csv', 'ofx', 'pdf')) -> List[str]:
           """Return supported file types from config, or fallback to default."""
           return config.get_value(ud_keys.Files.SUPPORTED, default=file_types)

       @staticmethod
       def get_last_used_dir() -> str:
           """Return the last used directory from config, or the user's home dir."""
           return config.get_value(ud_keys.Paths.LAST_SOURCE_DIR, default=default_dir)

       @staticmethod
       def set_last_used_dir(directory: str) -> None:
           """Save the last used directory to config."""
           config.set_value(ud_keys.Paths.LAST_SOURCE_DIR, directory)

       @staticmethod
       def discover_files_in_folder(folder_path: str) -> List[str]:
           """Discover supported files in the selected folder.
           
           Args:
               folder_path: Path to the folder to scan for files
               
           Returns:
               List of paths to supported files found in the folder
           """
           if not folder_path or not os.path.isdir(folder_path):
               log.debug(f"Invalid folder path: {folder_path}")
               return []

           folder = Path(folder_path)
           # Use a set for efficient O(1) average time complexity lookups
           supported_exts_set: Set[str] = set(ext.lstrip('.') for ext in FileUtils._get_supported_file_types())

           discovered_files = [
               str(file_path) 
               for file_path in folder.iterdir() 
               if file_path.is_file() and FileUtils._normalise_extension(file_path.name) in supported_exts_set
           ]
           
           log.debug(f"Discovered {len(discovered_files)} supported files in {folder_path}")
           return discovered_files
   ```

2. **Create temporary compatibility layer in get_files.py**
   ```python
   """
   DEPRECATED: This module is deprecated. Use file_selector.py instead.
   
   This compatibility layer is temporary and will be removed in a future release.
   """

   import warnings
   from .file_selector import FileSelector, FileUtils, FolderDialogue as SelectFolderDialogue, FileDialogue as SelectFileDialogue

   warnings.warn(
       "The get_files.py module is deprecated. Use file_selector.py instead.",
       DeprecationWarning,
       stacklevel=2
   )

   # Re-export the get_file_paths function for backwards compatibility
   get_file_paths = FileSelector.get_file_paths
   ```

### Step 2: Update File Management Imports

1. **Update file_management.py imports**
   ```python
   # Replace
   from .._view.shared_components.get_files import get_file_paths, FileUtils
   
   # With
   from .._view.shared_components.file_selector import FileSelector, FileUtils
   ```

2. **Update file_management.py usage**
   ```python
   # Replace any remaining instances of get_file_paths with FileSelector.get_file_paths
   ```

### Step 3: Validate Interface Compliance

1. **Verify view interface implementation in ud_view.py**
   - Confirm `show_files_dialog` and `show_folder_dialog` methods exist
   - Ensure they don't use `get_main_window()` internally
   - Test that they return the expected values

2. **Fix any interface mismatches**
   - Update method signatures if needed
   - Ensure return types match interface contract

## Phase 2: System Optimisation

**Timeline:** After Phase 1 (30 minutes)

### Step 4: Enhance Error Handling

1. **Improve folder validation in FileUtils**
   ```python
   @staticmethod
   def discover_files_in_folder(folder_path: str) -> List[str]:
       """Discover supported files in the selected folder."""
       if not folder_path:
           log.warning("Empty folder path provided")
           return []
           
       try:
           folder = Path(folder_path)
           if not folder.exists():
               log.warning(f"Folder does not exist: {folder_path}")
               return []
               
           if not folder.is_dir():
               log.warning(f"Path is not a directory: {folder_path}")
               return []
               
           if not os.access(folder_path, os.R_OK):
               log.warning(f"No read permission for folder: {folder_path}")
               return []
           
           # Rest of the method...
       except PermissionError as e:
           log.error(f"Permission error accessing folder {folder_path}: {e}")
           return []
       except Exception as e:
           log.error(f"Error discovering files in {folder_path}: {e}")
           return []
   ```

### Step 5: Configuration Management

1. **Centralise directory handling**
   ```python
   @staticmethod
   def get_last_used_dir() -> str:
       """Return the last used directory from config, or the user's home dir."""
       last_dir = config.get_value(ud_keys.Paths.LAST_SOURCE_DIR, default=default_dir)
       
       # Validate the directory exists and is accessible
       if last_dir and os.path.isdir(last_dir) and os.access(last_dir, os.R_OK):
           return last_dir
       
       # Fall back to home directory if the saved path is invalid
       return default_dir
   ```

## Phase 3: Testing & Documentation

**Timeline:** After Phase 2 (15 minutes)

### Step 6: Testing

1. **Test file selection workflow**
   - Select individual files
   - Verify correct paths are returned
   - Check last directory is saved

2. **Test folder selection workflow**
   - Select folder
   - Verify correct files are discovered
   - Check last directory is saved

### Step 7: Documentation

1. **Update architecture documentation**
   - Document the new component structure
   - Explain the interface contract
   - Provide usage examples

## Implementation Checklist

- [ ] Create new `file_selector.py` with proper class structure
- [ ] Create temporary compatibility layer in `get_files.py`
- [ ] Update imports in `file_management.py`
- [ ] Verify view interface implementation
- [ ] Fix any interface mismatches
- [ ] Enhance error handling in `FileUtils`
- [ ] Centralise directory configuration
- [ ] Test file selection workflow
- [ ] Test folder selection workflow
- [ ] Update architecture documentation

## Rollback Plan

If issues arise during implementation:

1. **Immediate Rollback**
   - Restore original `get_files.py`
   - Revert import changes
   - Test basic functionality

2. **Partial Rollback**
   - Keep architectural improvements
   - Revert problematic optimisations
   - Maintain MVP compliance

## Success Validation

After implementation, verify:

1. **Architectural Compliance**
   - Zero Qt imports in presenter layer
   - All dialogue operations through view interface
   - Clean component structure and naming

2. **Functional Requirements**
   - File selection works on all platforms
   - Folder selection discovers files correctly
   - Configuration persists properly
   - Error handling provides clear feedback

## Next Steps

1. Execute Phase 1 implementation immediately
2. Validate changes with basic testing
3. Document architectural improvements
4. Plan for Phase 2 and 3 implementation
