---
description: Session handover – Update Data / Guide Pane v2 (2025-08-10 23:18:39 NZ)
---

# Session Handover – Guide Pane v2 MVP integration
Time: 2025-08-10 23:18:39 NZ

## Context
- Objective: Refactor Update Data UI to integrate Guide Pane v2 under clean MVP.
- Decision: Use namespaced `guide_*` methods on `IUpdateDataView`; presenter stays Qt‑free; view never instantiates presenters.
- We are committing to Guide Pane v2 (remove switching/adapter paths).

## Current state (done)
- Presenter composition root: `UpdateDataPresenter` now instantiates `GuidePanePresenter(self.view)` and passes it to `StateManager`.
- View API: `UpdateDataView` implements namespaced `guide_*` methods, fail‑fast if `guide_pane` not initialised.
- Adapter removed: `_GuidePaneViewAdapter` and in‑view presenter notifications have been deleted.
- Interface extended: `IUpdateDataView` now declares all `guide_*` methods.
- Docs: Added mapping doc: `guide_pane_v2_api_mapping.md` (1:1 mapping from `guide_*` to widget v2).

## Relevant files & docs
- Code
  - `src/fm/modules/update_data/_ui/_presenter/guide_pane_presenter.py`
  - `src/fm/modules/update_data/ud_presenter.py`
  - `src/fm/modules/update_data/_ui/ud_view.py`
  - `src/fm/modules/update_data/_ui/interface/i_view_interface.py`
  - `src/fm/modules/update_data/_ui/_presenter/state_coordinator.py` (uses `guide_presenter`)
  - [Pending removal] `src/fm/modules/update_data/_ui/interface/i_guide_pane_view.py` (now unused)
- Docs
  - `__UD_REFACTORING_docs_workspace/25-08-10_Session_1_ GUIDE_PANE_WIDGETS/guide_pane_v2_api_mapping.md`
  - `__UD_REFACTORING_docs_workspace/25-08-10_Session_1_ GUIDE_PANE_WIDGETS/ui_system_audit/events_interface_matrix.md`

## Next session – planned work
- Protocol over metaclass inheritance
  - Replace/augment runtime assurance of view API conformance using `typing.Protocol` rather than inheriting ABC that conflicts with Qt metaclasses.
  - Provide a `SupportsUpdateDataView` protocol capturing only the surface used by presenters/managers (esp. `guide_*` API, left panel setters, dialogs shim).
  - Add lightweight runtime guard (optional): a dev utility to assert required attributes exist during wiring in presenter (fail fast in dev builds).

- Guide Pane v2 audit (naming & mapping)
  - Verify concrete `guide_pane` widget implements exactly:
    - `display(text)`
    - `set_status(status)`
    - `show_source_context_options(monitor_enabled: bool, auto_queue_enabled: bool, folder: Optional[str])`
    - `set_archive_summary(text)`
    - `set_actions_enabled(process_enabled: bool, reset_enabled: bool)`
    - `set_discovery_badge(active: bool)`
  - If names diverge, either:
    - Update the widget to match v2 canonical names (preferred), or
    - Adjust `UpdateDataView` delegators (temporary), keeping namespaced `guide_*` stable for presenters.

- Clean-up
  - Remove `i_guide_pane_view.py` if confirmed unused across the repo.
  - Grep for any non‑namespaced guide calls (`set_message`, `set_status`, etc.) and migrate.
  - Tighten logging to our standard (keep concise; no broad try/except that hides errors).

## Open questions / decisions
- Do we want a small dev‑time `assert_view_contract(view)` in `UpdateDataPresenter` to validate presence of `guide_*` methods? This keeps fail‑fast without metaclass coupling.
- Should `guide_show_source_context_options` carry `monitor_enabled` at all if v2 will not use it? Current mapping hard‑codes `False`.

## Risks / notes
- Any remaining legacy adapters or signals may cause duplicated UI writes. We removed known instances in `ud_view.py`.
- Ensure `StateManager` is the single orchestrator that calls `GuidePanePresenter` for state→UI sync paths.

## Quick verification steps
- List files from a folder → expect `guide_display`, `guide_set_status('info')`, `guide_set_actions_enabled(True, True)`, context options shown with correct `folder`, discovery badge reflects toggle state.
- Change save option → expect `guide_set_archive_summary` updates only (no side effects).

## Done this session
- Implemented Option 1 namespaced API.
- Presenter as composition root for guide presenter.
- Removed legacy adapter/switching logic.
- Added v2 API mapping document.
