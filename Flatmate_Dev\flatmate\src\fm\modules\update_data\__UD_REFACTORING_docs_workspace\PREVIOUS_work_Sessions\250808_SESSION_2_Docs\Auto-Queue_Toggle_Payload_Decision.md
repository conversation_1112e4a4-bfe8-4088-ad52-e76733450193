# Auto-Queue Toggle Payload Decision

Date: 2025-08-08
Status: Implemented (MVP)

## Summary
We chose a pragmatic MVP design for the per-folder Auto-Queue toggle: the UI emits the folder path together with the enabled state. This avoids presenter-side folder resolution logic during the toggle, reduces complexity, and preserves clean MVP separation.

- Event intent: `AUTO_QUEUE_TOGGLED` carries `{ folder: str, enabled: bool }`.
- The presenter delegates persistence/monitoring to `AutoQueueManager`.
- The UI checkbox is rendered strictly from persisted config for restart consistency.
- The toggle is shown/enabled only when a single folder context is resolved.

## Rationale
- Keeps the presenter the single source of truth for the current folder context while avoiding re-resolution at toggle time.
- Prevents drift between UI state and runtime monitoring state; the UI is always derived from `ud_config`.
- Simplifies MVP wiring and reduces brittle heuristics.

## Trade-offs
- Slightly increases coupling between the guide pane and the concept of a folder path. This is acceptable in MVP since the presenter provides the folder path explicitly when rendering.
- Multi-folder scenarios remain out of scope and will be handled by a future configuration pane.

## Implementation Notes
- `guide_pane.py` now caches `folder` passed via `show_source_context_options(..., folder=...)` and emits `publish_toggle_auto_queue_requested(str, bool)`.
- `ud_view.py` bridges the guide pane signal to the local bus as `AUTO_QUEUE_TOGGLED` with `{folder, enabled}`.
- `state_coordinator.py` passes the resolved folder to `view.show_guide_source_options(..., folder=folder_path)`.
- `ud_presenter.py` subscribes to the intent and delegates to:
  - `AutoQueueManager.set_auto_queue(folder, enabled)` which:
    - Persists via `ud_config.set_auto_queue_enabled(folder, enabled)`
    - Calls `FolderMonitorService.set_folder_monitored(folder, enabled)`
  - Presenter then calls `state_manager.sync_state_to_view()`.

## Out of Scope (MVP)
- Multi-folder selection toggling
- Bulk enable/disable
- Inferring folders for ambiguous contexts

## Future Considerations
- A dedicated configuration pane for managing multiple monitored folders and bulk actions.
- A background reconciliation task to sanity-check monitoring state on startup.
