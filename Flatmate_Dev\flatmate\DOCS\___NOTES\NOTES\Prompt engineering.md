# Prompt engineering


@bmad-master.md 
I have an issue with a borked refactor attempt.`


I have re organised the files related to this attempt,, they are all in @z_borked_attempt which should provide context as to what has recently transpired...

first:
I need a concise, but thorugh review report on how the gui in in ud_data actually functions.


then:



plese provide some options as to direction 

I think the real issue here is I may have failed to adequately describe the exact logical flow I want 
There is an attempt in sheet one of @Update Data State_v1.xlsx 

BUt I think a simple, user centric,  'pick a path book'-like description of the  logical flow, in a .md would be helpful.


---
---