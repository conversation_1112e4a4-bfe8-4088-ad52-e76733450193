# DRY Principles for Qt QSS Theming and Maintainability

Date: 2025-08-05
Purpose: Summarize DRY best practices applied to Qt Style Sheets (QSS) and propose maintainable patterns for theming in Flatmate.

Summary
- QSS does not natively support variables (neither {{...}} nor CSS var()) per Qt docs.
- DRY in QSS is achieved by centralizing decisions (palette, scale) and minimizing per-widget overrides.
- Use app-level preprocessing for limited template tokens ({{FONT}}, {{FONT_SIZE}}) and data-driven color mapping from YAML.
- Keep a single consolidated QSS as the source of truth; avoid overlay QSS layers where possible.

Authoritative references
- Qt Style Sheets docs: [Qt 6 Style Sheets](https://doc.qt.io/qt-6/stylesheet.html), [Style Sheet Syntax](https://doc.qt.io/qt-6/stylesheet-syntax.html)
- Variables discussion (community practice): [Using variables in Qt StyleSheets](https://stackoverflow.com/questions/10898399/using-variables-in-qt-stylesheets)

Core DRY principles for QSS
1) Single Source of Truth
- Authoritative stylesheet: base_theme.qss (preferred filename), with temporary fallback flatmate_consolidated.qss during migration.
- Avoid duplicating color/spacing values across many rules; reference the same value consistently.

2) Externalize Theme Data
- Store theme in data (YAML) rather than scattering values through QSS or Python.
- Keep font base size in config; keep palette in a small, named set.

3) Limited, High-Signal Tokens
- Use application-level template tokens only for textual parameters that aren’t native to QSS:
  - {{FONT_SIZE}} → integer pixel size (replaced from ConfigKeys.App.BASE_FONT_SIZE with fallback 14)
- Replace tokens in-memory prior to app.setStyleSheet(); log replacement counts, warn on dangling tokens.

4) Palette-First Design
- Define a minimal palette of authoritative colors:
  - primary, primary_hover, nav_bg, panel_bg, text_primary, border_muted, scrollbar_bg, scrollbar_handle
- Ensure QSS rules consistently use palette colors. This enables global changes via small YAML edits.

5) Deterministic Replacement
- Prefer direct hex-literal mapping initially:
  - Map exact hex literals appearing in QSS to new values from YAML.
  - Benefits: zero churn in QSS; guaranteed deterministic behavior; easy to audit via counts.
- Optionally introduce semantic color tokens later with guardrails once stable.

6) Component Variants over Ad Hoc Rules
- Consolidate buttons into a few variants:
  - QPushButton[type="action_btn"], [type="nav_btn"], [type="select_btn"], [type="exit_btn"]
- Each variant references only palette colors and shared spacing/shape rules, reducing divergence.

7) Guardrails and Telemetry
- Replacement summary:
  - replacements.font_size: N
  - replacements.colors: { "#1a381f": N, "#3B8A45": N, ... }
- Warn (or fail in strict mode) if any expected token/color has zero matches.
- Add a quick check for any remaining {{...}} tokens after transform.

Updated implementation pattern for Flatmate
1) Theme files (YAML)
- Location: flatmate/src/fm/gui/styles/themes/
- Files: theme-dark.yaml (baseline), theme-light.yaml (example)
- Example schema:
  theme: dark
  version: 1
  font:
    base_size: 14
  palette:
    primary: "#3B8A45"
    primary_hover: "#2F6E38"
    nav_bg: "#1a381f"
    panel_bg: "#1E1E1E"
    text_primary: "#FFFFFF"
  hex_map:
    "3B8A45": "#3B8A45"
    "1a381f": "#1a381f"
    "1E1E1E": "#1E1E1E"

2) Application flow
- Load consolidated QSS text via [loader.py](flatmate/src/fm/gui/styles/loader.py:1)
- Replace {{FONT_SIZE}} from config (fallback 14)
- If enabled, apply hex_map replacements (exact, case-sensitive), counting matches
- Set stylesheet via [applier.py](flatmate/src/fm/gui/styles/applier.py:1)

3) Theme Experiment default
- ENABLE_THEME_EXPERIMENT is disabled by default in [loader.py](flatmate/src/fm/gui/styles/loader.py:1).
- To enable: set ENABLE_THEME_EXPERIMENT=True and set DEFAULT_THEME_NAME to "light" or "dark".

Anti-patterns to avoid
- Over-tokenization: replacing every color with {{TOKEN}} immediately leads to heavy churn and confusion.
- Multiple QSS overlays: introduce cascading conflicts, unclear precedence, and DRY violations.
- Per-widget overrides scattered across the app: hard to audit and update.

Checklist for DRY compliance
- [ ] All component variants reference only palette colors (no stray values)
- [ ] {{FONT_SIZE}} present and replaced with logged counts
- [ ] Theme YAML contains a hex_map of exactly what QSS uses
- [ ] Replacement summary warns if any key had zero matches
- [ ] No new QSS overlays introduced without a documented scope and sunset plan

Integration points in Flatmate
- QSS baseline: [base_theme.qss](flatmate/src/fm/gui/styles/base_theme.qss:1)
- Loader/Applier: [loader.py](flatmate/src/fm/gui/styles/loader.py:1), [applier.py](flatmate/src/fm/gui/styles/applier.py:1)
- Documents:
  - Brownfield plan: [BROWNFIELD_Styling_System_Architecture.md](flatmate/DOCS/_ARCHITECTURE/STYLING_SYSTEM/BROWNFIELD_Styling_System_Architecture.md:1)
  - Template variables analysis: [VARIABLES_Usage_Investigation.md](flatmate/DOCS/_ARCHITECTURE/STYLING_SYSTEM/VARIABLES_Usage_Investigation.md:1)
  - Tavily findings: [TAVILY_QSS_Variables_Findings.md](flatmate/DOCS/_ARCHITECTURE/STYLING_SYSTEM/TAVILY_QSS_Variables_Findings.md:1)

Conclusion
A DRY QSS architecture in Flatmate centers on:
- One consolidated stylesheet
- Theme-as-data with YAML
- Minimal, high-signal template tokens
- Palette-driven component variants
- Deterministic, counted replacements with guardrails

This approach prevents widespread edits, supports theme evolution, and maintains clarity for developers.