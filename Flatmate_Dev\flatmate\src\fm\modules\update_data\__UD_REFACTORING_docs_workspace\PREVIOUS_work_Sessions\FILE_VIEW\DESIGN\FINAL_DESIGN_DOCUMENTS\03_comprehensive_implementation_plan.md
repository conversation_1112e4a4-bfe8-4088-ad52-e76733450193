# Comprehensive Implementation Plan

## Overview

This document integrates the directory structure, signal handling, and widget implementation designs into a cohesive implementation plan. It provides a clear roadmap for implementing the File View component refactoring in the Update Data module.

## Implementation Phases

### Phase 1: Directory Structure and Interface Setup

1. **Create Directory Structure**
   - Create `ui/` folder with subfolders for view, events, and managers
   - Create `file_pane_v2/` folder to keep original component as reference
   - Set up interface and event definition files
      >> good
2. **Define Interface and Events**
   - Create `i_view_interface.py` with method-only Protocol
        >> THIS ALREADY EXISTS - do you mean edit?
   - Create `file_events.py` with explicit event definitions
   - Ensure clear separation between interface methods and events
         >> I'm open to this 
3. **Create Component Skeleton**
   - Implement basic `UDFileView` class inheriting from BasePane
   - Set up internal component structure (file table, buttons)
   - Define data model and configuration classes

### Phase 2: Core Component Implementation

1. **Implement File View Data Model**
   - Create `FileViewModel` class in models.py
   - Implement file metadata handling
   - Set up selection state tracking

2. **Implement Core UI Components**
   - Build `FileTable` component for displaying files
   - Create `AddRemoveButtons` component
   - Implement `ContextMenu` for right-click operations

3. **Implement Interface Methods**
   - Add file operations (add, remove, get)
   - Implement configuration methods
   - Set up file dialog handling

4. **Set Up Event Publishing**
   - Create events object in UDFileView
   - Implement event emission for key operations
   - Connect internal signals to event publishing
  >> we already have an events and events_data in the module folder.. you'll need to examine these - i find the events_data name vague. 
### Phase 3: Integration with Update Data Module

1. **Update View Layer**
   - Modify `ud_view.py` to connect widget events to view signals
   - Remove signal handling from panel layout managers
   - Update center panel to use new file view component
   ->> I think these files should now be called eg:  _center_panel_layout_manager
2. **Update Presenter Connections**
   - Modify `ud_presenter.py` to connect to view signals
   - Configure the file view component through its interface
   - Update presenter handlers for file operations

3. **Test Integration**
   - Verify signal flow from widgets to presenter
   - Test file operations end-to-end
   - Ensure all functionality works as expected

### Phase 4: Migration and Cleanup

1. **Parallel Testing**
   - Run both old and new implementations in parallel
   - Compare behavior and fix any discrepancies
   - Ensure all features are properly implemented

2. **Switch to New Implementation**
   - Update module coordinator to use new file view
   - Verify all functionality works in production code
   - Address any issues that arise

3. **Remove Old Implementation**
   - Once new implementation is stable, remove old code
   - Update any remaining references
   - Clean up unused files and imports

## Concrete Implementation Steps

### Step 1: Create Directory Structure

```bash
# Create main folders
mkdir -p flatmate/src/fm/modules/update_data/ui/view/interface
mkdir -p flatmate/src/fm/modules/update_data/ui/view/components/file_pane_v2/components
mkdir -p flatmate/src/fm/modules/update_data/ui/events
mkdir -p flatmate/src/fm/modules/update_data/ui/managers

# Create __init__.py files
touch flatmate/src/fm/modules/update_data/ui/__init__.py
touch flatmate/src/fm/modules/update_data/ui/view/__init__.py
touch flatmate/src/fm/modules/update_data/ui/view/components/__init__.py
touch flatmate/src/fm/modules/update_data/ui/view/components/file_pane_v2/__init__.py
touch flatmate/src/fm/modules/update_data/ui/view/components/file_pane_v2/components/__init__.py
touch flatmate/src/fm/modules/update_data/ui/events/__init__.py
```

### Step 2: Create Interface and Events

```python
# ui/view/interface/i_view_interface.py
from typing import Protocol, List, Optional

class IUpdateDataView(Protocol):
    """Interface for update data view - METHODS ONLY"""
    def add_files(self, files: List[str]) -> None: ...
    def remove_file(self, file_path: str) -> None: ...
    def get_current_files(self) -> List[str]: ...
    def get_selected_file(self) -> Optional[str]: ...
    def set_processing_state(self, processing: bool) -> None: ...
    def show_error(self, message: str) -> None: ...
```

```python
# ui/events/file_events.py
from PySide6.QtCore import Signal, QObject

class FileViewEvents(QObject):
    """Events published by file view widget"""
    file_paths_list_updated = Signal(list)  # List[str] of file paths
    file_selected = Signal(str)             # Selected file path
```

### Step 3: Implement Component Files

```python
# ui/view/components/file_pane_v2/models.py
from dataclasses import dataclass
from typing import List, Optional
from datetime import datetime

@dataclass
class FileInfo:
    """Data structure for file information."""
    path: str
    size: int
    modified: datetime
    file_type: str
    is_valid: bool = True
    is_processed: bool = False
    
class FileViewModel:
    """Data model for the file view component."""
    # Implementation as defined in widget_implementation.md
```

```python
# ui/view/components/file_pane_v2/config.py
from dataclasses import dataclass, field
from typing import List

@dataclass
class FileConfig:
    """Configuration for file view behavior and appearance."""
    # Implementation as defined in widget_implementation.md
```

```python
# ui/view/components/file_pane_v2/ud_file_view.py
from PySide6.QtWidgets import QVBoxLayout
from fm.gui.components.shared.base_pane import BasePane
from fm.modules.update_data.ui.events.file_events import FileViewEvents
from .models import FileViewModel
from .config import FileConfig
from .components.file_table import FileTable
from .components.add_remove_btns import AddRemoveButtons

class UDFileView(BasePane):
    """Self-contained file display component inheriting from BasePane."""
    # Implementation as defined in widget_implementation.md
```

### Step 4: Update View and Presenter

```python
# ui/view/ud_view.py
from PySide6.QtWidgets import QWidget, QHBoxLayout
from PySide6.QtCore import Signal
from fm.modules.update_data.ui.view.components.center_panel import CenterPanel
from fm.modules.update_data.ui.view.components.left_panel import LeftPanel

class UpdateDataView(QWidget):
    # View-level signals for presenter
    file_list_changed = Signal(list)
    file_selected = Signal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_components()
        self._connect_widget_signals()
        
    def _setup_components(self):
        self.center_panel = CenterPanel()
        self.left_panel = LeftPanel()
        
        # Main layout
        main_layout = QHBoxLayout()
        main_layout.addWidget(self.left_panel)
        main_layout.addWidget(self.center_panel)
        self.setLayout(main_layout)
        
    def _connect_widget_signals(self):
        # Get the file view component
        file_view = self.center_panel.file_view
        
        # Connect widget events to view signals
        file_view.events.file_paths_list_updated.connect(self.file_list_changed.emit)
        file_view.events.file_selected.connect(self.file_selected.emit)
```

```python
# ud_presenter.py
class UpdateDataPresenter:
    def __init__(self, view):
        self.view = view
        self._connect_signals()
        
    def _connect_signals(self):
        """Connect signals between view and presenter."""
        # Connect to view signals, not widget signals
        self.view.file_list_changed.connect(self._on_file_list_changed)
        self.view.file_selected.connect(self._on_file_selected)
        
        # Configure the file view component
        file_view = self.view.center_panel.file_view
        file_view.configure(
            group_by_folder=True,
            sort_by="name",
            allowed_file_types=["*.csv"]  # Use module constants
        )
```

## Testing Plan

### Unit Tests

```python
# tests/modules/update_data/ui/view/components/file_pane_v2/test_ud_file_view.py
import pytest
from PySide6.QtWidgets import QApplication
from fm.modules.update_data.ui.view.components.file_pane_v2.ud_file_view import UDFileView

@pytest.fixture
def file_view():
    app = QApplication.instance() or QApplication([])
    view = UDFileView()
    yield view
    app.quit()

def test_add_file(file_view, tmp_path):
    # Create a test file
    test_file = tmp_path / "test.csv"
    test_file.write_text("test")
    
    # Add file
    file_view.add_file(str(test_file))
    
    # Verify file was added
    assert str(test_file) in file_view.get_files()
    
def test_remove_file(file_view, tmp_path):
    # Create a test file
    test_file = tmp_path / "test.csv"
    test_file.write_text("test")
    
    # Add and remove file
    file_view.add_file(str(test_file))
    file_view.remove_file(str(test_file))
    
    # Verify file was removed
    assert str(test_file) not in file_view.get_files()
```

### Integration Tests

```python
# tests/modules/update_data/test_integration.py
import pytest
from PySide6.QtWidgets import QApplication
from fm.modules.update_data.ui.view.ud_view import UpdateDataView
from fm.modules.update_data.ud_presenter import UpdateDataPresenter

@pytest.fixture
def update_data_module():
    app = QApplication.instance() or QApplication([])
    view = UpdateDataView()
    presenter = UpdateDataPresenter(view)
    yield (view, presenter)
    app.quit()

def test_signal_flow(update_data_module, qtbot):
    view, presenter = update_data_module
    
    # Mock presenter handler
    signal_received = False
    def handler(files):
        nonlocal signal_received
        signal_received = True
    
    # Connect to view signal
    view.file_list_changed.connect(handler)
    
    # Trigger event from widget
    file_view = view.center_panel.file_view
    file_view.add_file("test.csv")
    
    # Verify signal flow
    assert signal_received, "Signal did not propagate from widget to view"
```

## Risk Mitigation

1. **Parallel Implementation**: Develop new components alongside existing ones to minimize disruption
2. **Comprehensive Testing**: Test each component thoroughly before integration
3. **Gradual Migration**: Switch components one at a time to identify and fix issues early
4. **Rollback Plan**: Keep old implementation until new one is fully validated

## Success Criteria

1. **Clean Architecture**: UI concerns clearly separated from business logic
2. **Explicit Intent**: Interface methods vs. events clearly distinguished
3. **Maintainable Structure**: Related files grouped together
4. **Full Functionality**: All existing features work in the new implementation
5. **Improved Testability**: Components have clean boundaries for easier testing

## Timeline

1. **Phase 1**: 1-2 days
2. **Phase 2**: 2-3 days
3. **Phase 3**: 1-2 days
4. **Phase 4**: 1 day

Total estimated time: 5-8 days
