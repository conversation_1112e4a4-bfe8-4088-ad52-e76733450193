# Key Workflows

Scope: Full project. Hybrid style with targeted references and file paths. Focus on flows that cross layers.

## 1) Application Startup

Sequence:
1. Entry point launches the app and initializes core services
2. Configuration defaults ensured and user preferences loaded
3. Main window constructed with panes, toolbars, and status elements
4. Module system initialized and default/last-used module presented

Key paths:
- Main entry: flatmate/src/fm/main.py
- Module coordinator: flatmate/src/fm/module_coordinator.py
- Config system: flatmate/src/fm/core/config/
- GUI main window: flatmate/src/fm/gui/main_window.py

Notes:
- Ensure config is available to UI and modules at startup
- Consider event bus subscription setup during initialization to avoid tight coupling

## 2) Module Transition (Target Architecture)

Recommended pattern:
- Trigger: User clicks nav button (GUI)
- Publish: Navigation event on the global event bus
- Subscribe: ModuleCoordinator receives event and switches active module
- Render: Main window updates the center content/panels

Key paths:
- Event bus: flatmate/src/fm/core/services/event_bus.py
- Coordinator: flatmate/src/fm/module_coordinator.py
- GUI nav components: flatmate/src/fm/gui/components/ (or nav_pane if applicable)

Benefits:
- Decouples UI components from coordinator internals
- Standardizes transitions; improves testability

## 3) Update Data: File → Database

Stages:
1. User selects source files in Update Data UI
2. Handlers load CSVs with header inference and mapping to standard columns
3. Optional enrichment (debit/credit split, metadata columns)
4. Database persistence via repository
5. UI feedback via status/info panels; logs record operations

Key paths:
- Module: flatmate/src/fm/modules/update_data/
- Standards: flatmate/src/fm/core/data_services/standards/
- Database: flatmate/src/fm/core/database/
- Query processing: flatmate/src/fm/core/services/query_processing/

Operational notes:
- Respect user preferences for defaults (e.g., last source, behavior flags)
- Keep long-running IO/processing off the UI thread

## 4) Query → Table View Render

Sequence:
1. Build query based on module/view state and user filters
2. Execute via query processing utilities
3. Present DataFrame in table view v2
4. Toolbars support search, column visibility, ordering
5. Persist user preferences (e.g., visible columns, order)

Key paths:
- Query processing: flatmate/src/fm/core/services/query_processing/
- Table view components: flatmate/src/fm/gui/_shared_components/table_view_v2/
- Toolbar/search UI: see flatmate/DOCS/_ARCHITECTURE/_TABLE_VIEW_SYSTEM/ and related reports

Performance:
- Avoid fetching full datasets unnecessarily
- Defer heavy processing from the GUI thread

## 5) Export Current View (Planned)

Sequence:
1. Gather current filtered/visible dataset from view model
2. Serialize to CSV/XLSX honoring column visibility/order
3. Save to user-chosen destination

Considerations:
- Consistent naming; include timestamp and filter summary if helpful
- Align with standards and user preference system

## 6) Logging and Diagnostics

Flow:
- Use centralized logger; adjust verbosity via config
- Capture ingestion and persistence summaries
- Optional UI surface for recent processing messages

Key paths:
- Logger service: flatmate/src/fm/core/services/logger.py (or equivalent)
- Config toggles: flatmate/src/fm/core/config/

Guidelines:
- Avoid excessive terminal spam by default
- Provide structured context in error messages for root-cause analysis

## 7) Configuration Life Cycle

Sequence:
1. On startup, ensure defaults
2. Read user preferences
3. During runtime, update user preferences on key actions (e.g., last-used columns/layout)
4. Persist changes gracefully and validate keys/enums

Key paths:
- Config manager and keys: flatmate/src/fm/core/config/
- Module-specific usage: feature modules under flatmate/src/fm/modules/

Best practices:
- Centralize keys and document meanings
- Validate against duplicates, consider audit utilities