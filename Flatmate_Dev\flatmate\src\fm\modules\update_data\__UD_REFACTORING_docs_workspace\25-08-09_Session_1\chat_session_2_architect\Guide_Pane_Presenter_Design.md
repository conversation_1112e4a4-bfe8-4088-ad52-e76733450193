# Guide Pane Presenter and Interface – Design Decisions

## Goals

- Maintain MVP purity: zero Qt coupling in presenters.
- Centralise guide copy/state rules; avoid duplication across managers.
- Prefer direct interface method calls over events for synchronous, single-listener UI updates.

## Components

- **IGuidePaneView (new)**
  - A small view interface the widget implements.
  - Declares high-level methods only: set_message, set_status, set_actions_enabled, set_archive_summary, set_discovery_badge.
  - Purpose: keep presenters independent from Qt and concrete widgets.

- **GuidePanePresenter (new)**
  - Orchestrator for guide messages and state display.
  - Pulls facts from state/services and writes to `IGuidePaneView`.
  - Exposes a clear API of `on_*` methods and a `refresh()` convenience method.
  - No direct Qt, no event bus.

## Terminology

- **API (of a class)**: its public methods. Every class has an API.
- **Interface (type contract)**: a separate Protocol/ABC used to decouple callers from implementations.
  - We require an interface for the View to keep presenters Qt-free.
  - The presenter itself does not need an interface unless multiple implementations are expected.

## Presenter API (called by peer managers)

- `on_source_changed()`
- `on_files_listed()`
- `on_save_option_changed()`
- `on_discovery_toggled(enabled: bool, folder: str)`
- `on_processing_started(total: int)`
- `on_processing_completed(success_count: int, fail_count: int)`
- `on_error(title: str, message: str)`
- `refresh()` – recompute from current state (if provided)

## Copy/State Rules (concise)

- No source: "Select a folder or choose files to begin." status: info; actions disabled.
- Folder selected, N files listed: "Listed N file(s) from '<folder>'"; status: info; Process enabled if N > 0.
- Save option: "Archive: Same as Source" or "Archive: <path>".
- Discovery: show badge when enabled for the current folder.
- Processing: "Processing N file(s)…"; on completion show success or mixed counts; status success/warning/error accordingly.
- Do not claim processing unless the pipeline actually ran.

## Integration Guidance

- Other managers should call presenter methods directly (no event bus):
  - After setting canonical files → `on_files_listed()`
  - When save option changes → `on_save_option_changed()`
  - When enabling/disabling discovery → `on_discovery_toggled(...)`
  - On processing start/complete → `on_processing_started(...)` / `on_processing_completed(...)`
  - On errors → `on_error(...)`

## File Locations

- Interface: `fm/modules/update_data/_ui/interface/i_guide_pane_view.py`
- Presenter: `fm/modules/update_data/_ui/_presenter/guide_pane_presenter.py`

## Logging

- Use `from fm.core.services.logger import log`.
- Keep logs factual and minimal; avoid implying processing unless it occurred.
