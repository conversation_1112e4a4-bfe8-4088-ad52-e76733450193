"""
Guide Pane View Interface (Protocol).

Defines a minimal, Qt-free contract for guide pane updates used by presenters.
"""
from __future__ import annotations
from typing import Protocol


class IGuidePaneView(Protocol):
    """High-level guide pane API for presenters.

    Implemented by the concrete guide pane widget or an adapter around it.
    Keeps presenters decoupled from Qt and widget specifics.
    """

    # Display message text (plain)
    def set_message(self, text: str) -> None:
        ...

    # Visual status: one of 'idle' | 'info' | 'warning' | 'error' | 'success' | 'processing'
    def set_status(self, status: str) -> None:
        ...

    # Enable/disable key actions shown in the guide (process/reset)
    def set_actions_enabled(self, process_enabled: bool, reset_enabled: bool) -> None:
        ...

    # Show persistent archive summary line
    def set_archive_summary(self, text: str) -> None:
        ...

    # Show discovery indicator
    def set_discovery_badge(self, enabled: bool) -> None:
        ...

    # Optional: show contextual source options (folder-aware)
    def show_source_context_options(self, *, discovery_enabled: bool = False, folder: str | None = None) -> None:
        ...
