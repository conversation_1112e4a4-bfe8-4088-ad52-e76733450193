"""
RecentFoldersService

Lightweight MRU store for recent Source/Archive folders persisted to user config.
- File location: ~/.flatmate/config/recent_folders.json
- Format (versioned): {"version": 1, "source": [paths], "archive": [paths]}

Design goals:
- Minimal, explicit, readable (no speculative features)
- No external dependencies (JSON over YAML for stdlib only)
- Atomic writes, path normalisation, MRU ordering, bounded size, prune missing
"""
from __future__ import annotations

import json
import os
from dataclasses import dataclass, field
from pathlib import Path
from typing import Dict, List

from .logger import log


CONFIG_DIR = Path.home() / ".flatmate" / "config"
CONFIG_FILE = CONFIG_DIR / "recent_folders.json"
MAX_ITEMS = 10


def _norm_path(p: str) -> str:
    try:
        # Resolve to absolute, normalise case on Windows
        rp = Path(p).expanduser().resolve()
        np = os.path.normcase(str(rp)) if os.name == "nt" else str(rp)
        return np
    except Exception:
        return p


def _dir_name(p: str) -> str:
    try:
        return Path(p).name or str(p)
    except Exception:
        return str(p)


@dataclass
class _Store:
    version: int = 1
    source: List[str] = field(default_factory=list)
    archive: List[str] = field(default_factory=list)

    def to_dict(self) -> Dict:
        return {"version": self.version, "source": self.source, "archive": self.archive}

    @classmethod
    def from_dict(cls, d: Dict) -> "_Store":
        return cls(version=int(d.get("version", 1)),
                   source=list(d.get("source", []) or []),
                   archive=list(d.get("archive", []) or []))


class RecentFoldersService:
    """MRU recent folders for Source and Archive options."""

    def __init__(self, config_file: Path | None = None):
        self._file = Path(config_file) if config_file else CONFIG_FILE
        self._data = _Store()
        self._ensure_dirs()
        self.load()

    def _ensure_dirs(self) -> None:
        try:
            self._file.parent.mkdir(parents=True, exist_ok=True)
        except Exception as e:
            log.error(f"[RecentFolders] Failed ensuring config dir: {e}")

    def load(self) -> None:
        """Load store and prune missing paths."""
        try:
            if self._file.exists():
                with self._file.open("r", encoding="utf-8") as f:
                    raw = json.load(f)
                self._data = _Store.from_dict(raw)
            else:
                self._data = _Store()
            self._prune_missing()
        except Exception as e:
            log.error(f"[RecentFolders] Failed loading store: {e}")
            self._data = _Store()

    def save(self) -> None:
        """Write atomically to avoid corruption."""
        try:
            tmp = self._file.with_suffix(self._file.suffix + ".tmp")
            with tmp.open("w", encoding="utf-8") as f:
                json.dump(self._data.to_dict(), f, indent=2)
            os.replace(tmp, self._file)
        except Exception as e:
            log.error(f"[RecentFolders] Failed saving store: {e}")

    # Public API
    def get_sources(self) -> List[str]:
        return self._data.source.copy()

    def get_archives(self) -> List[str]:
        return self._data.archive.copy()

    def get_source_display(self) -> List[str]:
        return [_dir_name(p) for p in self._data.source]

    def get_archive_display(self) -> List[str]:
        return [_dir_name(p) for p in self._data.archive]

    def add_source(self, path: str) -> None:
        self._add("source", path)

    def add_archive(self, path: str) -> None:
        self._add("archive", path)

    # Internals
    def _add(self, key: str, path: str) -> None:
        try:
            norm = _norm_path(path)
            if not norm:
                return
            lst = getattr(self._data, key)
            # remove if exists, then insert at front
            lst = [p for p in lst if _norm_path(p) != norm]
            lst.insert(0, norm)
            # bound size
            setattr(self._data, key, lst[:MAX_ITEMS])
            self.save()
            log.debug(f"[RecentFolders] Added to {key}: {norm}")
        except Exception as e:
            log.error(f"[RecentFolders] Failed adding to {key}: {e}")

    def _prune_missing(self) -> None:
        try:
            def _exists(p: str) -> bool:
                try:
                    return Path(p).exists()
                except Exception:
                    return False

            self._data.source = [p for p in self._data.source if _exists(p)]
            self._data.archive = [p for p in self._data.archive if _exists(p)]
        except Exception as e:
            log.error(f"[RecentFolders] Failed pruning: {e}")
