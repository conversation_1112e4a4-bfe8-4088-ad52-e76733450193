import os
import pytest

# Phase-1 contract guard tests for Update Data UI
# - Distinct channels: SOURCE_SELECT_REQUESTED vs add_files_requested
# - Dialog policy: managers emit dialog-request events; View renders dialogs
# - Canonical rendering path: file list via FILE_LIST_UPDATED

from fm.modules.update_data.services.local_event_bus import update_data_local_bus, ViewEvents
from fm.modules.update_data._ui.ui_events import DialogRequestEvent, FileListUpdatedEvent


@pytest.fixture(autouse=True)
def clear_bus_log():
    # ensure clean log per test
    update_data_local_bus.clear_event_log()
    yield
    update_data_local_bus.clear_event_log()


def test_distinct_channels_exist_and_emit():
    # Arrange
    observed = []

    def handler(data):
        observed.append(data)

    update_data_local_bus.subscribe(ViewEvents.SOURCE_SELECT_REQUESTED.value, handler)
    update_data_local_bus.subscribe("add_files_requested", handler)

    # Act
    update_data_local_bus.emit(ViewEvents.SOURCE_SELECT_REQUESTED.value, "SELECT_FILES")
    update_data_local_bus.emit("add_files_requested", None)

    # Assert
    # Two emissions captured total, distinct event types
    log = update_data_local_bus.get_event_log(limit=10)
    types = [e["type"] for e in log]
    assert ViewEvents.SOURCE_SELECT_REQUESTED.value in types, "SOURCE_SELECT_REQUESTED not emitted"
    assert "add_files_requested" in types, "add_files_requested not emitted"
    assert types.count(ViewEvents.SOURCE_SELECT_REQUESTED.value) == 1
    assert types.count("add_files_requested") == 1
    assert len(observed) == 2


def test_dialog_request_event_structure_error():
    # Arrange
    captured = {}
    def on_error(evt):
        captured["evt"] = evt

    update_data_local_bus.subscribe(ViewEvents.ERROR_DIALOG_REQUESTED.value, on_error)

    # Act
    update_data_local_bus.emit(
        ViewEvents.ERROR_DIALOG_REQUESTED.value,
        DialogRequestEvent(dialog_type="error", title="X", extra_data={"message": "boom"})
    )

    # Assert
    assert "evt" in captured, "No dialog event captured"
    evt = captured["evt"]
    # accept dataclass-like
    assert getattr(evt, "dialog_type", None) == "error"
    assert getattr(evt, "title", "") == "X"
    extra = getattr(evt, "extra_data", {}) or {}
    assert extra.get("message") == "boom"


def test_file_list_updated_drives_render_path_only():
    # This test ensures the canonical FILE_LIST_UPDATED event is emitted and available.
    # Rendering is handled by the View; here we verify event presence and payload shape.
    captured = {}
    def on_list(evt):
        captured["evt"] = evt

    update_data_local_bus.subscribe(ViewEvents.FILE_LIST_UPDATED.value, on_list)

    # Act
    update_data_local_bus.emit(
        ViewEvents.FILE_LIST_UPDATED.value,
        FileListUpdatedEvent(files=["/tmp/a.csv", "/tmp/b.csv"], source_path="/tmp")
    )

    # Assert
    assert "evt" in captured, "No FILE_LIST_UPDATED event captured"
    evt = captured["evt"]
    files = getattr(evt, "files", None)
    src = getattr(evt, "source_path", None)
    assert isinstance(files, list) and len(files) == 2
    assert src == "/tmp"