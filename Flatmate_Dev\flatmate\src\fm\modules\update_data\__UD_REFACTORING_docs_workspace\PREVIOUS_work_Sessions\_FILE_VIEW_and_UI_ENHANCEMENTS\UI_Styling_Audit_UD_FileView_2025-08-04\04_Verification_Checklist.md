# 04 — Verification Checklist (Low-Risk Validation)

Objective
Verify that minimal styling changes achieve intended results without regressions. Keep scope to UD File View and themed widgets.

Preconditions
- Build/run the app normally (no special flags)
- Navigate to Update Data → File View pane
- Load a few files across different folders to populate the tree

A. Visual Checks (UD File View)
1) Toolbar Frame
- Expect a subtle border and radius around the button row
- Selector: QFrame#TableViewToolbar in [theme.qss](flatmate/src/fm/gui/styles/theme.qss:110)

2) Buttons
- Add button shows primary styling; Remove button shows secondary styling
- Selector: QPushButton[type=primary|secondary]
- Hover/pressed states are visible and readable

3) File Tree
- Alternating row colors visible
- Header sections styled with theme colors (background, border between sections)
- Selected item highlight legible
- Selector group: #file_tree, #file_tree::item, #file_tree::item:selected, #file_tree QHeaderView::section

B. Interaction Checks
- Column resize behaves; no jitter when switching selection
- Context menu on header toggles optional columns (<PERSON><PERSON>, <PERSON>) with expected show/hide behavior
- Selection changes update button enabled state as expected (Remove active when selected)

C. Regression Sweep (Scoped)
- Inspect other modules quickly to ensure no unexpected toolbar borders appear (we scoped with QFrame#TableViewToolbar; no other modules should use this name)
- Ensure no global color shifts occurred (we modified only theme.qss under the toolbar scope)

D. Logging/Diagnostics
- Review console logs for UD File View operations; no errors related to styling or missing object names
- Confirm add_files_requested intent still emitted on button click and dialogs show via FileManager

E. Rollback Plan (if something looks off)
- Revert ud_file_view.py QFrame → QWidget and objectName back
- Re-comment added toolbar button blocks in theme.qss
- Since selectors are scoped, rollback is safe and local

Sign-off Criteria
- All A/B checks pass
- No C regressions observed
- D logging clear (no styling-related errors)
- Document results in SESSION_LOG.md and update 05_Traceability.md