# Flatmate GUI Styles

Purpose: Document the consolidated QSS pipeline, supported tokens, optional YAML-driven color theming, provenance logging, and auditing utilities.

Status: Theme experiment disabled by default (ENABLE_THEME_EXPERIMENT=False). Use this README to enable and verify themes safely.

-------------------------------------------------------------------------------

1) Architecture Overview

- Single authoritative stylesheet file:
  - Preferred filename: base_theme.qss
  - Temporary migration fallback: flatmate_consolidated.qss (code prefers base_theme.qss if present)
- Modules
  - loader.py
    - Loads the consolidated stylesheet from disk
    - Replaces {{FONT_SIZE}} based on ConfigKeys.App.BASE_FONT_SIZE with fallback 14
    - Optionally applies YAML hex_map-based color substitution (exact hex literals)
    - Emits provenance logs for file selection, replacement counts, and theme mapping totals/zero-hits
  - applier.py
    - Public API: apply_styles(app)
    - Sets the resulting stylesheet on QApplication
    - Emits concise provenance logs for font size source (config or default) and the applied stylesheet path
    - Writes audit artifacts (Qt applied QSS, differences, delayed widget color snapshot)

Imports in application entry point:
from fm.gui.styles.applier import apply_styles  # called early in initialization

-------------------------------------------------------------------------------

2) Supported Tokens

- {{FONT_SIZE}}
  - Source: ConfigKeys.App.BASE_FONT_SIZE
  - Fallback: 14
  - Scope: Whole-document string replacement performed in loader.py
  - Provenance: Loader logs the number of {{FONT_SIZE}} replacements and the effective base size; Applier logs the base size and its source for clarity

Notes
- QSS does not support native variables like {{...}} or CSS var(). All token substitutions are application-side string replacements.

-------------------------------------------------------------------------------

3) YAML Theme Experiment (Disabled by Default)

- Default state: ENABLE_THEME_EXPERIMENT=False in loader.py
- Mechanism: A YAML theme file provides hex_map for deterministic, exact-color substitutions
  - The keys in hex_map must be hex strings appearing in the QSS (without leading '#')
  - The values must be hex colors starting with '#'
- Provided example: themes/theme-light.yaml

Enabling the theme experiment
1. Open loader.py and set:
   ENABLE_THEME_EXPERIMENT = True
2. Optionally change:
   DEFAULT_THEME_NAME = "light"  # or "dark" once created
3. Ensure the hex_map keys in the YAML match actual hex literals used in base_theme.qss

Example minimal schema (YAML)
theme: light
version: 1
palette:
  primary: "#2E7D32"
  panel_bg: "#FFFFFF"
  text_primary: "#000000"
hex_map:
  "1E1E1E": "#FFFFFF"
  "FFFFFF": "#000000"
  "3B8A45": "#2E7D32"

Behavior and guardrails
- Case-sensitive exact replacement with leading '#'
- Replacement counts are logged; zero-hit keys are listed to highlight mismatches
- No widget code changes are required for color theming

-------------------------------------------------------------------------------

4) Provenance Logging

Loader (loader.py)
- Logs stylesheet selection (base_theme.qss or fallback)
- Logs BASE_FONT_SIZE value and replacement count for {{FONT_SIZE}}
- When theming is enabled:
  - Logs total number of color replacements
  - Logs zero-hit keys to catch mismatches between YAML and QSS

Applier (applier.py)
- Logs BASE_FONT_SIZE and its source (config or default 14)
- Logs which consolidated stylesheet path was ultimately applied

-------------------------------------------------------------------------------

5) Auditing Utilities

When AUDIT_STYLESHEET is enabled:
- debug_combined_output.qss
  - The loader output before Qt application
- debug_qt_applied.qss
  - What Qt actually applied to the application
- debug_differences.txt
  - Quick side-by-side metadata and first 1000 chars for loader vs Qt-applied
- debug_actual_colors.txt
  - Delayed snapshot of selected widgets' palette colors (best-effort inspection)

Usage
- Useful during refactors or theme development to verify behavior
- Disable AUDIT_STYLESHEET for normal operation

-------------------------------------------------------------------------------

6) Migration Notes and Roadmap

- Current preference: base_theme.qss; code falls back to flatmate_consolidated.qss
- Future task: Physically rename flatmate_consolidated.qss → base_theme.qss and remove fallback logic
- Palette hygiene:
  - Sweep the consolidated QSS to remove stray/off-palette values
  - Keep a small, deliberate set of palette colors to simplify theming
- Themes:
  - Add theme-dark.yaml to mirror the current dark palette as an explicit baseline
  - Keep ENABLE_THEME_EXPERIMENT=False by default; document enable steps as above

-------------------------------------------------------------------------------

7) Quick Verification Checklist

- [ ] BASE_FONT_SIZE logs show value and source in the applier
- [ ] Loader logs show selected stylesheet path and replacement counts
- [ ] If theme experiment enabled:
  - [ ] Total replacements logged are non-zero when expected
  - [ ] Zero-hit keys list is empty, or investigated and fixed
- [ ] No un-replaced {{FONT_SIZE}} remains in the applied QSS
- [ ] Visual inspection matches expectations; audit artifacts confirm consistency