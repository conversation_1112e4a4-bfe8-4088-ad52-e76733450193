# Guide Pane – Short Overview

A concise overview of the Update Data guide pane and how it fits into the MVP architecture.

- __Purpose__
  - Provide clear, contextual instructions and status during Update Data flows.
  - Show a persistent archive summary (e.g., "Archive: Same as Source" or a folder path).
  - Offer contextual options such as "Monitor Folder" and "Auto‑Queue New Files".

- __Key Components__
  - `GuidePaneWidget` renders messages, archive summary label, and contextual options.
  - `UpdateDataView` exposes presenter‑facing methods and wires widget signals to the local bus.
  - `IUpdateDataView` defines the clean interface used by the presenter (no Qt coupling).

- __Presenter → View API__
  - `set_guide_archive_summary(text: str)`
  - `show_guide_source_options(monitor_enabled: bool = False, auto_queue_enabled: bool = False)`
  - Status/messages via existing `display`/state helpers (encapsulated in the view/widget).

- __Signals (View → Local Bus)__
  - `FOLDER_MONITORING_TOGGLED` (wired from `GuidePaneWidget`)
  - Optional: `AUTO_QUEUE_TOGGLED` (only if defined in `ViewEvents`)

- __UX Notes__
  - Archive summary is shown as a dedicated line (does not overwrite main message).
  - Contextual options appear when relevant to source selection/monitoring flows.

- __Further Details__
  - See the architecture document for structure, contracts, and testing checklist:
    - `src/fm/modules/update_data/__UD_REFACTORING_docs_workspace/RECENT_FOLDERS_and_FILE_VIEW_refinements/guide_pane/architecture.md`
