import pytest

from fm.modules.update_data.services.local_event_bus import update_data_local_bus, ViewEvents
from fm.modules.update_data._ui.ui_events import DialogRequestEvent, FileListUpdatedEvent


@pytest.fixture(autouse=True)
def _clean_bus():
    update_data_local_bus.clear_event_log()
    yield
    update_data_local_bus.clear_event_log()


def test_error_dialog_requested_on_explicit_emit_and_structure():
    """
    Contract: ERROR dialog requests are standardized via DialogRequestEvent.
    This asserts the event can be emitted and has the expected minimal fields.
    """
    captured = {}

    def on_err(evt):
        captured["evt"] = evt

    update_data_local_bus.subscribe(ViewEvents.ERROR_DIALOG_REQUESTED.value, on_err)

    # Emit standardized error
    update_data_local_bus.emit(
        ViewEvents.ERROR_DIALOG_REQUESTED.value,
        DialogRequestEvent(
            dialog_type="error",
            title="Selection Error",
            extra_data={"message": "No supported files found in the selected folder."},
        ),
    )

    assert "evt" in captured, "ERROR_DIALOG_REQUESTED not captured"
    evt = captured["evt"]
    assert getattr(evt, "dialog_type", None) == "error"
    assert getattr(evt, "title", "") == "Selection Error"
    extra = getattr(evt, "extra_data", {}) or {}
    assert extra.get("message") == "No supported files found in the selected folder."


def test_success_dialog_requested_minimal_shape():
    """
    Contract: SUCCESS dialog requests follow the same dataclass pattern.
    """
    captured = {}

    def on_ok(evt):
        captured["evt"] = evt

    update_data_local_bus.subscribe(ViewEvents.SUCCESS_DIALOG_REQUESTED.value, on_ok)

    update_data_local_bus.emit(
        ViewEvents.SUCCESS_DIALOG_REQUESTED.value,
        DialogRequestEvent(
            dialog_type="success",
            title="Operation Complete",
            extra_data={"message": "Files processed successfully."},
        ),
    )

    assert "evt" in captured, "SUCCESS_DIALOG_REQUESTED not captured"
    evt = captured["evt"]
    assert getattr(evt, "dialog_type", None) == "success"
    assert getattr(evt, "title", "") == "Operation Complete"
    extra = getattr(evt, "extra_data", {}) or {}
    assert extra.get("message") == "Files processed successfully."


def test_channel_separation_add_files_vs_left_panel_source():
    """
    Contract: File Pane add_files_requested must not collide with SOURCE_SELECT_REQUESTED.
    We assert both event types appear distinctly in the bus log.
    """
    update_data_local_bus.emit(ViewEvents.SOURCE_SELECT_REQUESTED.value, "SELECT_FILES")
    update_data_local_bus.emit("add_files_requested", None)

    log = update_data_local_bus.get_event_log(limit=10)
    seen = [e["type"] for e in log]
    assert ViewEvents.SOURCE_SELECT_REQUESTED.value in seen, "SOURCE_SELECT_REQUESTED missing"
    assert "add_files_requested" in seen, "add_files_requested missing"


def test_file_list_updated_payload_shape_is_minimal_valid():
    """
    Contract: FileListUpdatedEvent must provide 'files' list and optional 'source_path' string.
    """
    captured = {}
    update_data_local_bus.subscribe(ViewEvents.FILE_LIST_UPDATED.value, lambda e: captured.setdefault("evt", e))

    update_data_local_bus.emit(
        ViewEvents.FILE_LIST_UPDATED.value,
        FileListUpdatedEvent(files=["/tmp/a.csv", "/tmp/b.csv"], source_path="/tmp"),
    )

    evt = captured.get("evt")
    assert evt is not None, "FILE_LIST_UPDATED not captured"
    assert isinstance(getattr(evt, "files", None), list) and len(evt.files) == 2
    assert getattr(evt, "source_path", None) == "/tmp"


def test_view_dialog_policy_notes():
    """
    Documentation-aligned note:
    - Logical place for actual QMessageBox rendering is in the View (UpdateDataView),
      via handlers subscribed to *_DIALOG_REQUESTED events.
    - This test does NOT trigger real dialogs; it only validates the event contracts that drive them.
    """
    # This is a placeholder to document policy within tests; no assertions required.
    assert True