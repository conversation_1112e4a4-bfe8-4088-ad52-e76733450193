# Directory Discovery MVP — Decisions Log

Date: 2025-08-09
Owner: Architect (<PERSON>)

## Summary
MVP enables zero-config file discovery for the current source folder, rooted in per-folder toggle control and root-only scanning. Presenter orchestrates; no event bus in this flow.

## Key Decisions
- **Per-folder toggle text**: "Enable file discovery for this folder" (in `guide_pane.py`).
- **Scope**: Single current source folder only.
- **What counts as "new"**: Files present in the root of the source folder at scan time; no DB/history checks in MVP.
- **Scan depth**: Non-recursive (root-only).
- **Extensions**: Filter to supported types (centralised constant; case-insensitive).
- **Archive destination**: Source from left panel setting ("Same as Source"). No change to archiving behaviour in MVP.
- **Presenter vs Events**: Direct presenter→service→view calls. No events for this flow.
- **Service IO policy**: `DirectoryInfoService` remains in-memory; no hidden IO. Scanning is a separate utility.
- **Error handling**: Fail loud/early (e.g., `ValueError` when enabling without `archive_dest` for new folder). Log via custom logger.

## Flow (Enable)
1) Toggle ON in Guide Pane → Presenter resolves current folder + archive dest.
2) `DirectoryInfoService.enable_discovery(path, archive_dest)`.
3) `scan_root_files(path, include_exts)`.
4) Update view file list (prefer v2 API): `view.set_files([...])`.
5) `DirectoryInfoService.mark_scanned_now(path)`.

## Flow (Disable)
- `DirectoryInfoService.disable_discovery(path)`; no auto-refresh.

## Out of Scope (MVP)
- Folder monitoring service.
- Persistence for directory info (load/save).
- Multi-folder support.

## Open Questions
- Supported extensions definitive list and location (to be centralised in `fm/core/directory/utils.py`).
- Dual-pane coexistence: choose correct file list API (legacy vs v2) — add a guard in presenter.
