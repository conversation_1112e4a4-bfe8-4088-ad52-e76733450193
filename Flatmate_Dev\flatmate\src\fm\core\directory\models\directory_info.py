from __future__ import annotations

from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import Optional


@dataclass(frozen=True)
class DirectoryInfo:
    """Represents a source directory and its behaviours.

    Fields:
    - path: Absolute path to the directory (normalised).
    - role: Optional semantic role (e.g., 'source', 'archive', 'inbox').
    - discovery_enabled: Whether file discovery (watch + scan) is enabled for this directory.
    - archive_dest: Destination directory for archiving processed files (absolute path).
    - last_scan: When the last discovery scan was performed.
    """

    path: Path
    role: Optional[str]
    discovery_enabled: bool
    archive_dest: Path
    last_scan: Optional[datetime] = None

    def normalised(self) -> "DirectoryInfo":
        """Return a copy with normalised absolute paths."""
        p = self.path.resolve()
        a = self.archive_dest.resolve()
        return DirectoryInfo(
            path=p,
            role=self.role,
            discovery_enabled=self.discovery_enabled,
            archive_dest=a,
            last_scan=self.last_scan,
        )
