# Scoping: Recent Folders, File Info, and Guide Pane Monitoring

Status: Draft for discussion (concise, actionable)
Date: 2025-08-08

---

## 1) Scope (what this covers)
- File list/file tree behaviour in Update Data module
- Display of file info (Bank | Variant | Type) + Created
- Recent folders → Source options, Archive options (persist + prune)
- Guide pane prompt to monitor newly encountered folders (per-folder)
- Minimal contracts between presenter ↔ view and services

Out of scope (for now): full MVVM, global state frameworks, cross-module refactors.

---

## 2) Systems involved
- View/widgets
  - `ud_file_view.py` (smart widget container)
  - `file_tree.py` (presentational table)
  - Left panel option groups: Source, Archive (option menus)
  - Guide pane (messaging + actions)
- Presenter
  - `state_coordinator.py` (or equivalent coordinator for wiring)
- Services
  - `file_info_service.py` (hydrates Bank/Variant/Type + Created)
  - QuickAccessStore (JSON persistence for recent source/archive)
  - FolderMonitor (per-folder monitor with debounce)
- Events / Store (tiny, local)
  - FileListStore (single source of truth for current file list + info cache)
  - `FileListUpdated` event: op = {set|merge|remove}

---

## 3) Minimal contracts (clean, testable)
- Events
  - `FileListUpdated(paths: list[str], op: Literal['set','merge','remove'])`
  - `FolderMonitoringToggled(dir_path: str, enabled: bool)`
  - `FolderObserved(dir_path: str, files: list[str])` (monitor found new files)

- FileListStore (no framework)
  - `set_list(paths: list[str])`
  - `merge_in(paths: list[str])`
  - `remove(paths: list[str])`
  - `clear()`
  - `get_list() -> list[str]`
  - `get_info(path: str) -> FileInfo | None`
  - `subscribe(callback)` — notifies on list or info changes
  - Behaviour: on changes, delta-hydrate via FileInfoService; update internal info_cache; notify.

- FileInfoService (clarified)
  - `get_info(path: str) -> FileInfo`
  - `hydrate_many(paths: list[str]) -> dict[str, FileInfo]`
  - Contract: returns `bank`, `variant?`, `filetype`, `created`; unknown handler → show extension (e.g. ".csv").
  - Performance: simple LRU/TTL cache; only hydrate deltas.

- QuickAccessStore
  - `load() -> dict[str, list[str]]` (e.g., {"source": [...], "archive": [...]})
  - `save(data)`
  - `prune_missing()` — drop non-existent paths on startup
  - `add_source(dir_path)`, `add_archive(dir_path)` — ensure most-recent-first, unique

- FolderMonitor
  - `toggle(dir_path: str, enabled: bool)`
  - Debounce (250–500ms) and batch `FolderObserved(dir, files)` when new files arrive

- View interface (MVP; presenter-facing methods only)
  - File tree
    - `set_files(paths: list[str])`
    - `update_file_info(info_map: dict[str, FileInfo])`
    - Columns: Selected | File Info | Created (rename "Status" → "File Info")
  - Option menus
    - `set_source_options(options: list[str], selected: str | None)`
    - `set_archive_options(options: list[str], selected: str | None)`
  - Guide pane
    - `show_monitor_prompt(dir_path: str)` — CTA: "Monitor this folder for new files?"
    - `hide_monitor_prompt()`

---

## 4) Simple flows (end-to-end)
- Selecting files/folder in left panel
  1) Presenter computes resulting file paths; emits `FileListUpdated(paths, op='set')`.
  2) Store sets list, delta-hydrates info, notifies subscribers.
  3) View updates: `set_files()` then `update_file_info()`.
  4) Presenter updates QuickAccessStore and option menus (`set_source_options(..., selected=dir_name)`).

- Folder monitoring prompt (guide pane)
  1) When user selects files from a folder not seen before, presenter calls `show_monitor_prompt(dir)`.
  2) If user accepts: emit `FolderMonitoringToggled(dir, True)`; FolderMonitor starts; persist setting if needed.
  3) When new files arrive: monitor emits `FolderObserved(dir, files)`; presenter forwards as `FileListUpdated(files, op='merge')`.

- Archive selection
  1) Selecting an archive folder updates Archive options via QuickAccessStore and `set_archive_options(..., selected=dir_name)`.

---

## 5) Chunks to implement (small, low risk)
A) Rename column + info rendering
- Rename "Status" → "File Info"; render "Bank | Variant | Type" or fallback extension; show Created from info.
- Wire `update_file_info()` to read from store info_cache.

B) Tiny FileListStore + event
- Implement FileListStore and `FileListUpdated` event.
- Make `file_tree.py` subscribe (presenter wires it) and stop direct UI mutation from producers.

C) Quick access options
- Implement QuickAccessStore (JSON), prune on startup, update options on selection, set selected text to folder name.

D) Guide pane monitor prompt
- Add `show_monitor_prompt(dir)`; per-folder toggle; 250–500ms debounce in FolderMonitor.

---

## 6) Decisions & risks
- Keep presenter free of Qt; view implements interface.
- Prefer interface methods for direct UI updates; reserve events for multi-listener/async (store updates, folder monitor).
- Risk: partial rewiring may cause temporary desync — mitigate with the store as single truth and smoke test.

---

## 7) Minimal smoke test (manual or automated)
- Select a source folder → file list updates; info hydrates; File Info/Created columns render.
- Guide pane shows prompt for new folder; accept → monitor toggled.
- New files dropped into folder → list merges in; info appears after debounce.
- Source/Archive option menus reflect recent folders and selected labels correctly; missing paths pruned on restart.
