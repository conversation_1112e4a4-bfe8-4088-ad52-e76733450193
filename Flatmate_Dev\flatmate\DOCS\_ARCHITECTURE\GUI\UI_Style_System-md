# UI Style System Guide

Purpose: Define how we design, style, build, refactor, and elevate widgets in Flatmate, aligning the QSS styling system with Python widget architecture and a maturity model for promotion to shared components.

Scope
- Current state (how it works today)
- Target state (how it should work moving forward)
- Standards and workflows across layers:
  - QSS primitives (consolidated stylesheet, tokens, palette discipline)
  - Python base widgets (shared contracts)
  - Composite widgets (composition patterns and boundaries)
  - Module-level implementations (local customization)
- Maturity gates and elevation criteria (module → shared component)
- Migration and refactoring guidance

-------------------------------------------------------------------------------

1) Current State

Styling pipeline
- Single consolidated stylesheet preferred: base_theme.qss (fallback to flatmate_consolidated.qss during migration)
- Application-side substitutions:
  - {{FONT_SIZE}} replaced in memory via loader using ConfigKeys.App.BASE_FONT_SIZE with fallback 14
  - QSS does not support native variables; all substitutions happen in Python
- Optional theme experiment:
  - YAML-driven color-only mapping via exact hex replacement (disabled by default)
  - themes/theme-light.yaml exists as scaffold

Modules and responsibilities
- loader.py
  - Loads consolidated stylesheet
  - Replaces {{FONT_SIZE}} based on config
  - Optionally applies YAML hex_map mapping; logs counts and zero-hit keys
- applier.py
  - Public API: apply_styles(app)
  - Applies stylesheet to QApplication
  - Emits concise provenance logs (BASE_FONT_SIZE source, chosen stylesheet)
  - Provides auditing utilities: Qt-applied dump, diffs, delayed widget color snapshot #>> (questioonable fucmitionality - could be imporved)
- main.py
  - Explicitly imports and calls apply_styles(app) during initialization (no hidden behavior in __init__)

Examples in codebase
- Mature: table_view_v2 (shared component family with structured subcomponents)
- In progress: Update Data module widgets (folder dialog, file_view); not yet ready for elevation

-------------------------------------------------------------------------------

2) Target State

Design goals
- Deterministic, DRY styling with a single source of truth
- Clear widget layering: primitives → base widgets → composites → module-level implementations
- Palette discipline: all colors drawn from a known set; avoid stray/off-palette values
- Observability: provenance logs and audit artifacts to catch regressions early
- Elevation-ready: consistent maturity gates to promote stable widgets to shared components

Standards
- QSS file of record: base_theme.qss
  - Remove the fallback file once the rename is completed across code and docs
- Substitutions are minimal and logged
  - {{FONT_SIZE}} only (no over-tokenization)
  - Optional hex_map for colors via YAML themes
- Theme experiment default: disabled; enable via loader flag for controlled trials
- Avoid per-widget one-off rules in QSS; prefer class- or type-based selectors and objectName scoping only as necessary
- Keep module-specific styling in the consolidated file where possible; local overrides only if tightly scoped and documented

-------------------------------------------------------------------------------

3) Layered Architecture and Workflows

Layer A: QSS primitives (base_theme.qss)
- Responsibilities
  - Define global widget baselines and variants (e.g., QPushButton[type="action_btn"], [type="nav_btn"], [type="select_btn"], [type="exit_btn"])
  - Define panel, surface, typography scale, scrollbar, inputs, item views
  - Use only approved palette hex values; avoid introducing ad hoc colors
- Guidelines
  - Prefer type+attribute selectors for variants over deeply nested selectors
  - Keep selectors shallow; ensure specificity does not require !important
  - Keep sizes and spacing consistent across families (buttons, inputs, lists)
  - Use objectName for module-specific hooks sparingly (e.g., #file_view) and document the contract
- Checks
  - No dangling {{FONT_SIZE}} after replacement
  - All colors found in an approved palette list
  - Audit artifacts reviewed for parity when changes are made

Layer B: Python base widgets (shared contracts)
- Responsibilities
  - Provide a standard baseline widget interface and common behavior
  - Attach stable objectName and type attributes (via setProperty) if styling requires it
  - Centralize repeated behavior: focus outlines, disabled states, hover/press logic, size policies
- Guidelines
  - Avoid styling in Python; rely on QSS as the source of truth
  - Provide helper methods to set properties that influence QSS variants (e.g., setProperty("type", "action_btn"))
  - Keep init responsibilities clear: assign objectName, ensure accessible names, wire signals minimally
- Checks
  - Constructor sets objectName and variant properties
  - No inline palette fiddling unless absolutely necessary
  - Documentation string references the QSS variant used

Layer C: Composite widgets (composition-first)
- Responsibilities
  - Combine base widgets into a higher-level component (e.g., table_view, toolbar, search panel, file_view, select_option_group (composed of label, option menu select button))
  - Manage layout, slots/signals orchestration; no invasive styling - view connects signals to interface methods and events.
- Guidelines
  - Favor composition over inheritance for behavior assembly
  - Keep objectName assignments clear at container boundaries for QSS scoping if required
  - Do not re-implement base behavior; import and compose base widgets
- Checks
  - Composition diagram or short comment block showing children and responsibilities
  - Public API includes only the intended surface (avoid leaking internals)
  - Tests cover composite behavior scenarios and states

Layer D: Module-level implementations (local usage)
- Responsibilities
  - Use composites and base widgets to build module screens (e.g., Update Data: folder dialog, file_view)
  - Apply minimal module-level configurations (data models, labels, text)
- Guidelines
  - Avoid ad hoc styling; if a visual requirement appears unique, consider proposing an extension to the shared QSS or a reusable variant
  - Keep module-specific objectName usage tight and documented
- Checks
  - Module code does not introduce color or style constants
  - Module code references the shared base/composite API rather than raw primitives unless justified

-------------------------------------------------------------------------------

4) Maturity Model and Elevation Gates

Levels
- L0 Prototype (module-local): Untested or partially tested; not shared
- L1 Candidate (module-local, reusable intent): Tests in place for behavior; consistent use of base widgets; no ad hoc styling
- L2 Shared Component (shared library): API stabilized; documented; used by at least one module; QSS variants and objectName conventions documented
- L3 Mature Shared Component: Used in multiple modules; performance checks; UX review complete; theming stability verified via audits

Elevation checklist (module → shared)
- [ ] Behavior tests cover primary flows and error states
- [ ] No module-specific styling hacks; uses standardized QSS variants
- [ ] ObjectName and type properties follow naming conventions
- [ ] Public API documented and stable
- [ ] No stray palette values; all colors are from the approved set
- [ ] Audit artifacts reviewed after integration (debug_qt_applied.qss, diffs, colors)
- [ ] At least one consumer integrated and validated
- [ ] Performance/profile spot-checks done (if applicable to list/table heavy components)

-------------------------------------------------------------------------------

5) Naming and Conventions

Object names and properties
- objectName: module_or_component + concise role (e.g., update_data_file_view, folder_dialog_panel)
- type property for variants: action_btn, nav_btn, select_btn, exit_btn
- Avoid names that expose implementation details; focus on role/intent

Directory structure
- Shared base widgets: flatmate/src/fm/gui/_shared_components/base/
- Shared composites: flatmate/src/fm/gui/_shared_components/panes/ or relevant domain directories
- Module implementations: flatmate/src/fm/gui/_main_window_components/... or module-specific folders
- Docs for shared components: alongside in docs/ or under DOCS/_ARCHITECTURE/_GUI

-------------------------------------------------------------------------------

6) Palette and Theming Discipline

Palette policy
- Approved palette list maintained in docs (and optionally a YAML palette reference)
- Consolidated QSS must use only approved palette hex values
- Introduce new colors via a palette proposal PR, not ad hoc in QSS

Theme experiment (opt-in)
- ENABLE_THEME_EXPERIMENT=False by default
- To trial a theme:
  - Enable flag in loader.py
  - Provide themes/theme-*.yaml with exact hex_map (keys must match QSS literal hexes)
  - Review log counts and zero-hit keys
  - Perform visual spot-check with audit artifacts

-------------------------------------------------------------------------------

7) Refactoring Guidance

When refactoring a widget
- Step 1: Identify its layer (base, composite, module) and intended maturity level
- Step 2: Extract inline styling to QSS variants; use setProperty("type", ...) where necessary
- Step 3: Ensure objectName and type properties align with guidelines
- Step 4: Add or update tests for behavior (not visuals)
- Step 5: Run style audits:
  - debug_combined_output.qss
  - debug_qt_applied.qss
  - debug_differences.txt
  - debug_actual_colors.txt
- Step 6: Review performance if the widget renders large lists/tables
- Step 7: If candidate for sharing, complete elevation checklist and propose as L2 Shared

-------------------------------------------------------------------------------

8) Migration and Roadmap

Immediate actions
- Physically rename flatmate_consolidated.qss → base_theme.qss and remove fallback logic once references are updated
- Sweep consolidated QSS for off-palette values and normalize
- Add theme-dark.yaml mirroring current dark palette for a documented baseline

Short-term
- Write a brief checklist UI_Widget_Review_Checklist.md (optional companion)
- Document naming examples for objectName and type properties
- Capture a small gallery of before/after screenshots for key shared components (table_view_v2, buttons, panels) for visual QA

Medium-term
- Introduce a minimal semantic token layer for the highest-value colors only (optional), gated by audit counts (low priority )
- Add pre-commit/CI checks for stray hexes and for unresolved {{FONT_SIZE}} tokens

-------------------------------------------------------------------------------

9) Examples

QSS variant snippet (buttons)
QPushButton[type="action_btn"] {
  background: #3B8A45;
  color: #FFFFFF;
  border: none;
  padding: 8px 16px;
  height: 45px;
}
QPushButton[type="action_btn"]:hover {
  background: #2F6E38;
}

Python usage of variant
btn = QPushButton("Run")
btn.setProperty("type", "action_btn")
btn.setObjectName("update_data_run_button")

Composite pattern
class FileViewHeader(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("file_view_header")
        # compose pre-styled buttons, labels, inputs from base widgets

-------------------------------------------------------------------------------

10) References

- Styling architecture (current): [BROWNFIELD_Styling_System_Architecture.md](../STYLING_SYSTEM/BROWNFIELD_Styling_System_Architecture.md)
- DRY principles: [DRY_Principles_for_QSS_Theming.md](../STYLING_SYSTEM/DRY_Principles_for_QSS_Theming.md)
- Variables investigation: [VARIABLES_Usage_Investigation.md](../STYLING_SYSTEM/VARIABLES_Usage_Investigation.md)
- Loader implementation: [loader.py](../../src/fm/gui/styles/loader.py)
- Applier implementation: [applier.py](../../src/fm/gui/styles/applier.py)
- Theme scaffold: [theme-light.yaml](../../src/fm/gui/styles/themes/theme-light.yaml)

-------------------------------------------------------------------------------

Appendix: Maturity Gate Quick-Check

- L0 Prototype: local only, incomplete tests, visually unstable
- L1 Candidate: tests in place, uses base widgets and QSS variants, no ad hoc styling
- L2 Shared: API stable, documented, palette-compliant, elevation checklist complete
- L3 Mature: multi-module adoption, performance reviewed, UX reviewed, audits clean