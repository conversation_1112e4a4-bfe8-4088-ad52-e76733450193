"""
Configuration for file view behaviour and appearance.

Contains configuration classes and default settings for the file view component.
"""

from dataclasses import dataclass, field
from typing import List


@dataclass
class FileConfig:
    """Configuration for file view behaviour and appearance."""
    
    # Display options
    show_file_icons: bool = True
    show_file_size: bool = True
    show_file_type: bool = True
    show_modified_date: bool = True
    
    # Grouping and sorting
    group_by_folder: bool = True
    sort_by: str = "name"  # "name", "size", "type", "date"
    sort_order: str = "asc"  # "asc", "desc"
    
    # User interaction
    allow_add: bool = True
    allow_remove: bool = True
    allow_context_menu: bool = True
    allow_drag_drop: bool = True
    
    # File filtering
    allowed_file_types: List[str] = field(
        default_factory=lambda: ["*.csv", "*.xlsx", "*.xls"]
    )
    
    # UI appearance
    table_alternating_row_colours: bool = True
    table_selection_behaviour: str = "rows"  # "rows", "items"
    table_header_visible: bool = True
    
    # Performance
    max_files_display: int = 1000
    auto_refresh_enabled: bool = False
    
    @classmethod
    def default(cls) -> 'FileConfig':
        """Get default configuration."""
        return cls()
    
    @classmethod
    def csv_only(cls) -> 'FileConfig':
        """Get configuration for CSV files only."""
        config = cls.default()
        config.allowed_file_types = ["*.csv"]
        return config
    
    def is_file_type_allowed(self, file_path: str) -> bool:
        """Check if a file type is allowed based on the configuration."""
        from pathlib import Path
        import fnmatch
        
        file_extension = Path(file_path).suffix.lower()
        
        for pattern in self.allowed_file_types:
            # Convert glob pattern to match file extension
            if pattern.startswith("*."):
                allowed_ext = pattern[1:].lower()  # Remove "*" prefix
                if file_extension == allowed_ext:
                    return True
            elif fnmatch.fnmatch(file_path.lower(), pattern.lower()):
                return True
        
        return False
