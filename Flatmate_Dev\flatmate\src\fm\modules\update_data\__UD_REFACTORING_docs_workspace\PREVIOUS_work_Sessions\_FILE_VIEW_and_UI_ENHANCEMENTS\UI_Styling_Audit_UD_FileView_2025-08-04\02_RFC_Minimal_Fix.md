# 02 — RFC: Minimal, Low-Risk Styling Remediation for UD File View

Status: Draft  
Owner: Architect (<PERSON>)  
Scope: UD File View styling alignment with theme.qss (no app-wide refactor)

Problem Statement
The UD File View styling appears inconsistent due to:
1) Dual stylesheet sources (style.qss vs theme.qss) without a documented precedence/loading order.
2) Mismatch between intended theme selectors and actual widget naming/classes (e.g., toolbar container).
3) Commented-out theme rules for toolbar affordances, and lack of an explicit Styling Contract in PRD.

Non-Goals
- No design overhaul of the entire app or module.
- No immediate removal of style.qss.
- No change to event/MVP wiring.

Decision
Adopt theme.qss as the primary styling source for UD File View and align widget hooks to it. Keep modifications surgical, reversible, and scoped to UD File View + theme hooks. Document a Styling Contract and a stylesheet precedence rule.

Proposed Changes (Minimal Surface)

A. UD File View: Align toolbar container to theme.qss hook
- Convert the button bar container from QWidget#FileTreeButtonBar to QFrame#TableViewToolbar to match theme selector [theme.qss](flatmate/src/fm/gui/styles/theme.qss:110).
- Keep Add/Remove buttons with properties type="primary"/"secondary".

B. theme.qss: Reinstate minimal toolbar affordances
- Un-comment or reintroduce basic toolbar button styling within QFrame#TableViewToolbar scope to ensure visible affordances (border, hover).
- Keep it subtle to avoid conflicts.

C. Styling Contract and Precedence Policy (Documentation)
- Add a “Styling Contract” section to the PRD and the Update-Data-UI-Deep-Dive specifying:
  - file tree objectName: #file_tree (already in place)
  - toolbar frame: QFrame#TableViewToolbar
  - buttons: QPushButton[type="primary"|"secondary"]
- Define stylesheet loading policy: theme.qss must be applied after style.qss OR ensure theme.qss has higher specificity for UD components. Document the rule and avoid inline setStyleSheet in code.

Rationale
- Aligns with existing theme.qss variable-driven approach and component scoping.
- Restores consistent, predictable UI without broad rewrites.
- Keeps legacy style.qss intact while preventing it from unintentionally overriding UD File View.

Risks and Mitigations
- Risk: theme.qss rules still get overridden elsewhere.
  - Mitigation: Scope selectors under #file_tree and QFrame#TableViewToolbar; verify load order; avoid global selectors in this change.
- Risk: Visual changes too subtle.
  - Mitigation: Verification checklist (04) includes concrete visuals to confirm affordances.

Acceptance Criteria
- UD File View toolbar shows consistent, themed affordances (subtle border/background).
- Primary/secondary buttons style correctly via type property.
- File tree rows and headers match theme.qss (#file_tree selectors).
- No visual regressions elsewhere (scoped selectors).
- PRD includes Styling Contract and verification items.

Appendix: Short-Term Policy
- Do not remove style.qss now; document that theme.qss is the source of truth for new/refactored modules. Future sessions can progressively migrate.